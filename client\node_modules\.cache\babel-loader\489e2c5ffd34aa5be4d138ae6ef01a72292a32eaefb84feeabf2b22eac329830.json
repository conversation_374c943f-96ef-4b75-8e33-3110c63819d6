{"ast": null, "code": "import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectChartLayout } from '../context/chartLayoutContext';\nimport { useAppDispatch, useAppSelector } from './hooks';\nimport { addLegendPayload, removeLegendPayload } from './legendSlice';\nvar noop = () => {};\nexport function SetLegendPayload(_ref) {\n  var {\n    legendPayload\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, isPanorama, legendPayload]);\n  return null;\n}\nexport function SetPolarLegendPayload(_ref2) {\n  var {\n    legendPayload\n  } = _ref2;\n  var dispatch = useAppDispatch();\n  var layout = useAppSelector(selectChartLayout);\n  useEffect(() => {\n    if (layout !== 'centric' && layout !== 'radial') {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, layout, legendPayload]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "useIsPanorama", "selectChartLayout", "useAppDispatch", "useAppSelector", "addLegendPayload", "removeLegendPayload", "noop", "SetLegendPayload", "_ref", "legendPayload", "dispatch", "isPanorama", "SetPolarLegendPayload", "_ref2", "layout"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/SetLegendPayload.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectChartLayout } from '../context/chartLayoutContext';\nimport { useAppDispatch, useAppSelector } from './hooks';\nimport { addLegendPayload, removeLegendPayload } from './legendSlice';\nvar noop = () => {};\nexport function SetLegendPayload(_ref) {\n  var {\n    legendPayload\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, isPanorama, legendPayload]);\n  return null;\n}\nexport function SetPolarLegendPayload(_ref2) {\n  var {\n    legendPayload\n  } = _ref2;\n  var dispatch = useAppDispatch();\n  var layout = useAppSelector(selectChartLayout);\n  useEffect(() => {\n    if (layout !== 'centric' && layout !== 'radial') {\n      return noop;\n    }\n    dispatch(addLegendPayload(legendPayload));\n    return () => {\n      dispatch(removeLegendPayload(legendPayload));\n    };\n  }, [dispatch, layout, legendPayload]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,EAAEC,cAAc,QAAQ,SAAS;AACxD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,eAAe;AACrE,IAAIC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnB,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,IAAIE,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/B,IAAIS,UAAU,GAAGX,aAAa,CAAC,CAAC;EAChCD,SAAS,CAAC,MAAM;IACd,IAAIY,UAAU,EAAE;MACd,OAAOL,IAAI;IACb;IACAI,QAAQ,CAACN,gBAAgB,CAACK,aAAa,CAAC,CAAC;IACzC,OAAO,MAAM;MACXC,QAAQ,CAACL,mBAAmB,CAACI,aAAa,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAEC,UAAU,EAAEF,aAAa,CAAC,CAAC;EACzC,OAAO,IAAI;AACb;AACA,OAAO,SAASG,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,IAAI;IACFJ;EACF,CAAC,GAAGI,KAAK;EACT,IAAIH,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/B,IAAIY,MAAM,GAAGX,cAAc,CAACF,iBAAiB,CAAC;EAC9CF,SAAS,CAAC,MAAM;IACd,IAAIe,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,QAAQ,EAAE;MAC/C,OAAOR,IAAI;IACb;IACAI,QAAQ,CAACN,gBAAgB,CAACK,aAAa,CAAC,CAAC;IACzC,OAAO,MAAM;MACXC,QAAQ,CAACL,mBAAmB,CAACI,aAAa,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAEI,MAAM,EAAEL,aAAa,CAAC,CAAC;EACrC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
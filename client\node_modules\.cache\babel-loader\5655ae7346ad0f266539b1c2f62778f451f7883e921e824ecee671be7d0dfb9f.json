{"ast": null, "code": "function once(func) {\n  let called = false;\n  let cache;\n  return function (...args) {\n    if (!called) {\n      called = true;\n      cache = func(...args);\n    }\n    return cache;\n  };\n}\nexport { once };", "map": {"version": 3, "names": ["once", "func", "called", "cache", "args"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/once.mjs"], "sourcesContent": ["function once(func) {\n    let called = false;\n    let cache;\n    return function (...args) {\n        if (!called) {\n            called = true;\n            cache = func(...args);\n        }\n        return cache;\n    };\n}\n\nexport { once };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAChB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,KAAK;EACT,OAAO,UAAU,GAAGC,IAAI,EAAE;IACtB,IAAI,CAACF,MAAM,EAAE;MACTA,MAAM,GAAG,IAAI;MACbC,KAAK,GAAGF,IAAI,CAAC,GAAGG,IAAI,CAAC;IACzB;IACA,OAAOD,KAAK;EAChB,CAAC;AACL;AAEA,SAASH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
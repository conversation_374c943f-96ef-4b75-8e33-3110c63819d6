{"ast": null, "code": "import { debounce } from './debounce.mjs';\nfunction throttle(func, throttleMs, {\n  signal,\n  edges = ['leading', 'trailing']\n} = {}) {\n  let pendingAt = null;\n  const debounced = debounce(func, throttleMs, {\n    signal,\n    edges\n  });\n  const throttled = function (...args) {\n    if (pendingAt == null) {\n      pendingAt = Date.now();\n    } else {\n      if (Date.now() - pendingAt >= throttleMs) {\n        pendingAt = Date.now();\n        debounced.cancel();\n      }\n    }\n    debounced(...args);\n  };\n  throttled.cancel = debounced.cancel;\n  throttled.flush = debounced.flush;\n  return throttled;\n}\nexport { throttle };", "map": {"version": 3, "names": ["debounce", "throttle", "func", "throttleMs", "signal", "edges", "pendingAt", "debounced", "throttled", "args", "Date", "now", "cancel", "flush"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/throttle.mjs"], "sourcesContent": ["import { debounce } from './debounce.mjs';\n\nfunction throttle(func, throttleMs, { signal, edges = ['leading', 'trailing'] } = {}) {\n    let pendingAt = null;\n    const debounced = debounce(func, throttleMs, { signal, edges });\n    const throttled = function (...args) {\n        if (pendingAt == null) {\n            pendingAt = Date.now();\n        }\n        else {\n            if (Date.now() - pendingAt >= throttleMs) {\n                pendingAt = Date.now();\n                debounced.cancel();\n            }\n        }\n        debounced(...args);\n    };\n    throttled.cancel = debounced.cancel;\n    throttled.flush = debounced.flush;\n    return throttled;\n}\n\nexport { throttle };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,EAAE;EAAEC,MAAM;EAAEC,KAAK,GAAG,CAAC,SAAS,EAAE,UAAU;AAAE,CAAC,GAAG,CAAC,CAAC,EAAE;EAClF,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,SAAS,GAAGP,QAAQ,CAACE,IAAI,EAAEC,UAAU,EAAE;IAAEC,MAAM;IAAEC;EAAM,CAAC,CAAC;EAC/D,MAAMG,SAAS,GAAG,SAAAA,CAAU,GAAGC,IAAI,EAAE;IACjC,IAAIH,SAAS,IAAI,IAAI,EAAE;MACnBA,SAAS,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAID,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,SAAS,IAAIH,UAAU,EAAE;QACtCG,SAAS,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;QACtBJ,SAAS,CAACK,MAAM,CAAC,CAAC;MACtB;IACJ;IACAL,SAAS,CAAC,GAAGE,IAAI,CAAC;EACtB,CAAC;EACDD,SAAS,CAACI,MAAM,GAAGL,SAAS,CAACK,MAAM;EACnCJ,SAAS,CAACK,KAAK,GAAGN,SAAS,CAACM,KAAK;EACjC,OAAOL,SAAS;AACpB;AAEA,SAASP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
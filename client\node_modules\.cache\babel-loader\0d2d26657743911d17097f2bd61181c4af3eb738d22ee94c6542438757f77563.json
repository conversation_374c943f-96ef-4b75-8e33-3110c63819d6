{"ast": null, "code": "function isPlainObject(value) {\n  if (!value || typeof value !== 'object') {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  const hasObjectPrototype = proto === null || proto === Object.prototype || Object.getPrototypeOf(proto) === null;\n  if (!hasObjectPrototype) {\n    return false;\n  }\n  return Object.prototype.toString.call(value) === '[object Object]';\n}\nexport { isPlainObject };", "map": {"version": 3, "names": ["isPlainObject", "value", "proto", "Object", "getPrototypeOf", "hasObjectPrototype", "prototype", "toString", "call"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isPlainObject.mjs"], "sourcesContent": ["function isPlainObject(value) {\n    if (!value || typeof value !== 'object') {\n        return false;\n    }\n    const proto = Object.getPrototypeOf(value);\n    const hasObjectPrototype = proto === null ||\n        proto === Object.prototype ||\n        Object.getPrototypeOf(proto) === null;\n    if (!hasObjectPrototype) {\n        return false;\n    }\n    return Object.prototype.toString.call(value) === '[object Object]';\n}\n\nexport { isPlainObject };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,KAAK,EAAE;EAC1B,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,MAAMC,KAAK,GAAGC,MAAM,CAACC,cAAc,CAACH,KAAK,CAAC;EAC1C,MAAMI,kBAAkB,GAAGH,KAAK,KAAK,IAAI,IACrCA,KAAK,KAAKC,MAAM,CAACG,SAAS,IAC1BH,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC,KAAK,IAAI;EACzC,IAAI,CAACG,kBAAkB,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,OAAOF,MAAM,CAACG,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,iBAAiB;AACtE;AAEA,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
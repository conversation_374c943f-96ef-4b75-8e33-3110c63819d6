{"ast": null, "code": "import { createContext, useContext } from 'react';\nexport var TooltipPortalContext = /*#__PURE__*/createContext(null);\nexport var useTooltipPortal = () => useContext(TooltipPortalContext);", "map": {"version": 3, "names": ["createContext", "useContext", "TooltipPortalContext", "useTooltipPortal"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/context/tooltipPortalContext.js"], "sourcesContent": ["import { createContext, useContext } from 'react';\nexport var TooltipPortalContext = /*#__PURE__*/createContext(null);\nexport var useTooltipPortal = () => useContext(TooltipPortalContext);"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,OAAO,IAAIC,oBAAoB,GAAG,aAAaF,aAAa,CAAC,IAAI,CAAC;AAClE,OAAO,IAAIG,gBAAgB,GAAGA,CAAA,KAAMF,UAAU,CAACC,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
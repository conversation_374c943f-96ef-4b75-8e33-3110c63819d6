{"ast": null, "code": "module.exports = require('../dist/compat/math/sumBy.js').sumBy;", "map": {"version": 3, "names": ["module", "exports", "require", "sumBy"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/compat/sumBy.js"], "sourcesContent": ["module.exports = require('../dist/compat/math/sumBy.js').sumBy;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
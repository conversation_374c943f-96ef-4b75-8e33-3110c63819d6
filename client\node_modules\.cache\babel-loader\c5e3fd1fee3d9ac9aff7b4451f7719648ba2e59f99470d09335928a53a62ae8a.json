{"ast": null, "code": "function reverseString(value) {\n  return [...value].reverse().join('');\n}\nexport { reverseString };", "map": {"version": 3, "names": ["reverseString", "value", "reverse", "join"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/reverseString.mjs"], "sourcesContent": ["function reverseString(value) {\n    return [...value].reverse().join('');\n}\n\nexport { reverseString };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AACxC;AAEA,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export var ACCURACY = 1e-4;\nvar cubicBezierFactor = (c1, c2) => [0, 3 * c1, 3 * c2 - 6 * c1, 3 * c1 - 3 * c2 + 1];\nvar evaluatePolynomial = (params, t) => params.map((param, i) => param * t ** i).reduce((pre, curr) => pre + curr);\nvar cubicBezier = (c1, c2) => t => {\n  var params = cubicBezierFactor(c1, c2);\n  return evaluatePolynomial(params, t);\n};\nvar derivativeCubicBezier = (c1, c2) => t => {\n  var params = cubicBezierFactor(c1, c2);\n  var newParams = [...params.map((param, i) => param * i).slice(1), 0];\n  return evaluatePolynomial(newParams, t);\n};\n// calculate cubic-bezier using <PERSON>'s method\nexport var configBezier = function configBezier() {\n  var x1, x2, y1, y2;\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (args.length === 1) {\n    switch (args[0]) {\n      case 'linear':\n        [x1, y1, x2, y2] = [0.0, 0.0, 1.0, 1.0];\n        break;\n      case 'ease':\n        [x1, y1, x2, y2] = [0.25, 0.1, 0.25, 1.0];\n        break;\n      case 'ease-in':\n        [x1, y1, x2, y2] = [0.42, 0.0, 1.0, 1.0];\n        break;\n      case 'ease-out':\n        [x1, y1, x2, y2] = [0.42, 0.0, 0.58, 1.0];\n        break;\n      case 'ease-in-out':\n        [x1, y1, x2, y2] = [0.0, 0.0, 0.58, 1.0];\n        break;\n      default:\n        {\n          var easing = args[0].split('(');\n          if (easing[0] === 'cubic-bezier' && easing[1].split(')')[0].split(',').length === 4) {\n            [x1, y1, x2, y2] = easing[1].split(')')[0].split(',').map(x => parseFloat(x));\n          }\n        }\n    }\n  } else if (args.length === 4) {\n    [x1, y1, x2, y2] = args;\n  }\n  var curveX = cubicBezier(x1, x2);\n  var curveY = cubicBezier(y1, y2);\n  var derCurveX = derivativeCubicBezier(x1, x2);\n  var rangeValue = value => {\n    if (value > 1) {\n      return 1;\n    }\n    if (value < 0) {\n      return 0;\n    }\n    return value;\n  };\n  var bezier = _t => {\n    var t = _t > 1 ? 1 : _t;\n    var x = t;\n    for (var i = 0; i < 8; ++i) {\n      var evalT = curveX(x) - t;\n      var derVal = derCurveX(x);\n      if (Math.abs(evalT - t) < ACCURACY || derVal < ACCURACY) {\n        return curveY(x);\n      }\n      x = rangeValue(x - evalT / derVal);\n    }\n    return curveY(x);\n  };\n  bezier.isStepper = false;\n  return bezier;\n};\nexport var configSpring = function configSpring() {\n  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var {\n    stiff = 100,\n    damping = 8,\n    dt = 17\n  } = config;\n  var stepper = (currX, destX, currV) => {\n    var FSpring = -(currX - destX) * stiff;\n    var FDamping = currV * damping;\n    var newV = currV + (FSpring - FDamping) * dt / 1000;\n    var newX = currV * dt / 1000 + currX;\n    if (Math.abs(newX - destX) < ACCURACY && Math.abs(newV) < ACCURACY) {\n      return [destX, 0];\n    }\n    return [newX, newV];\n  };\n  stepper.isStepper = true;\n  stepper.dt = dt;\n  return stepper;\n};\nexport var configEasing = easing => {\n  if (typeof easing === 'string') {\n    switch (easing) {\n      case 'ease':\n      case 'ease-in-out':\n      case 'ease-out':\n      case 'ease-in':\n      case 'linear':\n        return configBezier(easing);\n      case 'spring':\n        return configSpring();\n      default:\n        if (easing.split('(')[0] === 'cubic-bezier') {\n          return configBezier(easing);\n        }\n    }\n  }\n  if (typeof easing === 'function') {\n    return easing;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["ACCURACY", "cubicBezierFactor", "c1", "c2", "evaluatePolynomial", "params", "t", "map", "param", "i", "reduce", "pre", "curr", "cubicBezier", "derivativeCubicBezier", "newParams", "slice", "config<PERSON><PERSON><PERSON>", "x1", "x2", "y1", "y2", "_len", "arguments", "length", "args", "Array", "_key", "easing", "split", "x", "parseFloat", "curveX", "curveY", "derCurveX", "rangeValue", "value", "bezier", "_t", "evalT", "<PERSON><PERSON><PERSON>", "Math", "abs", "isStepper", "configS<PERSON>ring", "config", "undefined", "stiff", "damping", "dt", "stepper", "currX", "destX", "currV", "FSpring", "FDamping", "newV", "newX", "configEasing"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/animation/easing.js"], "sourcesContent": ["export var ACCURACY = 1e-4;\nvar cubicBezierFactor = (c1, c2) => [0, 3 * c1, 3 * c2 - 6 * c1, 3 * c1 - 3 * c2 + 1];\nvar evaluatePolynomial = (params, t) => params.map((param, i) => param * t ** i).reduce((pre, curr) => pre + curr);\nvar cubicBezier = (c1, c2) => t => {\n  var params = cubicBezierFactor(c1, c2);\n  return evaluatePolynomial(params, t);\n};\nvar derivativeCubicBezier = (c1, c2) => t => {\n  var params = cubicBezierFactor(c1, c2);\n  var newParams = [...params.map((param, i) => param * i).slice(1), 0];\n  return evaluatePolynomial(newParams, t);\n};\n// calculate cubic-bezier using <PERSON>'s method\nexport var configBezier = function configBezier() {\n  var x1, x2, y1, y2;\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (args.length === 1) {\n    switch (args[0]) {\n      case 'linear':\n        [x1, y1, x2, y2] = [0.0, 0.0, 1.0, 1.0];\n        break;\n      case 'ease':\n        [x1, y1, x2, y2] = [0.25, 0.1, 0.25, 1.0];\n        break;\n      case 'ease-in':\n        [x1, y1, x2, y2] = [0.42, 0.0, 1.0, 1.0];\n        break;\n      case 'ease-out':\n        [x1, y1, x2, y2] = [0.42, 0.0, 0.58, 1.0];\n        break;\n      case 'ease-in-out':\n        [x1, y1, x2, y2] = [0.0, 0.0, 0.58, 1.0];\n        break;\n      default:\n        {\n          var easing = args[0].split('(');\n          if (easing[0] === 'cubic-bezier' && easing[1].split(')')[0].split(',').length === 4) {\n            [x1, y1, x2, y2] = easing[1].split(')')[0].split(',').map(x => parseFloat(x));\n          }\n        }\n    }\n  } else if (args.length === 4) {\n    [x1, y1, x2, y2] = args;\n  }\n  var curveX = cubicBezier(x1, x2);\n  var curveY = cubicBezier(y1, y2);\n  var derCurveX = derivativeCubicBezier(x1, x2);\n  var rangeValue = value => {\n    if (value > 1) {\n      return 1;\n    }\n    if (value < 0) {\n      return 0;\n    }\n    return value;\n  };\n  var bezier = _t => {\n    var t = _t > 1 ? 1 : _t;\n    var x = t;\n    for (var i = 0; i < 8; ++i) {\n      var evalT = curveX(x) - t;\n      var derVal = derCurveX(x);\n      if (Math.abs(evalT - t) < ACCURACY || derVal < ACCURACY) {\n        return curveY(x);\n      }\n      x = rangeValue(x - evalT / derVal);\n    }\n    return curveY(x);\n  };\n  bezier.isStepper = false;\n  return bezier;\n};\nexport var configSpring = function configSpring() {\n  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var {\n    stiff = 100,\n    damping = 8,\n    dt = 17\n  } = config;\n  var stepper = (currX, destX, currV) => {\n    var FSpring = -(currX - destX) * stiff;\n    var FDamping = currV * damping;\n    var newV = currV + (FSpring - FDamping) * dt / 1000;\n    var newX = currV * dt / 1000 + currX;\n    if (Math.abs(newX - destX) < ACCURACY && Math.abs(newV) < ACCURACY) {\n      return [destX, 0];\n    }\n    return [newX, newV];\n  };\n  stepper.isStepper = true;\n  stepper.dt = dt;\n  return stepper;\n};\nexport var configEasing = easing => {\n  if (typeof easing === 'string') {\n    switch (easing) {\n      case 'ease':\n      case 'ease-in-out':\n      case 'ease-out':\n      case 'ease-in':\n      case 'linear':\n        return configBezier(easing);\n      case 'spring':\n        return configSpring();\n      default:\n        if (easing.split('(')[0] === 'cubic-bezier') {\n          return configBezier(easing);\n        }\n    }\n  }\n  if (typeof easing === 'function') {\n    return easing;\n  }\n  return null;\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG,IAAI;AAC1B,IAAIC,iBAAiB,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,GAAGD,EAAE,EAAE,CAAC,GAAGC,EAAE,GAAG,CAAC,GAAGD,EAAE,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAG,CAAC,CAAC;AACrF,IAAIC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,CAAC,KAAKD,MAAM,CAACE,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAKD,KAAK,GAAGF,CAAC,IAAIG,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC;AAClH,IAAIC,WAAW,GAAGA,CAACX,EAAE,EAAEC,EAAE,KAAKG,CAAC,IAAI;EACjC,IAAID,MAAM,GAAGJ,iBAAiB,CAACC,EAAE,EAAEC,EAAE,CAAC;EACtC,OAAOC,kBAAkB,CAACC,MAAM,EAAEC,CAAC,CAAC;AACtC,CAAC;AACD,IAAIQ,qBAAqB,GAAGA,CAACZ,EAAE,EAAEC,EAAE,KAAKG,CAAC,IAAI;EAC3C,IAAID,MAAM,GAAGJ,iBAAiB,CAACC,EAAE,EAAEC,EAAE,CAAC;EACtC,IAAIY,SAAS,GAAG,CAAC,GAAGV,MAAM,CAACE,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAKD,KAAK,GAAGC,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACpE,OAAOZ,kBAAkB,CAACW,SAAS,EAAET,CAAC,CAAC;AACzC,CAAC;AACD;AACA,OAAO,IAAIW,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EAChD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,IAAIF,IAAI,CAACD,MAAM,KAAK,CAAC,EAAE;IACrB,QAAQC,IAAI,CAAC,CAAC,CAAC;MACb,KAAK,QAAQ;QACX,CAACP,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QACvC;MACF,KAAK,MAAM;QACT,CAACH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;QACzC;MACF,KAAK,SAAS;QACZ,CAACH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QACxC;MACF,KAAK,UAAU;QACb,CAACH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;QACzC;MACF,KAAK,aAAa;QAChB,CAACH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;QACxC;MACF;QACE;UACE,IAAIO,MAAM,GAAGH,IAAI,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC;UAC/B,IAAID,MAAM,CAAC,CAAC,CAAC,KAAK,cAAc,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAACL,MAAM,KAAK,CAAC,EAAE;YACnF,CAACN,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAGO,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAACtB,GAAG,CAACuB,CAAC,IAAIC,UAAU,CAACD,CAAC,CAAC,CAAC;UAC/E;QACF;IACJ;EACF,CAAC,MAAM,IAAIL,IAAI,CAACD,MAAM,KAAK,CAAC,EAAE;IAC5B,CAACN,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,GAAGI,IAAI;EACzB;EACA,IAAIO,MAAM,GAAGnB,WAAW,CAACK,EAAE,EAAEC,EAAE,CAAC;EAChC,IAAIc,MAAM,GAAGpB,WAAW,CAACO,EAAE,EAAEC,EAAE,CAAC;EAChC,IAAIa,SAAS,GAAGpB,qBAAqB,CAACI,EAAE,EAAEC,EAAE,CAAC;EAC7C,IAAIgB,UAAU,GAAGC,KAAK,IAAI;IACxB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,CAAC;IACV;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,CAAC;IACV;IACA,OAAOA,KAAK;EACd,CAAC;EACD,IAAIC,MAAM,GAAGC,EAAE,IAAI;IACjB,IAAIhC,CAAC,GAAGgC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGA,EAAE;IACvB,IAAIR,CAAC,GAAGxB,CAAC;IACT,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAI8B,KAAK,GAAGP,MAAM,CAACF,CAAC,CAAC,GAAGxB,CAAC;MACzB,IAAIkC,MAAM,GAAGN,SAAS,CAACJ,CAAC,CAAC;MACzB,IAAIW,IAAI,CAACC,GAAG,CAACH,KAAK,GAAGjC,CAAC,CAAC,GAAGN,QAAQ,IAAIwC,MAAM,GAAGxC,QAAQ,EAAE;QACvD,OAAOiC,MAAM,CAACH,CAAC,CAAC;MAClB;MACAA,CAAC,GAAGK,UAAU,CAACL,CAAC,GAAGS,KAAK,GAAGC,MAAM,CAAC;IACpC;IACA,OAAOP,MAAM,CAACH,CAAC,CAAC;EAClB,CAAC;EACDO,MAAM,CAACM,SAAS,GAAG,KAAK;EACxB,OAAON,MAAM;AACf,CAAC;AACD,OAAO,IAAIO,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EAChD,IAAIC,MAAM,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuB,SAAS,GAAGvB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAI;IACFwB,KAAK,GAAG,GAAG;IACXC,OAAO,GAAG,CAAC;IACXC,EAAE,GAAG;EACP,CAAC,GAAGJ,MAAM;EACV,IAAIK,OAAO,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACrC,IAAIC,OAAO,GAAG,EAAEH,KAAK,GAAGC,KAAK,CAAC,GAAGL,KAAK;IACtC,IAAIQ,QAAQ,GAAGF,KAAK,GAAGL,OAAO;IAC9B,IAAIQ,IAAI,GAAGH,KAAK,GAAG,CAACC,OAAO,GAAGC,QAAQ,IAAIN,EAAE,GAAG,IAAI;IACnD,IAAIQ,IAAI,GAAGJ,KAAK,GAAGJ,EAAE,GAAG,IAAI,GAAGE,KAAK;IACpC,IAAIV,IAAI,CAACC,GAAG,CAACe,IAAI,GAAGL,KAAK,CAAC,GAAGpD,QAAQ,IAAIyC,IAAI,CAACC,GAAG,CAACc,IAAI,CAAC,GAAGxD,QAAQ,EAAE;MAClE,OAAO,CAACoD,KAAK,EAAE,CAAC,CAAC;IACnB;IACA,OAAO,CAACK,IAAI,EAAED,IAAI,CAAC;EACrB,CAAC;EACDN,OAAO,CAACP,SAAS,GAAG,IAAI;EACxBO,OAAO,CAACD,EAAE,GAAGA,EAAE;EACf,OAAOC,OAAO;AAChB,CAAC;AACD,OAAO,IAAIQ,YAAY,GAAG9B,MAAM,IAAI;EAClC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,QAAQA,MAAM;MACZ,KAAK,MAAM;MACX,KAAK,aAAa;MAClB,KAAK,UAAU;MACf,KAAK,SAAS;MACd,KAAK,QAAQ;QACX,OAAOX,YAAY,CAACW,MAAM,CAAC;MAC7B,KAAK,QAAQ;QACX,OAAOgB,YAAY,CAAC,CAAC;MACvB;QACE,IAAIhB,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,cAAc,EAAE;UAC3C,OAAOZ,YAAY,CAACW,MAAM,CAAC;QAC7B;IACJ;EACF;EACA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM;EACf;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
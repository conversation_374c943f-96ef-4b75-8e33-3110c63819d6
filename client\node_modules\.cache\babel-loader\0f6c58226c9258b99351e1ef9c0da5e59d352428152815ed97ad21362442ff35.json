{"ast": null, "code": "import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setKeyboardInteraction } from './tooltipSlice';\nimport { selectTooltipAxisTicks, selectTooltipDisplayedData } from './selectors/tooltipSelectors';\nimport { selectCoordinateForDefaultIndex } from './selectors/selectors';\nimport { selectChartDirection } from './selectors/axisSelectors';\nimport { combineActiveTooltipIndex } from './selectors/combiners/combineActiveTooltipIndex';\nexport var keyDownAction = createAction('keyDown');\nexport var focusAction = createAction('focus');\nexport var keyboardEventsMiddleware = createListenerMiddleware();\nkeyboardEventsMiddleware.startListening({\n  actionCreator: keyDownAction,\n  effect: (action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    var key = action.payload;\n    if (key !== 'ArrowRight' && key !== 'ArrowLeft' && key !== 'Enter') {\n      return;\n    }\n\n    // TODO this is lacking index for charts that do not support numeric indexes\n    var currentIndex = Number(combineActiveTooltipIndex(keyboardInteraction, selectTooltipDisplayedData(state)));\n    var tooltipTicks = selectTooltipAxisTicks(state);\n    if (key === 'Enter') {\n      var _coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(keyboardInteraction.index));\n      listenerApi.dispatch(setKeyboardInteraction({\n        active: !keyboardInteraction.active,\n        activeIndex: keyboardInteraction.index,\n        activeDataKey: keyboardInteraction.dataKey,\n        activeCoordinate: _coordinate\n      }));\n      return;\n    }\n    var direction = selectChartDirection(state);\n    var directionMultiplier = direction === 'left-to-right' ? 1 : -1;\n    var movement = key === 'ArrowRight' ? 1 : -1;\n    var nextIndex = currentIndex + movement * directionMultiplier;\n    if (tooltipTicks == null || nextIndex >= tooltipTicks.length || nextIndex < 0) {\n      return;\n    }\n    var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n    listenerApi.dispatch(setKeyboardInteraction({\n      active: true,\n      activeIndex: nextIndex.toString(),\n      activeDataKey: undefined,\n      activeCoordinate: coordinate\n    }));\n  }\n});\nkeyboardEventsMiddleware.startListening({\n  actionCreator: focusAction,\n  effect: (_action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    if (keyboardInteraction.active) {\n      return;\n    }\n    if (keyboardInteraction.index == null) {\n      var nextIndex = '0';\n      var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n      listenerApi.dispatch(setKeyboardInteraction({\n        activeDataKey: undefined,\n        active: true,\n        activeIndex: nextIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});", "map": {"version": 3, "names": ["createAction", "createListenerMiddleware", "setKeyboardInteraction", "selectTooltipAxisTicks", "selectTooltipDisplayedData", "selectCoordinateForDefaultIndex", "selectChartDirection", "combineActiveTooltipIndex", "keyDownAction", "focusAction", "keyboardEventsMiddleware", "startListening", "actionCreator", "effect", "action", "listenerApi", "state", "getState", "accessibilityLayerIsActive", "rootProps", "accessibilityLayer", "keyboardInteraction", "tooltip", "key", "payload", "currentIndex", "Number", "tooltipTicks", "_coordinate", "String", "index", "dispatch", "active", "activeIndex", "activeDataKey", "dataKey", "activeCoordinate", "direction", "directionMultiplier", "movement", "nextIndex", "length", "coordinate", "toString", "undefined", "_action"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/keyboardEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setKeyboardInteraction } from './tooltipSlice';\nimport { selectTooltipAxisTicks, selectTooltipDisplayedData } from './selectors/tooltipSelectors';\nimport { selectCoordinateForDefaultIndex } from './selectors/selectors';\nimport { selectChartDirection } from './selectors/axisSelectors';\nimport { combineActiveTooltipIndex } from './selectors/combiners/combineActiveTooltipIndex';\nexport var keyDownAction = createAction('keyDown');\nexport var focusAction = createAction('focus');\nexport var keyboardEventsMiddleware = createListenerMiddleware();\nkeyboardEventsMiddleware.startListening({\n  actionCreator: keyDownAction,\n  effect: (action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    var key = action.payload;\n    if (key !== 'ArrowRight' && key !== 'ArrowLeft' && key !== 'Enter') {\n      return;\n    }\n\n    // TODO this is lacking index for charts that do not support numeric indexes\n    var currentIndex = Number(combineActiveTooltipIndex(keyboardInteraction, selectTooltipDisplayedData(state)));\n    var tooltipTicks = selectTooltipAxisTicks(state);\n    if (key === 'Enter') {\n      var _coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(keyboardInteraction.index));\n      listenerApi.dispatch(setKeyboardInteraction({\n        active: !keyboardInteraction.active,\n        activeIndex: keyboardInteraction.index,\n        activeDataKey: keyboardInteraction.dataKey,\n        activeCoordinate: _coordinate\n      }));\n      return;\n    }\n    var direction = selectChartDirection(state);\n    var directionMultiplier = direction === 'left-to-right' ? 1 : -1;\n    var movement = key === 'ArrowRight' ? 1 : -1;\n    var nextIndex = currentIndex + movement * directionMultiplier;\n    if (tooltipTicks == null || nextIndex >= tooltipTicks.length || nextIndex < 0) {\n      return;\n    }\n    var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n    listenerApi.dispatch(setKeyboardInteraction({\n      active: true,\n      activeIndex: nextIndex.toString(),\n      activeDataKey: undefined,\n      activeCoordinate: coordinate\n    }));\n  }\n});\nkeyboardEventsMiddleware.startListening({\n  actionCreator: focusAction,\n  effect: (_action, listenerApi) => {\n    var state = listenerApi.getState();\n    var accessibilityLayerIsActive = state.rootProps.accessibilityLayer !== false;\n    if (!accessibilityLayerIsActive) {\n      return;\n    }\n    var {\n      keyboardInteraction\n    } = state.tooltip;\n    if (keyboardInteraction.active) {\n      return;\n    }\n    if (keyboardInteraction.index == null) {\n      var nextIndex = '0';\n      var coordinate = selectCoordinateForDefaultIndex(state, 'axis', 'hover', String(nextIndex));\n      listenerApi.dispatch(setKeyboardInteraction({\n        activeDataKey: undefined,\n        active: true,\n        activeIndex: nextIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});"], "mappings": "AAAA,SAASA,YAAY,EAAEC,wBAAwB,QAAQ,kBAAkB;AACzE,SAASC,sBAAsB,QAAQ,gBAAgB;AACvD,SAASC,sBAAsB,EAAEC,0BAA0B,QAAQ,8BAA8B;AACjG,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,OAAO,IAAIC,aAAa,GAAGR,YAAY,CAAC,SAAS,CAAC;AAClD,OAAO,IAAIS,WAAW,GAAGT,YAAY,CAAC,OAAO,CAAC;AAC9C,OAAO,IAAIU,wBAAwB,GAAGT,wBAAwB,CAAC,CAAC;AAChES,wBAAwB,CAACC,cAAc,CAAC;EACtCC,aAAa,EAAEJ,aAAa;EAC5BK,MAAM,EAAEA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC/B,IAAIC,KAAK,GAAGD,WAAW,CAACE,QAAQ,CAAC,CAAC;IAClC,IAAIC,0BAA0B,GAAGF,KAAK,CAACG,SAAS,CAACC,kBAAkB,KAAK,KAAK;IAC7E,IAAI,CAACF,0BAA0B,EAAE;MAC/B;IACF;IACA,IAAI;MACFG;IACF,CAAC,GAAGL,KAAK,CAACM,OAAO;IACjB,IAAIC,GAAG,GAAGT,MAAM,CAACU,OAAO;IACxB,IAAID,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,OAAO,EAAE;MAClE;IACF;;IAEA;IACA,IAAIE,YAAY,GAAGC,MAAM,CAACnB,yBAAyB,CAACc,mBAAmB,EAAEjB,0BAA0B,CAACY,KAAK,CAAC,CAAC,CAAC;IAC5G,IAAIW,YAAY,GAAGxB,sBAAsB,CAACa,KAAK,CAAC;IAChD,IAAIO,GAAG,KAAK,OAAO,EAAE;MACnB,IAAIK,WAAW,GAAGvB,+BAA+B,CAACW,KAAK,EAAE,MAAM,EAAE,OAAO,EAAEa,MAAM,CAACR,mBAAmB,CAACS,KAAK,CAAC,CAAC;MAC5Gf,WAAW,CAACgB,QAAQ,CAAC7B,sBAAsB,CAAC;QAC1C8B,MAAM,EAAE,CAACX,mBAAmB,CAACW,MAAM;QACnCC,WAAW,EAAEZ,mBAAmB,CAACS,KAAK;QACtCI,aAAa,EAAEb,mBAAmB,CAACc,OAAO;QAC1CC,gBAAgB,EAAER;MACpB,CAAC,CAAC,CAAC;MACH;IACF;IACA,IAAIS,SAAS,GAAG/B,oBAAoB,CAACU,KAAK,CAAC;IAC3C,IAAIsB,mBAAmB,GAAGD,SAAS,KAAK,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;IAChE,IAAIE,QAAQ,GAAGhB,GAAG,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAIiB,SAAS,GAAGf,YAAY,GAAGc,QAAQ,GAAGD,mBAAmB;IAC7D,IAAIX,YAAY,IAAI,IAAI,IAAIa,SAAS,IAAIb,YAAY,CAACc,MAAM,IAAID,SAAS,GAAG,CAAC,EAAE;MAC7E;IACF;IACA,IAAIE,UAAU,GAAGrC,+BAA+B,CAACW,KAAK,EAAE,MAAM,EAAE,OAAO,EAAEa,MAAM,CAACW,SAAS,CAAC,CAAC;IAC3FzB,WAAW,CAACgB,QAAQ,CAAC7B,sBAAsB,CAAC;MAC1C8B,MAAM,EAAE,IAAI;MACZC,WAAW,EAAEO,SAAS,CAACG,QAAQ,CAAC,CAAC;MACjCT,aAAa,EAAEU,SAAS;MACxBR,gBAAgB,EAAEM;IACpB,CAAC,CAAC,CAAC;EACL;AACF,CAAC,CAAC;AACFhC,wBAAwB,CAACC,cAAc,CAAC;EACtCC,aAAa,EAAEH,WAAW;EAC1BI,MAAM,EAAEA,CAACgC,OAAO,EAAE9B,WAAW,KAAK;IAChC,IAAIC,KAAK,GAAGD,WAAW,CAACE,QAAQ,CAAC,CAAC;IAClC,IAAIC,0BAA0B,GAAGF,KAAK,CAACG,SAAS,CAACC,kBAAkB,KAAK,KAAK;IAC7E,IAAI,CAACF,0BAA0B,EAAE;MAC/B;IACF;IACA,IAAI;MACFG;IACF,CAAC,GAAGL,KAAK,CAACM,OAAO;IACjB,IAAID,mBAAmB,CAACW,MAAM,EAAE;MAC9B;IACF;IACA,IAAIX,mBAAmB,CAACS,KAAK,IAAI,IAAI,EAAE;MACrC,IAAIU,SAAS,GAAG,GAAG;MACnB,IAAIE,UAAU,GAAGrC,+BAA+B,CAACW,KAAK,EAAE,MAAM,EAAE,OAAO,EAAEa,MAAM,CAACW,SAAS,CAAC,CAAC;MAC3FzB,WAAW,CAACgB,QAAQ,CAAC7B,sBAAsB,CAAC;QAC1CgC,aAAa,EAAEU,SAAS;QACxBZ,MAAM,EAAE,IAAI;QACZC,WAAW,EAAEO,SAAS;QACtBJ,gBAAgB,EAAEM;MACpB,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
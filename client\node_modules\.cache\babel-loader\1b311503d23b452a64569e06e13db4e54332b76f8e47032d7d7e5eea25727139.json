{"ast": null, "code": "import { useId } from './useId';\n\n/**\n * A hook that generates a unique ID. It uses React.useId() in React 18+ for SSR safety\n * and falls back to a client-side-only unique ID generator for older versions.\n *\n * The ID will stay the same across renders, and you can optionally provide a prefix.\n *\n * @param [prefix] - An optional prefix for the generated ID.\n * @param [customId] - An optional custom ID to override the generated one.\n * @returns The unique ID.\n */\nexport function useUniqueId(prefix, customId) {\n  /*\n   * We have to call this hook here even if we don't use the result because\n   * rules of hooks demand that hooks are never called conditionally.\n   */\n  var generatedId = useId();\n\n  // If a custom ID is provided, it always takes precedence.\n  if (customId) {\n    return customId;\n  }\n\n  // Apply the prefix if one was provided.\n  return prefix ? \"\".concat(prefix, \"-\").concat(generatedId) : generatedId;\n}\n\n/**\n * The useUniqueId hook returns a unique ID that is either reused from external props or generated internally.\n * Either way the ID is now guaranteed to be present so no more nulls or undefined.\n */", "map": {"version": 3, "names": ["useId", "useUniqueId", "prefix", "customId", "generatedId", "concat"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/useUniqueId.js"], "sourcesContent": ["import { useId } from './useId';\n\n/**\n * A hook that generates a unique ID. It uses React.useId() in React 18+ for SSR safety\n * and falls back to a client-side-only unique ID generator for older versions.\n *\n * The ID will stay the same across renders, and you can optionally provide a prefix.\n *\n * @param [prefix] - An optional prefix for the generated ID.\n * @param [customId] - An optional custom ID to override the generated one.\n * @returns The unique ID.\n */\nexport function useUniqueId(prefix, customId) {\n  /*\n   * We have to call this hook here even if we don't use the result because\n   * rules of hooks demand that hooks are never called conditionally.\n   */\n  var generatedId = useId();\n\n  // If a custom ID is provided, it always takes precedence.\n  if (customId) {\n    return customId;\n  }\n\n  // Apply the prefix if one was provided.\n  return prefix ? \"\".concat(prefix, \"-\").concat(generatedId) : generatedId;\n}\n\n/**\n * The useUniqueId hook returns a unique ID that is either reused from external props or generated internally.\n * Either way the ID is now guaranteed to be present so no more nulls or undefined.\n */"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C;AACF;AACA;AACA;EACE,IAAIC,WAAW,GAAGJ,KAAK,CAAC,CAAC;;EAEzB;EACA,IAAIG,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;;EAEA;EACA,OAAOD,MAAM,GAAG,EAAE,CAACG,MAAM,CAACH,MAAM,EAAE,GAAG,CAAC,CAACG,MAAM,CAACD,WAAW,CAAC,GAAGA,WAAW;AAC1E;;AAEA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
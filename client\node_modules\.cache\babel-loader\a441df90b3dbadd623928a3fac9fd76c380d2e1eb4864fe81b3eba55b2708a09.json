{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { Global } from './Global';\nimport { LRUCache } from './LRUCache';\nvar defaultConfig = {\n  cacheSize: 2000,\n  enableCache: true\n};\nvar currentConfig = _objectSpread({}, defaultConfig);\nvar stringCache = new LRUCache(currentConfig.cacheSize);\nvar SPAN_STYLE = {\n  position: 'absolute',\n  top: '-20000px',\n  left: 0,\n  padding: 0,\n  margin: 0,\n  border: 'none',\n  whiteSpace: 'pre'\n};\nvar MEASUREMENT_SPAN_ID = 'recharts_measurement_span';\nfunction createCacheKey(text, style) {\n  // Simple string concatenation for better performance than JSON.stringify\n  var fontSize = style.fontSize || '';\n  var fontFamily = style.fontFamily || '';\n  var fontWeight = style.fontWeight || '';\n  var fontStyle = style.fontStyle || '';\n  var letterSpacing = style.letterSpacing || '';\n  var textTransform = style.textTransform || '';\n  return \"\".concat(text, \"|\").concat(fontSize, \"|\").concat(fontFamily, \"|\").concat(fontWeight, \"|\").concat(fontStyle, \"|\").concat(letterSpacing, \"|\").concat(textTransform);\n}\n\n/**\n * Measure text using DOM (accurate but slower)\n * @param text - The text to measure\n * @param style - CSS style properties to apply\n * @returns The size of the text\n */\nvar measureTextWithDOM = (text, style) => {\n  try {\n    var measurementSpan = document.getElementById(MEASUREMENT_SPAN_ID);\n    if (!measurementSpan) {\n      measurementSpan = document.createElement('span');\n      measurementSpan.setAttribute('id', MEASUREMENT_SPAN_ID);\n      measurementSpan.setAttribute('aria-hidden', 'true');\n      document.body.appendChild(measurementSpan);\n    }\n\n    // Apply styles directly without unnecessary object creation\n    Object.assign(measurementSpan.style, SPAN_STYLE, style);\n    measurementSpan.textContent = \"\".concat(text);\n    var rect = measurementSpan.getBoundingClientRect();\n    return {\n      width: rect.width,\n      height: rect.height\n    };\n  } catch (_unused) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n};\nexport var getStringSize = function getStringSize(text) {\n  var style = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (text === undefined || text === null || Global.isSsr) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n\n  // If caching is disabled, measure directly\n  if (!currentConfig.enableCache) {\n    return measureTextWithDOM(text, style);\n  }\n  var cacheKey = createCacheKey(text, style);\n  var cachedResult = stringCache.get(cacheKey);\n  if (cachedResult) {\n    return cachedResult;\n  }\n\n  // Measure using DOM\n  var result = measureTextWithDOM(text, style);\n\n  // Store in LRU cache\n  stringCache.set(cacheKey, result);\n  return result;\n};\n\n/**\n * Configure text measurement behavior\n * @param config - Partial configuration to apply\n * @returns void\n */\nexport var configureTextMeasurement = config => {\n  var newConfig = _objectSpread(_objectSpread({}, currentConfig), config);\n  if (newConfig.cacheSize !== currentConfig.cacheSize) {\n    stringCache = new LRUCache(newConfig.cacheSize);\n  }\n  currentConfig = newConfig;\n};\n\n/**\n * Get current text measurement configuration\n * @returns Current configuration\n */\nexport var getTextMeasurementConfig = () => _objectSpread({}, currentConfig);\n\n/**\n * Clear the string size cache. Useful for testing or memory management.\n * @returns void\n */\nexport var clearStringCache = () => {\n  stringCache.clear();\n};\n\n/**\n * Get cache statistics for debugging purposes.\n * @returns Cache statistics including size and max size\n */\nexport var getStringCacheStats = () => ({\n  size: stringCache.size(),\n  maxSize: currentConfig.cacheSize\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "Global", "L<PERSON><PERSON><PERSON>", "defaultConfig", "cacheSize", "enableCache", "currentConfig", "stringCache", "SPAN_STYLE", "position", "top", "left", "padding", "margin", "border", "whiteSpace", "MEASUREMENT_SPAN_ID", "createCacheKey", "text", "style", "fontSize", "fontFamily", "fontWeight", "fontStyle", "letterSpacing", "textTransform", "concat", "measureTextWithDOM", "measurementSpan", "document", "getElementById", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "assign", "textContent", "rect", "getBoundingClientRect", "width", "height", "_unused", "getStringSize", "undefined", "isSsr", "cache<PERSON>ey", "cachedResult", "get", "result", "set", "configureTextMeasurement", "config", "newConfig", "getTextMeasurementConfig", "clearStringCache", "clear", "getStringCacheStats", "size", "maxSize"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/DOMUtils.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { Global } from './Global';\nimport { LRUCache } from './LRUCache';\nvar defaultConfig = {\n  cacheSize: 2000,\n  enableCache: true\n};\nvar currentConfig = _objectSpread({}, defaultConfig);\nvar stringCache = new LRUCache(currentConfig.cacheSize);\nvar SPAN_STYLE = {\n  position: 'absolute',\n  top: '-20000px',\n  left: 0,\n  padding: 0,\n  margin: 0,\n  border: 'none',\n  whiteSpace: 'pre'\n};\nvar MEASUREMENT_SPAN_ID = 'recharts_measurement_span';\nfunction createCacheKey(text, style) {\n  // Simple string concatenation for better performance than JSON.stringify\n  var fontSize = style.fontSize || '';\n  var fontFamily = style.fontFamily || '';\n  var fontWeight = style.fontWeight || '';\n  var fontStyle = style.fontStyle || '';\n  var letterSpacing = style.letterSpacing || '';\n  var textTransform = style.textTransform || '';\n  return \"\".concat(text, \"|\").concat(fontSize, \"|\").concat(fontFamily, \"|\").concat(fontWeight, \"|\").concat(fontStyle, \"|\").concat(letterSpacing, \"|\").concat(textTransform);\n}\n\n/**\n * Measure text using DOM (accurate but slower)\n * @param text - The text to measure\n * @param style - CSS style properties to apply\n * @returns The size of the text\n */\nvar measureTextWithDOM = (text, style) => {\n  try {\n    var measurementSpan = document.getElementById(MEASUREMENT_SPAN_ID);\n    if (!measurementSpan) {\n      measurementSpan = document.createElement('span');\n      measurementSpan.setAttribute('id', MEASUREMENT_SPAN_ID);\n      measurementSpan.setAttribute('aria-hidden', 'true');\n      document.body.appendChild(measurementSpan);\n    }\n\n    // Apply styles directly without unnecessary object creation\n    Object.assign(measurementSpan.style, SPAN_STYLE, style);\n    measurementSpan.textContent = \"\".concat(text);\n    var rect = measurementSpan.getBoundingClientRect();\n    return {\n      width: rect.width,\n      height: rect.height\n    };\n  } catch (_unused) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n};\nexport var getStringSize = function getStringSize(text) {\n  var style = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (text === undefined || text === null || Global.isSsr) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n\n  // If caching is disabled, measure directly\n  if (!currentConfig.enableCache) {\n    return measureTextWithDOM(text, style);\n  }\n  var cacheKey = createCacheKey(text, style);\n  var cachedResult = stringCache.get(cacheKey);\n  if (cachedResult) {\n    return cachedResult;\n  }\n\n  // Measure using DOM\n  var result = measureTextWithDOM(text, style);\n\n  // Store in LRU cache\n  stringCache.set(cacheKey, result);\n  return result;\n};\n\n/**\n * Configure text measurement behavior\n * @param config - Partial configuration to apply\n * @returns void\n */\nexport var configureTextMeasurement = config => {\n  var newConfig = _objectSpread(_objectSpread({}, currentConfig), config);\n  if (newConfig.cacheSize !== currentConfig.cacheSize) {\n    stringCache = new LRUCache(newConfig.cacheSize);\n  }\n  currentConfig = newConfig;\n};\n\n/**\n * Get current text measurement configuration\n * @returns Current configuration\n */\nexport var getTextMeasurementConfig = () => _objectSpread({}, currentConfig);\n\n/**\n * Clear the string size cache. Useful for testing or memory management.\n * @returns void\n */\nexport var clearStringCache = () => {\n  stringCache.clear();\n};\n\n/**\n * Get cache statistics for debugging purposes.\n * @returns Cache statistics including size and max size\n */\nexport var getStringCacheStats = () => ({\n  size: stringCache.size(),\n  maxSize: currentConfig.cacheSize\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,IAAIC,aAAa,GAAG;EAClBC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAE;AACf,CAAC;AACD,IAAIC,aAAa,GAAGzB,aAAa,CAAC,CAAC,CAAC,EAAEsB,aAAa,CAAC;AACpD,IAAII,WAAW,GAAG,IAAIL,QAAQ,CAACI,aAAa,CAACF,SAAS,CAAC;AACvD,IAAII,UAAU,GAAG;EACfC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,UAAU;EACfC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,MAAM;EACdC,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,mBAAmB,GAAG,2BAA2B;AACrD,SAASC,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACnC;EACA,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAI,EAAE;EACnC,IAAIC,UAAU,GAAGF,KAAK,CAACE,UAAU,IAAI,EAAE;EACvC,IAAIC,UAAU,GAAGH,KAAK,CAACG,UAAU,IAAI,EAAE;EACvC,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS,IAAI,EAAE;EACrC,IAAIC,aAAa,GAAGL,KAAK,CAACK,aAAa,IAAI,EAAE;EAC7C,IAAIC,aAAa,GAAGN,KAAK,CAACM,aAAa,IAAI,EAAE;EAC7C,OAAO,EAAE,CAACC,MAAM,CAACR,IAAI,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACN,QAAQ,EAAE,GAAG,CAAC,CAACM,MAAM,CAACL,UAAU,EAAE,GAAG,CAAC,CAACK,MAAM,CAACJ,UAAU,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,SAAS,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,aAAa,EAAE,GAAG,CAAC,CAACE,MAAM,CAACD,aAAa,CAAC;AAC3K;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,kBAAkB,GAAGA,CAACT,IAAI,EAAEC,KAAK,KAAK;EACxC,IAAI;IACF,IAAIS,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAACd,mBAAmB,CAAC;IAClE,IAAI,CAACY,eAAe,EAAE;MACpBA,eAAe,GAAGC,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;MAChDH,eAAe,CAACI,YAAY,CAAC,IAAI,EAAEhB,mBAAmB,CAAC;MACvDY,eAAe,CAACI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MACnDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,eAAe,CAAC;IAC5C;;IAEA;IACAxD,MAAM,CAAC+D,MAAM,CAACP,eAAe,CAACT,KAAK,EAAEX,UAAU,EAAEW,KAAK,CAAC;IACvDS,eAAe,CAACQ,WAAW,GAAG,EAAE,CAACV,MAAM,CAACR,IAAI,CAAC;IAC7C,IAAImB,IAAI,GAAGT,eAAe,CAACU,qBAAqB,CAAC,CAAC;IAClD,OAAO;MACLC,KAAK,EAAEF,IAAI,CAACE,KAAK;MACjBC,MAAM,EAAEH,IAAI,CAACG;IACf,CAAC;EACH,CAAC,CAAC,OAAOC,OAAO,EAAE;IAChB,OAAO;MACLF,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;AACF,CAAC;AACD,OAAO,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACxB,IAAI,EAAE;EACtD,IAAIC,KAAK,GAAGrC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK6D,SAAS,GAAG7D,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIoC,IAAI,KAAKyB,SAAS,IAAIzB,IAAI,KAAK,IAAI,IAAIjB,MAAM,CAAC2C,KAAK,EAAE;IACvD,OAAO;MACLL,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;;EAEA;EACA,IAAI,CAAClC,aAAa,CAACD,WAAW,EAAE;IAC9B,OAAOsB,kBAAkB,CAACT,IAAI,EAAEC,KAAK,CAAC;EACxC;EACA,IAAI0B,QAAQ,GAAG5B,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;EAC1C,IAAI2B,YAAY,GAAGvC,WAAW,CAACwC,GAAG,CAACF,QAAQ,CAAC;EAC5C,IAAIC,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;;EAEA;EACA,IAAIE,MAAM,GAAGrB,kBAAkB,CAACT,IAAI,EAAEC,KAAK,CAAC;;EAE5C;EACAZ,WAAW,CAAC0C,GAAG,CAACJ,QAAQ,EAAEG,MAAM,CAAC;EACjC,OAAOA,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,wBAAwB,GAAGC,MAAM,IAAI;EAC9C,IAAIC,SAAS,GAAGvE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,aAAa,CAAC,EAAE6C,MAAM,CAAC;EACvE,IAAIC,SAAS,CAAChD,SAAS,KAAKE,aAAa,CAACF,SAAS,EAAE;IACnDG,WAAW,GAAG,IAAIL,QAAQ,CAACkD,SAAS,CAAChD,SAAS,CAAC;EACjD;EACAE,aAAa,GAAG8C,SAAS;AAC3B,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIC,wBAAwB,GAAGA,CAAA,KAAMxE,aAAa,CAAC,CAAC,CAAC,EAAEyB,aAAa,CAAC;;AAE5E;AACA;AACA;AACA;AACA,OAAO,IAAIgD,gBAAgB,GAAGA,CAAA,KAAM;EAClC/C,WAAW,CAACgD,KAAK,CAAC,CAAC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIC,mBAAmB,GAAGA,CAAA,MAAO;EACtCC,IAAI,EAAElD,WAAW,CAACkD,IAAI,CAAC,CAAC;EACxBC,OAAO,EAAEpD,aAAa,CAACF;AACzB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
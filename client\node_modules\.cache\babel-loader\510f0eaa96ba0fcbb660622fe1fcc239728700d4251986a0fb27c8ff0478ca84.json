{"ast": null, "code": "import { useAppSelector } from '../state/hooks';\nexport var useAccessibilityLayer = () => useAppSelector(state => state.rootProps.accessibilityLayer);", "map": {"version": 3, "names": ["useAppSelector", "useAccessibilityLayer", "state", "rootProps", "accessibilityLayer"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/context/accessibilityContext.js"], "sourcesContent": ["import { useAppSelector } from '../state/hooks';\nexport var useAccessibilityLayer = () => useAppSelector(state => state.rootProps.accessibilityLayer);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,IAAIC,qBAAqB,GAAGA,CAAA,KAAMD,cAAc,CAACE,KAAK,IAAIA,KAAK,CAACC,SAAS,CAACC,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
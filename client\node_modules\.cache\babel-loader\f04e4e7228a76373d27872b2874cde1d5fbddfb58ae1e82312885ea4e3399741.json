{"ast": null, "code": "import { isPlainObject } from '../predicate/isPlainObject.mjs';\nfunction flattenObject(object, {\n  delimiter = '.'\n} = {}) {\n  return flattenObjectImpl(object, '', delimiter);\n}\nfunction flattenObjectImpl(object, prefix = '', delimiter = '.') {\n  const result = {};\n  const keys = Object.keys(object);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = object[key];\n    const prefixedKey = prefix ? `${prefix}${delimiter}${key}` : key;\n    if (isPlainObject(value) && Object.keys(value).length > 0) {\n      Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));\n      continue;\n    }\n    if (Array.isArray(value)) {\n      Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));\n      continue;\n    }\n    result[prefixedKey] = value;\n  }\n  return result;\n}\nexport { flattenObject };", "map": {"version": 3, "names": ["isPlainObject", "flattenObject", "object", "delimiter", "flattenObjectImpl", "prefix", "result", "keys", "Object", "i", "length", "key", "value", "prefixedKey", "assign", "Array", "isArray"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/flattenObject.mjs"], "sourcesContent": ["import { isPlainObject } from '../predicate/isPlainObject.mjs';\n\nfunction flattenObject(object, { delimiter = '.' } = {}) {\n    return flattenObjectImpl(object, '', delimiter);\n}\nfunction flattenObjectImpl(object, prefix = '', delimiter = '.') {\n    const result = {};\n    const keys = Object.keys(object);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = object[key];\n        const prefixedKey = prefix ? `${prefix}${delimiter}${key}` : key;\n        if (isPlainObject(value) && Object.keys(value).length > 0) {\n            Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));\n            continue;\n        }\n        if (Array.isArray(value)) {\n            Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));\n            continue;\n        }\n        result[prefixedKey] = value;\n    }\n    return result;\n}\n\nexport { flattenObject };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gCAAgC;AAE9D,SAASC,aAAaA,CAACC,MAAM,EAAE;EAAEC,SAAS,GAAG;AAAI,CAAC,GAAG,CAAC,CAAC,EAAE;EACrD,OAAOC,iBAAiB,CAACF,MAAM,EAAE,EAAE,EAAEC,SAAS,CAAC;AACnD;AACA,SAASC,iBAAiBA,CAACF,MAAM,EAAEG,MAAM,GAAG,EAAE,EAAEF,SAAS,GAAG,GAAG,EAAE;EAC7D,MAAMG,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACL,MAAM,CAAC;EAChC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,MAAMG,KAAK,GAAGV,MAAM,CAACS,GAAG,CAAC;IACzB,MAAME,WAAW,GAAGR,MAAM,GAAG,GAAGA,MAAM,GAAGF,SAAS,GAAGQ,GAAG,EAAE,GAAGA,GAAG;IAChE,IAAIX,aAAa,CAACY,KAAK,CAAC,IAAIJ,MAAM,CAACD,IAAI,CAACK,KAAK,CAAC,CAACF,MAAM,GAAG,CAAC,EAAE;MACvDF,MAAM,CAACM,MAAM,CAACR,MAAM,EAAEF,iBAAiB,CAACQ,KAAK,EAAEC,WAAW,EAAEV,SAAS,CAAC,CAAC;MACvE;IACJ;IACA,IAAIY,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;MACtBJ,MAAM,CAACM,MAAM,CAACR,MAAM,EAAEF,iBAAiB,CAACQ,KAAK,EAAEC,WAAW,EAAEV,SAAS,CAAC,CAAC;MACvE;IACJ;IACAG,MAAM,CAACO,WAAW,CAAC,GAAGD,KAAK;EAC/B;EACA,OAAON,MAAM;AACjB;AAEA,SAASL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
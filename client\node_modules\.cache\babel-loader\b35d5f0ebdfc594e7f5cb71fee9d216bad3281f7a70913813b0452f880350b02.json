{"ast": null, "code": "function isString(value) {\n  return typeof value === 'string';\n}\nexport { isString };", "map": {"version": 3, "names": ["isString", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isString.mjs"], "sourcesContent": ["function isString(value) {\n    return typeof value === 'string';\n}\n\nexport { isString };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
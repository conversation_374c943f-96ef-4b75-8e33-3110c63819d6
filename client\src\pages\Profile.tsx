import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { User, Settings, Shield, Target, Save, Award } from 'lucide-react';

const Profile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'privacy' | 'goals'>('profile');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [profileData, setProfileData] = useState({
    displayName: user?.profile.displayName || '',
    bio: user?.profile.bio || '',
    timezone: user?.profile.timezone || 'UTC'
  });

  const [privacyData, setPrivacyData] = useState({
    shareScreenTime: user?.privacy.shareScreenTime || false,
    showInLeaderboard: user?.privacy.showInLeaderboard || false,
    allowFriendRequests: user?.privacy.allowFriendRequests ?? true
  });

  const [goalsData, setGoalsData] = useState({
    dailyLimit: user?.goals.dailyLimit ? Math.round(user.goals.dailyLimit / 60) : 480, // Convert to minutes
    weeklyLimit: user?.goals.weeklyLimit ? Math.round(user.goals.weeklyLimit / 60) : 3360,
    categories: user?.goals.categories.map(cat => ({
      name: cat.name,
      limit: Math.round(cat.limit / 60)
    })) || []
  });

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      await axios.put('/users/profile', profileData);
      updateUser({ profile: { ...user!.profile, ...profileData } });
      setMessage('Profile updated successfully!');
    } catch (err: any) {
      setMessage(err.response?.data?.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePrivacySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      await axios.put('/users/privacy', privacyData);
      updateUser({ privacy: { ...user!.privacy, ...privacyData } });
      setMessage('Privacy settings updated successfully!');
    } catch (err: any) {
      setMessage(err.response?.data?.message || 'Failed to update privacy settings');
    } finally {
      setLoading(false);
    }
  };

  const handleGoalsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      await axios.put('/users/goals', goalsData);
      updateUser({ 
        goals: { 
          ...user!.goals, 
          dailyLimit: goalsData.dailyLimit * 60,
          weeklyLimit: goalsData.weeklyLimit * 60,
          categories: goalsData.categories.map(cat => ({
            name: cat.name,
            limit: cat.limit * 60
          }))
        } 
      });
      setMessage('Goals updated successfully!');
    } catch (err: any) {
      setMessage(err.response?.data?.message || 'Failed to update goals');
    } finally {
      setLoading(false);
    }
  };

  const addCategory = () => {
    setGoalsData(prev => ({
      ...prev,
      categories: [...prev.categories, { name: '', limit: 60 }]
    }));
  };

  const removeCategory = (index: number) => {
    setGoalsData(prev => ({
      ...prev,
      categories: prev.categories.filter((_, i) => i !== index)
    }));
  };

  const updateCategory = (index: number, field: 'name' | 'limit', value: string | number) => {
    setGoalsData(prev => ({
      ...prev,
      categories: prev.categories.map((cat, i) => 
        i === index ? { ...cat, [field]: value } : cat
      )
    }));
  };

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'privacy', name: 'Privacy', icon: Shield },
    { id: 'goals', name: 'Goals', icon: Target }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
        <p className="text-gray-600">Manage your account and preferences</p>
      </div>

      {/* User Info Card */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center">
            <span className="text-white text-xl font-medium">
              {user?.profile.displayName.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{user?.profile.displayName}</h2>
            <p className="text-gray-600">@{user?.username}</p>
            <p className="text-sm text-gray-500">Member since {new Date(user?.createdAt || '').toLocaleDateString()}</p>
          </div>
          <div className="ml-auto flex items-center space-x-4">
            <div className="text-center">
              <div className="flex items-center space-x-1">
                <Award className="h-4 w-4 text-yellow-500" />
                <span className="text-lg font-semibold text-gray-900">{user?.achievements?.length || 0}</span>
              </div>
              <p className="text-xs text-gray-500">Achievements</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          );
        })}
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-md ${
          message.includes('successfully') 
            ? 'bg-success-50 text-success-800 border border-success-200' 
            : 'bg-danger-50 text-danger-800 border border-danger-200'
        }`}>
          {message}
        </div>
      )}

      {/* Tab Content */}
      <div className="card">
        {activeTab === 'profile' && (
          <form onSubmit={handleProfileSubmit} className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Profile Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Display Name
              </label>
              <input
                type="text"
                value={profileData.displayName}
                onChange={(e) => setProfileData(prev => ({ ...prev, displayName: e.target.value }))}
                className="input"
                placeholder="How others will see you"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bio
              </label>
              <textarea
                value={profileData.bio}
                onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                className="input"
                rows={3}
                placeholder="Tell others about yourself..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={profileData.timezone}
                onChange={(e) => setProfileData(prev => ({ ...prev, timezone: e.target.value }))}
                className="input"
              >
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
                <option value="Europe/London">London</option>
                <option value="Europe/Paris">Paris</option>
                <option value="Asia/Tokyo">Tokyo</option>
              </select>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </form>
        )}

        {activeTab === 'privacy' && (
          <form onSubmit={handlePrivacySubmit} className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Privacy Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Share Screen Time Data</h4>
                  <p className="text-sm text-gray-500">Allow your screen time data to be visible to friends and in leaderboards</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={privacyData.shareScreenTime}
                    onChange={(e) => setPrivacyData(prev => ({ ...prev, shareScreenTime: e.target.checked }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Show in Leaderboard</h4>
                  <p className="text-sm text-gray-500">Appear in global leaderboards and competitions</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={privacyData.showInLeaderboard}
                    onChange={(e) => setPrivacyData(prev => ({ ...prev, showInLeaderboard: e.target.checked }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Allow Friend Requests</h4>
                  <p className="text-sm text-gray-500">Let other users send you friend requests</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={privacyData.allowFriendRequests}
                    onChange={(e) => setPrivacyData(prev => ({ ...prev, allowFriendRequests: e.target.checked }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </form>
        )}

        {activeTab === 'goals' && (
          <form onSubmit={handleGoalsSubmit} className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Screen Time Goals</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Daily Limit (minutes)
                </label>
                <input
                  type="number"
                  value={goalsData.dailyLimit}
                  onChange={(e) => setGoalsData(prev => ({ ...prev, dailyLimit: parseInt(e.target.value) || 0 }))}
                  className="input"
                  min="0"
                  placeholder="480"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weekly Limit (minutes)
                </label>
                <input
                  type="number"
                  value={goalsData.weeklyLimit}
                  onChange={(e) => setGoalsData(prev => ({ ...prev, weeklyLimit: parseInt(e.target.value) || 0 }))}
                  className="input"
                  min="0"
                  placeholder="3360"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-900">Category Limits</h4>
                <button
                  type="button"
                  onClick={addCategory}
                  className="btn-secondary text-sm"
                >
                  Add Category
                </button>
              </div>

              <div className="space-y-3">
                {goalsData.categories.map((category, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={category.name}
                      onChange={(e) => updateCategory(index, 'name', e.target.value)}
                      className="input flex-1"
                      placeholder="Category name"
                    />
                    <input
                      type="number"
                      value={category.limit}
                      onChange={(e) => updateCategory(index, 'limit', parseInt(e.target.value) || 0)}
                      className="input w-32"
                      min="0"
                      placeholder="Minutes"
                    />
                    <button
                      type="button"
                      onClick={() => removeCategory(index)}
                      className="btn-danger text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default Profile;

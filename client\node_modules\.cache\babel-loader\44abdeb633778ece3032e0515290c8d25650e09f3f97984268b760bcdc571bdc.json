{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeLinePoints } from '../../cartesian/Line';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickLineId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\nfunction isLineSettings(item) {\n  return item.type === 'line';\n}\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * So here instead of reading the dataKey from the props, we always read it from the state.\n */\nvar selectSynchronisedLineSettings = createSelector([selectUnfilteredCartesianItems, pickLineId], (graphicalItems, id) => graphicalItems.filter(isLineSettings).find(x => x.id === id));\nexport var selectLinePoints = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectSynchronisedLineSettings, selectBandSize, selectChartDataWithIndexesIfNotInPanorama], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, lineSettings, bandSize, _ref) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (lineSettings == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    data\n  } = lineSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeLinePoints({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeLinePoints", "selectChartDataWithIndexesIfNotInPanorama", "selectChartLayout", "selectAxisWithScale", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "getBandSizeOfAxis", "isCategoricalAxis", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectBandSize", "layout", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "pickLineId", "_state", "_isPanorama", "id", "isLineSettings", "item", "type", "selectSynchronisedLineSettings", "graphicalItems", "filter", "find", "x", "selectLinePoints", "lineSettings", "bandSize", "_ref", "chartData", "dataStartIndex", "dataEndIndex", "length", "undefined", "dataKey", "data", "displayedData", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/lineSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeLinePoints } from '../../cartesian/Line';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickLineId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\nfunction isLineSettings(item) {\n  return item.type === 'line';\n}\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * So here instead of reading the dataKey from the props, we always read it from the state.\n */\nvar selectSynchronisedLineSettings = createSelector([selectUnfilteredCartesianItems, pickLineId], (graphicalItems, id) => graphicalItems.filter(isLineSettings).find(x => x.id === id));\nexport var selectLinePoints = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectSynchronisedLineSettings, selectBandSize, selectChartDataWithIndexesIfNotInPanorama], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, lineSettings, bandSize, _ref) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (lineSettings == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    data\n  } = lineSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeLinePoints({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,mBAAmB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AACjH,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC5E,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKT,mBAAmB,CAACM,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAC7H,IAAIC,gBAAgB,GAAGA,CAACJ,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKR,0BAA0B,CAACK,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAChI,IAAIE,oBAAoB,GAAGA,CAACL,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKT,mBAAmB,CAACM,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAC7H,IAAIK,gBAAgB,GAAGA,CAACR,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKR,0BAA0B,CAACK,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAChI,IAAIM,cAAc,GAAGnB,cAAc,CAAC,CAACG,iBAAiB,EAAEM,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,CAAC,EAAE,CAACE,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,KAAK;EACzL,IAAIhB,iBAAiB,CAACY,MAAM,EAAE,OAAO,CAAC,EAAE;IACtC,OAAOb,iBAAiB,CAACc,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;EACpD;EACA,OAAOhB,iBAAiB,CAACe,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;AACpD,CAAC,CAAC;AACF,IAAIC,UAAU,GAAGA,CAACC,MAAM,EAAEV,QAAQ,EAAEJ,QAAQ,EAAEe,WAAW,EAAEC,EAAE,KAAKA,EAAE;AACpE,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACC,IAAI,KAAK,MAAM;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,8BAA8B,GAAGhC,cAAc,CAAC,CAACM,8BAA8B,EAAEmB,UAAU,CAAC,EAAE,CAACQ,cAAc,EAAEL,EAAE,KAAKK,cAAc,CAACC,MAAM,CAACL,cAAc,CAAC,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKA,EAAE,CAAC,CAAC;AACvL,OAAO,IAAIS,gBAAgB,GAAGrC,cAAc,CAAC,CAACG,iBAAiB,EAAEM,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,EAAEc,8BAA8B,EAAEb,cAAc,EAAEjB,yCAAyC,CAAC,EAAE,CAACkB,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEc,YAAY,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EAC3T,IAAI;IACFC,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGH,IAAI;EACR,IAAIF,YAAY,IAAI,IAAI,IAAIjB,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAID,UAAU,CAACqB,MAAM,KAAK,CAAC,IAAIpB,UAAU,CAACoB,MAAM,KAAK,CAAC,IAAIL,QAAQ,IAAI,IAAI,EAAE;IAChL,OAAOM,SAAS;EAClB;EACA,IAAI;IACFC,OAAO;IACPC;EACF,CAAC,GAAGT,YAAY;EAChB,IAAIU,aAAa;EACjB,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE;IACnCI,aAAa,GAAGD,IAAI;EACtB,CAAC,MAAM;IACLC,aAAa,GAAGP,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACQ,KAAK,CAACP,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIK,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOH,SAAS;EAClB;EACA,OAAO5C,iBAAiB,CAAC;IACvBmB,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVsB,OAAO;IACPP,QAAQ;IACRS;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
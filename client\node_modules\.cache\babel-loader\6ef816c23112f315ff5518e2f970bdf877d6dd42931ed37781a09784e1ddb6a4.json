{"ast": null, "code": "const CASE_SPLIT_PATTERN = /\\p{Lu}?\\p{Ll}+|[0-9]+|\\p{Lu}+(?!\\p{Ll})|\\p{Emoji_Presentation}|\\p{Extended_Pictographic}|\\p{L}+/gu;\nfunction words(str) {\n  return Array.from(str.match(CASE_SPLIT_PATTERN) ?? []);\n}\nexport { CASE_SPLIT_PATTERN, words };", "map": {"version": 3, "names": ["CASE_SPLIT_PATTERN", "words", "str", "Array", "from", "match"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/words.mjs"], "sourcesContent": ["const CASE_SPLIT_PATTERN = /\\p{Lu}?\\p{Ll}+|[0-9]+|\\p{Lu}+(?!\\p{Ll})|\\p{Emoji_Presentation}|\\p{Extended_Pictographic}|\\p{L}+/gu;\nfunction words(str) {\n    return Array.from(str.match(CASE_SPLIT_PATTERN) ?? []);\n}\n\nexport { CASE_SPLIT_PATTERN, words };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,mGAAmG;AAC9H,SAASC,KAAKA,CAACC,GAAG,EAAE;EAChB,OAAOC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,KAAK,CAACL,kBAAkB,CAAC,IAAI,EAAE,CAAC;AAC1D;AAEA,SAASA,kBAAkB,EAAEC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { compareValues } from '../_internal/compareValues.mjs';\nfunction orderBy(arr, criteria, orders) {\n  return arr.slice().sort((a, b) => {\n    const ordersLength = orders.length;\n    for (let i = 0; i < criteria.length; i++) {\n      const order = ordersLength > i ? orders[i] : orders[ordersLength - 1];\n      const criterion = criteria[i];\n      const criterionIsFunction = typeof criterion === 'function';\n      const valueA = criterionIsFunction ? criterion(a) : a[criterion];\n      const valueB = criterionIsFunction ? criterion(b) : b[criterion];\n      const result = compareValues(valueA, valueB, order);\n      if (result !== 0) {\n        return result;\n      }\n    }\n    return 0;\n  });\n}\nexport { orderBy };", "map": {"version": 3, "names": ["compareValues", "orderBy", "arr", "criteria", "orders", "slice", "sort", "a", "b", "ordersLength", "length", "i", "order", "criterion", "criterionIsFunction", "valueA", "valueB", "result"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/orderBy.mjs"], "sourcesContent": ["import { compareValues } from '../_internal/compareValues.mjs';\n\nfunction orderBy(arr, criteria, orders) {\n    return arr.slice().sort((a, b) => {\n        const ordersLength = orders.length;\n        for (let i = 0; i < criteria.length; i++) {\n            const order = ordersLength > i ? orders[i] : orders[ordersLength - 1];\n            const criterion = criteria[i];\n            const criterionIsFunction = typeof criterion === 'function';\n            const valueA = criterionIsFunction ? criterion(a) : a[criterion];\n            const valueB = criterionIsFunction ? criterion(b) : b[criterion];\n            const result = compareValues(valueA, valueB, order);\n            if (result !== 0) {\n                return result;\n            }\n        }\n        return 0;\n    });\n}\n\nexport { orderBy };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gCAAgC;AAE9D,SAASC,OAAOA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACpC,OAAOF,GAAG,CAACG,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC9B,MAAMC,YAAY,GAAGL,MAAM,CAACM,MAAM;IAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACO,MAAM,EAAEC,CAAC,EAAE,EAAE;MACtC,MAAMC,KAAK,GAAGH,YAAY,GAAGE,CAAC,GAAGP,MAAM,CAACO,CAAC,CAAC,GAAGP,MAAM,CAACK,YAAY,GAAG,CAAC,CAAC;MACrE,MAAMI,SAAS,GAAGV,QAAQ,CAACQ,CAAC,CAAC;MAC7B,MAAMG,mBAAmB,GAAG,OAAOD,SAAS,KAAK,UAAU;MAC3D,MAAME,MAAM,GAAGD,mBAAmB,GAAGD,SAAS,CAACN,CAAC,CAAC,GAAGA,CAAC,CAACM,SAAS,CAAC;MAChE,MAAMG,MAAM,GAAGF,mBAAmB,GAAGD,SAAS,CAACL,CAAC,CAAC,GAAGA,CAAC,CAACK,SAAS,CAAC;MAChE,MAAMI,MAAM,GAAGjB,aAAa,CAACe,MAAM,EAAEC,MAAM,EAAEJ,KAAK,CAAC;MACnD,IAAIK,MAAM,KAAK,CAAC,EAAE;QACd,OAAOA,MAAM;MACjB;IACJ;IACA,OAAO,CAAC;EACZ,CAAC,CAAC;AACN;AAEA,SAAShB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { isArray } from '../compat/predicate/isArray.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\nimport { camelCase } from '../string/camelCase.mjs';\nfunction toCamelCaseKeys(obj) {\n  if (isArray(obj)) {\n    return obj.map(item => toCamelCaseKeys(item));\n  }\n  if (isPlainObject(obj)) {\n    const result = {};\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const camelKey = camelCase(key);\n      const camelCaseKeys = toCamelCaseKeys(obj[key]);\n      result[camelKey] = camelCaseKeys;\n    }\n    return result;\n  }\n  return obj;\n}\nexport { toCamelCaseKeys };", "map": {"version": 3, "names": ["isArray", "isPlainObject", "camelCase", "toCamelCaseKeys", "obj", "map", "item", "result", "keys", "Object", "i", "length", "key", "camel<PERSON><PERSON>", "camelCase<PERSON>eys"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/toCamelCaseKeys.mjs"], "sourcesContent": ["import { isArray } from '../compat/predicate/isArray.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\nimport { camelCase } from '../string/camelCase.mjs';\n\nfunction toCamelCaseKeys(obj) {\n    if (isArray(obj)) {\n        return obj.map(item => toCamelCaseKeys(item));\n    }\n    if (isPlainObject(obj)) {\n        const result = {};\n        const keys = Object.keys(obj);\n        for (let i = 0; i < keys.length; i++) {\n            const key = keys[i];\n            const camelKey = camelCase(key);\n            const camelCaseKeys = toCamelCaseKeys(obj[key]);\n            result[camelKey] = camelCaseKeys;\n        }\n        return result;\n    }\n    return obj;\n}\n\nexport { toCamelCaseKeys };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iCAAiC;AACzD,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,SAAS,QAAQ,yBAAyB;AAEnD,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC1B,IAAIJ,OAAO,CAACI,GAAG,CAAC,EAAE;IACd,OAAOA,GAAG,CAACC,GAAG,CAACC,IAAI,IAAIH,eAAe,CAACG,IAAI,CAAC,CAAC;EACjD;EACA,IAAIL,aAAa,CAACG,GAAG,CAAC,EAAE;IACpB,MAAMG,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,GAAG,CAAC;IAC7B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;MACnB,MAAMG,QAAQ,GAAGX,SAAS,CAACU,GAAG,CAAC;MAC/B,MAAME,aAAa,GAAGX,eAAe,CAACC,GAAG,CAACQ,GAAG,CAAC,CAAC;MAC/CL,MAAM,CAACM,QAAQ,CAAC,GAAGC,aAAa;IACpC;IACA,OAAOP,MAAM;EACjB;EACA,OAAOH,GAAG;AACd;AAEA,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
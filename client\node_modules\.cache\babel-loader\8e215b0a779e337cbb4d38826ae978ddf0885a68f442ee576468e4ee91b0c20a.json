{"ast": null, "code": "function invert(obj) {\n  const result = {};\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = obj[key];\n    result[value] = key;\n  }\n  return result;\n}\nexport { invert };", "map": {"version": 3, "names": ["invert", "obj", "result", "keys", "Object", "i", "length", "key", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/invert.mjs"], "sourcesContent": ["function invert(obj) {\n    const result = {};\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = obj[key];\n        result[value] = key;\n    }\n    return result;\n}\n\nexport { invert };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,GAAG,CAAC;EAC7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,MAAMG,KAAK,GAAGP,GAAG,CAACM,GAAG,CAAC;IACtBL,MAAM,CAACM,KAAK,CAAC,GAAGD,GAAG;EACvB;EACA,OAAOL,MAAM;AACjB;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
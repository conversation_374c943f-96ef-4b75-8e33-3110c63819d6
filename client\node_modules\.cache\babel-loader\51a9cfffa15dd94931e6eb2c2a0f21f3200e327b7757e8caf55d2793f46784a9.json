{"ast": null, "code": "export function getSliced(arr, startIndex, endIndex) {\n  if (!Array.isArray(arr)) {\n    return arr;\n  }\n  if (arr && startIndex + endIndex !== 0) {\n    return arr.slice(startIndex, endIndex + 1);\n  }\n  return arr;\n}", "map": {"version": 3, "names": ["getSliced", "arr", "startIndex", "endIndex", "Array", "isArray", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/getSliced.js"], "sourcesContent": ["export function getSliced(arr, startIndex, endIndex) {\n  if (!Array.isArray(arr)) {\n    return arr;\n  }\n  if (arr && startIndex + endIndex !== 0) {\n    return arr.slice(startIndex, endIndex + 1);\n  }\n  return arr;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EACnD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,EAAE;IACvB,OAAOA,GAAG;EACZ;EACA,IAAIA,GAAG,IAAIC,UAAU,GAAGC,QAAQ,KAAK,CAAC,EAAE;IACtC,OAAOF,GAAG,CAACK,KAAK,CAACJ,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC;EAC5C;EACA,OAAOF,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
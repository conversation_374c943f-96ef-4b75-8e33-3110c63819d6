{"ast": null, "code": "function zipWith(arr1, ...rest) {\n  const arrs = [arr1, ...rest.slice(0, -1)];\n  const combine = rest[rest.length - 1];\n  const maxIndex = Math.max(...arrs.map(arr => arr.length));\n  const result = Array(maxIndex);\n  for (let i = 0; i < maxIndex; i++) {\n    const elements = arrs.map(arr => arr[i]);\n    result[i] = combine(...elements);\n  }\n  return result;\n}\nexport { zipWith };", "map": {"version": 3, "names": ["zipWith", "arr1", "rest", "arrs", "slice", "combine", "length", "maxIndex", "Math", "max", "map", "arr", "result", "Array", "i", "elements"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/zipWith.mjs"], "sourcesContent": ["function zipWith(arr1, ...rest) {\n    const arrs = [arr1, ...rest.slice(0, -1)];\n    const combine = rest[rest.length - 1];\n    const maxIndex = Math.max(...arrs.map(arr => arr.length));\n    const result = Array(maxIndex);\n    for (let i = 0; i < maxIndex; i++) {\n        const elements = arrs.map(arr => arr[i]);\n        result[i] = combine(...elements);\n    }\n    return result;\n}\n\nexport { zipWith };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,IAAI,EAAE,GAAGC,IAAI,EAAE;EAC5B,MAAMC,IAAI,GAAG,CAACF,IAAI,EAAE,GAAGC,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMC,OAAO,GAAGH,IAAI,CAACA,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;EACrC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGN,IAAI,CAACO,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACL,MAAM,CAAC,CAAC;EACzD,MAAMM,MAAM,GAAGC,KAAK,CAACN,QAAQ,CAAC;EAC9B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,QAAQ,EAAEO,CAAC,EAAE,EAAE;IAC/B,MAAMC,QAAQ,GAAGZ,IAAI,CAACO,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACG,CAAC,CAAC,CAAC;IACxCF,MAAM,CAACE,CAAC,CAAC,GAAGT,OAAO,CAAC,GAAGU,QAAQ,CAAC;EACpC;EACA,OAAOH,MAAM;AACjB;AAEA,SAASZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
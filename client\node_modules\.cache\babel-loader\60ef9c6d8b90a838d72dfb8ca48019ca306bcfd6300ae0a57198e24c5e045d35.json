{"ast": null, "code": "import { toNumber } from './toNumber.mjs';\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === Infinity || value === -Infinity) {\n    const sign = value < 0 ? -1 : 1;\n    return sign * Number.MAX_VALUE;\n  }\n  return value === value ? value : 0;\n}\nexport { toFinite };", "map": {"version": 3, "names": ["toNumber", "toFinite", "value", "Infinity", "sign", "Number", "MAX_VALUE"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/util/toFinite.mjs"], "sourcesContent": ["import { toNumber } from './toNumber.mjs';\n\nfunction toFinite(value) {\n    if (!value) {\n        return value === 0 ? value : 0;\n    }\n    value = toNumber(value);\n    if (value === Infinity || value === -Infinity) {\n        const sign = value < 0 ? -1 : 1;\n        return sign * Number.MAX_VALUE;\n    }\n    return value === value ? value : 0;\n}\n\nexport { toFinite };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,IAAI,CAACA,KAAK,EAAE;IACR,OAAOA,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EAClC;EACAA,KAAK,GAAGF,QAAQ,CAACE,KAAK,CAAC;EACvB,IAAIA,KAAK,KAAKC,QAAQ,IAAID,KAAK,KAAK,CAACC,QAAQ,EAAE;IAC3C,MAAMC,IAAI,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,OAAOE,IAAI,GAAGC,MAAM,CAACC,SAAS;EAClC;EACA,OAAOJ,KAAK,KAAKA,KAAK,GAAGA,KAAK,GAAG,CAAC;AACtC;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
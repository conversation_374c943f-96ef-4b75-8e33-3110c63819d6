{"ast": null, "code": "function sample(arr) {\n  const randomIndex = Math.floor(Math.random() * arr.length);\n  return arr[randomIndex];\n}\nexport { sample };", "map": {"version": 3, "names": ["sample", "arr", "randomIndex", "Math", "floor", "random", "length"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/sample.mjs"], "sourcesContent": ["function sample(arr) {\n    const randomIndex = Math.floor(Math.random() * arr.length);\n    return arr[randomIndex];\n}\n\nexport { sample };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,GAAG,EAAE;EACjB,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,GAAG,CAACK,MAAM,CAAC;EAC1D,OAAOL,GAAG,CAACC,WAAW,CAAC;AAC3B;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export function reduxDevtoolsJsonStringifyReplacer(_key, value) {\n  if (value instanceof HTMLElement) {\n    return \"HTMLElement <\".concat(value.tagName, \" class=\\\"\").concat(value.className, \"\\\">\");\n  }\n  if (value === window) {\n    return 'global.window';\n  }\n  return value;\n}", "map": {"version": 3, "names": ["reduxDevtoolsJsonStringifyReplacer", "_key", "value", "HTMLElement", "concat", "tagName", "className", "window"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/reduxDevtoolsJsonStringifyReplacer.js"], "sourcesContent": ["export function reduxDevtoolsJsonStringifyReplacer(_key, value) {\n  if (value instanceof HTMLElement) {\n    return \"HTMLElement <\".concat(value.tagName, \" class=\\\"\").concat(value.className, \"\\\">\");\n  }\n  if (value === window) {\n    return 'global.window';\n  }\n  return value;\n}"], "mappings": "AAAA,OAAO,SAASA,kCAAkCA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC9D,IAAIA,KAAK,YAAYC,WAAW,EAAE;IAChC,OAAO,eAAe,CAACC,MAAM,CAACF,KAAK,CAACG,OAAO,EAAE,WAAW,CAAC,CAACD,MAAM,CAACF,KAAK,CAACI,SAAS,EAAE,KAAK,CAAC;EAC1F;EACA,IAAIJ,KAAK,KAAKK,MAAM,EAAE;IACpB,OAAO,eAAe;EACxB;EACA,OAAOL,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
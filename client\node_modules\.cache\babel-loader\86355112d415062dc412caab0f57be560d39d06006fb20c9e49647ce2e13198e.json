{"ast": null, "code": "export var selectTooltipState = state => state.tooltip;", "map": {"version": 3, "names": ["selectTooltipState", "state", "tooltip"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/selectTooltipState.js"], "sourcesContent": ["export var selectTooltipState = state => state.tooltip;"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAGC,KAAK,IAAIA,KAAK,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { useEffect, useRef, useState } from 'react';\nimport { noop } from 'es-toolkit';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport configUpdate from './configUpdate';\nimport { configEasing } from './easing';\nimport { useAnimationManager } from './useAnimationManager';\nvar defaultJavascriptAnimateProps = {\n  begin: 0,\n  duration: 1000,\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n};\nvar from = {\n  t: 0\n};\nvar to = {\n  t: 1\n};\nexport function JavascriptAnimate(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultJavascriptAnimateProps);\n  var {\n    isActive,\n    canBegin,\n    duration,\n    easing,\n    begin,\n    onAnimationEnd,\n    onAnimationStart,\n    children\n  } = props;\n  var animationManager = useAnimationManager('JavascriptAnimate', props.animationManager);\n  var [style, setStyle] = useState(isActive ? from : to);\n  var stopJSAnimation = useRef(null);\n  useEffect(() => {\n    if (!isActive) {\n      setStyle(to);\n    }\n  }, [isActive]);\n  useEffect(() => {\n    if (!isActive || !canBegin) {\n      return noop;\n    }\n    var startAnimation = configUpdate(from, to, configEasing(easing), duration, setStyle, animationManager.getTimeoutController());\n    var onAnimationActive = () => {\n      stopJSAnimation.current = startAnimation();\n    };\n    animationManager.start([onAnimationStart, begin, onAnimationActive, duration, onAnimationEnd]);\n    return () => {\n      animationManager.stop();\n      if (stopJSAnimation.current) {\n        stopJSAnimation.current();\n      }\n      onAnimationEnd();\n    };\n  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager]);\n  return children(style.t);\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "noop", "resolveDefaultProps", "configUpdate", "configEasing", "useAnimationManager", "defaultJavascriptAnimateProps", "begin", "duration", "easing", "isActive", "canBegin", "onAnimationEnd", "onAnimationStart", "from", "t", "to", "JavascriptAnimate", "outsideProps", "props", "children", "animationManager", "style", "setStyle", "stopJSAnimation", "startAnimation", "getTimeoutController", "onAnimationActive", "current", "start", "stop"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/animation/JavascriptAnimate.js"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { noop } from 'es-toolkit';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport configUpdate from './configUpdate';\nimport { configEasing } from './easing';\nimport { useAnimationManager } from './useAnimationManager';\nvar defaultJavascriptAnimateProps = {\n  begin: 0,\n  duration: 1000,\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n};\nvar from = {\n  t: 0\n};\nvar to = {\n  t: 1\n};\nexport function JavascriptAnimate(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultJavascriptAnimateProps);\n  var {\n    isActive,\n    canBegin,\n    duration,\n    easing,\n    begin,\n    onAnimationEnd,\n    onAnimationStart,\n    children\n  } = props;\n  var animationManager = useAnimationManager('JavascriptAnimate', props.animationManager);\n  var [style, setStyle] = useState(isActive ? from : to);\n  var stopJSAnimation = useRef(null);\n  useEffect(() => {\n    if (!isActive) {\n      setStyle(to);\n    }\n  }, [isActive]);\n  useEffect(() => {\n    if (!isActive || !canBegin) {\n      return noop;\n    }\n    var startAnimation = configUpdate(from, to, configEasing(easing), duration, setStyle, animationManager.getTimeoutController());\n    var onAnimationActive = () => {\n      stopJSAnimation.current = startAnimation();\n    };\n    animationManager.start([onAnimationStart, begin, onAnimationActive, duration, onAnimationEnd]);\n    return () => {\n      animationManager.stop();\n      if (stopJSAnimation.current) {\n        stopJSAnimation.current();\n      }\n      onAnimationEnd();\n    };\n  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager]);\n  return children(style.t);\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,IAAIC,6BAA6B,GAAG;EAClCC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;EACxBC,gBAAgB,EAAEA,CAAA,KAAM,CAAC;AAC3B,CAAC;AACD,IAAIC,IAAI,GAAG;EACTC,CAAC,EAAE;AACL,CAAC;AACD,IAAIC,EAAE,GAAG;EACPD,CAAC,EAAE;AACL,CAAC;AACD,OAAO,SAASE,iBAAiBA,CAACC,YAAY,EAAE;EAC9C,IAAIC,KAAK,GAAGjB,mBAAmB,CAACgB,YAAY,EAAEZ,6BAA6B,CAAC;EAC5E,IAAI;IACFI,QAAQ;IACRC,QAAQ;IACRH,QAAQ;IACRC,MAAM;IACNF,KAAK;IACLK,cAAc;IACdC,gBAAgB;IAChBO;EACF,CAAC,GAAGD,KAAK;EACT,IAAIE,gBAAgB,GAAGhB,mBAAmB,CAAC,mBAAmB,EAAEc,KAAK,CAACE,gBAAgB,CAAC;EACvF,IAAI,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAACU,QAAQ,GAAGI,IAAI,GAAGE,EAAE,CAAC;EACtD,IAAIQ,eAAe,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAClCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,QAAQ,EAAE;MACba,QAAQ,CAACP,EAAE,CAAC;IACd;EACF,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACdZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,QAAQ,IAAI,CAACC,QAAQ,EAAE;MAC1B,OAAOV,IAAI;IACb;IACA,IAAIwB,cAAc,GAAGtB,YAAY,CAACW,IAAI,EAAEE,EAAE,EAAEZ,YAAY,CAACK,MAAM,CAAC,EAAED,QAAQ,EAAEe,QAAQ,EAAEF,gBAAgB,CAACK,oBAAoB,CAAC,CAAC,CAAC;IAC9H,IAAIC,iBAAiB,GAAGA,CAAA,KAAM;MAC5BH,eAAe,CAACI,OAAO,GAAGH,cAAc,CAAC,CAAC;IAC5C,CAAC;IACDJ,gBAAgB,CAACQ,KAAK,CAAC,CAAChB,gBAAgB,EAAEN,KAAK,EAAEoB,iBAAiB,EAAEnB,QAAQ,EAAEI,cAAc,CAAC,CAAC;IAC9F,OAAO,MAAM;MACXS,gBAAgB,CAACS,IAAI,CAAC,CAAC;MACvB,IAAIN,eAAe,CAACI,OAAO,EAAE;QAC3BJ,eAAe,CAACI,OAAO,CAAC,CAAC;MAC3B;MACAhB,cAAc,CAAC,CAAC;IAClB,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,EAAEC,QAAQ,EAAEH,QAAQ,EAAEC,MAAM,EAAEF,KAAK,EAAEM,gBAAgB,EAAED,cAAc,EAAES,gBAAgB,CAAC,CAAC;EACrG,OAAOD,QAAQ,CAACE,KAAK,CAACP,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _excluded = [\"width\", \"height\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { ChartDataContextProvider } from '../context/chartDataContext';\nimport { ReportMainChartProps } from '../state/ReportMainChartProps';\nimport { ReportChartProps } from '../state/ReportChartProps';\nimport { CategoricalChart } from './CategoricalChart';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar defaultMargin = {\n  top: 5,\n  right: 5,\n  bottom: 5,\n  left: 5\n};\nvar defaultProps = {\n  accessibilityLayer: true,\n  layout: 'horizontal',\n  stackOffset: 'none',\n  barCategoryGap: '10%',\n  barGap: 4,\n  margin: defaultMargin,\n  reverseStackOrder: false,\n  syncMethod: 'index'\n};\n\n/**\n * These are one-time, immutable options that decide the chart's behavior.\n * Users who wish to call CartesianChart may decide to pass these options explicitly,\n * but usually we would expect that they use one of the convenience components like BarChart, LineChart, etc.\n */\n\nexport var CartesianChart = /*#__PURE__*/forwardRef(function CartesianChart(props, ref) {\n  var _categoricalChartProp;\n  var rootChartProps = resolveDefaultProps(props.categoricalChartProps, defaultProps);\n  var {\n      width,\n      height\n    } = rootChartProps,\n    otherCategoricalProps = _objectWithoutProperties(rootChartProps, _excluded);\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  var {\n    chartName,\n    defaultTooltipEventType,\n    validateTooltipEventTypes,\n    tooltipPayloadSearcher,\n    categoricalChartProps\n  } = props;\n  var options = {\n    chartName,\n    defaultTooltipEventType,\n    validateTooltipEventTypes,\n    tooltipPayloadSearcher,\n    eventEmitter: undefined\n  };\n  return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n    preloadedState: {\n      options\n    },\n    reduxStoreName: (_categoricalChartProp = categoricalChartProps.id) !== null && _categoricalChartProp !== void 0 ? _categoricalChartProp : chartName\n  }, /*#__PURE__*/React.createElement(ChartDataContextProvider, {\n    chartData: categoricalChartProps.data\n  }), /*#__PURE__*/React.createElement(ReportMainChartProps, {\n    width: width,\n    height: height,\n    layout: rootChartProps.layout,\n    margin: rootChartProps.margin\n  }), /*#__PURE__*/React.createElement(ReportChartProps, {\n    accessibilityLayer: rootChartProps.accessibilityLayer,\n    barCategoryGap: rootChartProps.barCategoryGap,\n    maxBarSize: rootChartProps.maxBarSize,\n    stackOffset: rootChartProps.stackOffset,\n    barGap: rootChartProps.barGap,\n    barSize: rootChartProps.barSize,\n    syncId: rootChartProps.syncId,\n    syncMethod: rootChartProps.syncMethod,\n    className: rootChartProps.className\n  }), /*#__PURE__*/React.createElement(CategoricalChart, _extends({}, otherCategoricalProps, {\n    width: width,\n    height: height,\n    ref: ref\n  })));\n});", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "forwardRef", "RechartsStoreProvider", "ChartDataContextProvider", "ReportMainChartProps", "ReportChartProps", "CategoricalChart", "resolveDefaultProps", "isPositiveNumber", "defaultMargin", "top", "right", "bottom", "left", "defaultProps", "accessibilityLayer", "layout", "stackOffset", "barCategoryGap", "barGap", "margin", "reverseStackOrder", "syncMethod", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "ref", "_categoricalChartProp", "rootChartProps", "categoricalChartProps", "width", "height", "otherCategoricalProps", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "options", "eventEmitter", "undefined", "createElement", "preloadedState", "reduxStoreName", "id", "chartData", "data", "maxBarSize", "barSize", "syncId", "className"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/chart/CartesianChart.js"], "sourcesContent": ["var _excluded = [\"width\", \"height\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { ChartDataContextProvider } from '../context/chartDataContext';\nimport { ReportMainChartProps } from '../state/ReportMainChartProps';\nimport { ReportChartProps } from '../state/ReportChartProps';\nimport { CategoricalChart } from './CategoricalChart';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar defaultMargin = {\n  top: 5,\n  right: 5,\n  bottom: 5,\n  left: 5\n};\nvar defaultProps = {\n  accessibilityLayer: true,\n  layout: 'horizontal',\n  stackOffset: 'none',\n  barCategoryGap: '10%',\n  barGap: 4,\n  margin: defaultMargin,\n  reverseStackOrder: false,\n  syncMethod: 'index'\n};\n\n/**\n * These are one-time, immutable options that decide the chart's behavior.\n * Users who wish to call CartesianChart may decide to pass these options explicitly,\n * but usually we would expect that they use one of the convenience components like BarChart, LineChart, etc.\n */\n\nexport var CartesianChart = /*#__PURE__*/forwardRef(function CartesianChart(props, ref) {\n  var _categoricalChartProp;\n  var rootChartProps = resolveDefaultProps(props.categoricalChartProps, defaultProps);\n  var {\n      width,\n      height\n    } = rootChartProps,\n    otherCategoricalProps = _objectWithoutProperties(rootChartProps, _excluded);\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  var {\n    chartName,\n    defaultTooltipEventType,\n    validateTooltipEventTypes,\n    tooltipPayloadSearcher,\n    categoricalChartProps\n  } = props;\n  var options = {\n    chartName,\n    defaultTooltipEventType,\n    validateTooltipEventTypes,\n    tooltipPayloadSearcher,\n    eventEmitter: undefined\n  };\n  return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n    preloadedState: {\n      options\n    },\n    reduxStoreName: (_categoricalChartProp = categoricalChartProps.id) !== null && _categoricalChartProp !== void 0 ? _categoricalChartProp : chartName\n  }, /*#__PURE__*/React.createElement(ChartDataContextProvider, {\n    chartData: categoricalChartProps.data\n  }), /*#__PURE__*/React.createElement(ReportMainChartProps, {\n    width: width,\n    height: height,\n    layout: rootChartProps.layout,\n    margin: rootChartProps.margin\n  }), /*#__PURE__*/React.createElement(ReportChartProps, {\n    accessibilityLayer: rootChartProps.accessibilityLayer,\n    barCategoryGap: rootChartProps.barCategoryGap,\n    maxBarSize: rootChartProps.maxBarSize,\n    stackOffset: rootChartProps.stackOffset,\n    barGap: rootChartProps.barGap,\n    barSize: rootChartProps.barSize,\n    syncId: rootChartProps.syncId,\n    syncMethod: rootChartProps.syncMethod,\n    className: rootChartProps.className\n  }), /*#__PURE__*/React.createElement(CategoricalChart, _extends({}, otherCategoricalProps, {\n    width: width,\n    height: height,\n    ref: ref\n  })));\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AACnC,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,IAAIC,aAAa,GAAG;EAClBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBC,kBAAkB,EAAE,IAAI;EACxBC,MAAM,EAAE,YAAY;EACpBC,WAAW,EAAE,MAAM;EACnBC,cAAc,EAAE,KAAK;EACrBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAEX,aAAa;EACrBY,iBAAiB,EAAE,KAAK;EACxBC,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,cAAc,GAAG,aAAatB,UAAU,CAAC,SAASsB,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACtF,IAAIC,qBAAqB;EACzB,IAAIC,cAAc,GAAGpB,mBAAmB,CAACiB,KAAK,CAACI,qBAAqB,EAAEd,YAAY,CAAC;EACnF,IAAI;MACAe,KAAK;MACLC;IACF,CAAC,GAAGH,cAAc;IAClBI,qBAAqB,GAAGtC,wBAAwB,CAACkC,cAAc,EAAEhD,SAAS,CAAC;EAC7E,IAAI,CAAC6B,gBAAgB,CAACqB,KAAK,CAAC,IAAI,CAACrB,gBAAgB,CAACsB,MAAM,CAAC,EAAE;IACzD,OAAO,IAAI;EACb;EACA,IAAI;IACFE,SAAS;IACTC,uBAAuB;IACvBC,yBAAyB;IACzBC,sBAAsB;IACtBP;EACF,CAAC,GAAGJ,KAAK;EACT,IAAIY,OAAO,GAAG;IACZJ,SAAS;IACTC,uBAAuB;IACvBC,yBAAyB;IACzBC,sBAAsB;IACtBE,YAAY,EAAEC;EAChB,CAAC;EACD,OAAO,aAAatC,KAAK,CAACuC,aAAa,CAACrC,qBAAqB,EAAE;IAC7DsC,cAAc,EAAE;MACdJ;IACF,CAAC;IACDK,cAAc,EAAE,CAACf,qBAAqB,GAAGE,qBAAqB,CAACc,EAAE,MAAM,IAAI,IAAIhB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGM;EAC5I,CAAC,EAAE,aAAahC,KAAK,CAACuC,aAAa,CAACpC,wBAAwB,EAAE;IAC5DwC,SAAS,EAAEf,qBAAqB,CAACgB;EACnC,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACuC,aAAa,CAACnC,oBAAoB,EAAE;IACzDyB,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdd,MAAM,EAAEW,cAAc,CAACX,MAAM;IAC7BI,MAAM,EAAEO,cAAc,CAACP;EACzB,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACuC,aAAa,CAAClC,gBAAgB,EAAE;IACrDU,kBAAkB,EAAEY,cAAc,CAACZ,kBAAkB;IACrDG,cAAc,EAAES,cAAc,CAACT,cAAc;IAC7C2B,UAAU,EAAElB,cAAc,CAACkB,UAAU;IACrC5B,WAAW,EAAEU,cAAc,CAACV,WAAW;IACvCE,MAAM,EAAEQ,cAAc,CAACR,MAAM;IAC7B2B,OAAO,EAAEnB,cAAc,CAACmB,OAAO;IAC/BC,MAAM,EAAEpB,cAAc,CAACoB,MAAM;IAC7BzB,UAAU,EAAEK,cAAc,CAACL,UAAU;IACrC0B,SAAS,EAAErB,cAAc,CAACqB;EAC5B,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACuC,aAAa,CAACjC,gBAAgB,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEmD,qBAAqB,EAAE;IACzFF,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdL,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
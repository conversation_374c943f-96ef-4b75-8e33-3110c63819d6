{"ast": null, "code": "function isSymbol(value) {\n  return typeof value === 'symbol' || value instanceof Symbol;\n}\nexport { isSymbol };", "map": {"version": 3, "names": ["isSymbol", "value", "Symbol"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/predicate/isSymbol.mjs"], "sourcesContent": ["function isSymbol(value) {\n    return typeof value === 'symbol' || value instanceof Symbol;\n}\n\nexport { isSymbol };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,YAAYC,MAAM;AAC/D;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function isError(value) {\n  return value instanceof Error;\n}\nexport { isError };", "map": {"version": 3, "names": ["isError", "value", "Error"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isError.mjs"], "sourcesContent": ["function isError(value) {\n    return value instanceof Error;\n}\n\nexport { isError };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpB,OAAOA,KAAK,YAAYC,KAAK;AACjC;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
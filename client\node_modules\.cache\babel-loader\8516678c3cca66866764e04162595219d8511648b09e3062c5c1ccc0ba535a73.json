{"ast": null, "code": "async function attemptAsync(func) {\n  try {\n    const result = await func();\n    return [null, result];\n  } catch (error) {\n    return [error, null];\n  }\n}\nexport { attemptAsync };", "map": {"version": 3, "names": ["attemptAsync", "func", "result", "error"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/util/attemptAsync.mjs"], "sourcesContent": ["async function attemptAsync(func) {\n    try {\n        const result = await func();\n        return [null, result];\n    }\n    catch (error) {\n        return [error, null];\n    }\n}\n\nexport { attemptAsync };\n"], "mappings": "AAAA,eAAeA,YAAYA,CAACC,IAAI,EAAE;EAC9B,IAAI;IACA,MAAMC,MAAM,GAAG,MAAMD,IAAI,CAAC,CAAC;IAC3B,OAAO,CAAC,IAAI,EAAEC,MAAM,CAAC;EACzB,CAAC,CACD,OAAOC,KAAK,EAAE;IACV,OAAO,CAACA,KAAK,EAAE,IAAI,CAAC;EACxB;AACJ;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
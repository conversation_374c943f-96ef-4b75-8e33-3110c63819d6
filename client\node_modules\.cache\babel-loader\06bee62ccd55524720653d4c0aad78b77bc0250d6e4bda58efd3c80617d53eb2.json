{"ast": null, "code": "export var defaultPolarRadiusAxisProps = {\n  allowDataOverflow: false,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  axisLine: true,\n  cx: 0,\n  cy: 0,\n  orientation: 'right',\n  radiusAxisId: 0,\n  scale: 'auto',\n  stroke: '#ccc',\n  tick: true,\n  tickCount: 5,\n  type: 'number'\n};", "map": {"version": 3, "names": ["defaultPolarRadiusAxisProps", "allowDataOverflow", "allowDuplicatedCategory", "angle", "axisLine", "cx", "cy", "orientation", "radiusAxisId", "scale", "stroke", "tick", "tickCount", "type"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/polar/defaultPolarRadiusAxisProps.js"], "sourcesContent": ["export var defaultPolarRadiusAxisProps = {\n  allowDataOverflow: false,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  axisLine: true,\n  cx: 0,\n  cy: 0,\n  orientation: 'right',\n  radiusAxisId: 0,\n  scale: 'auto',\n  stroke: '#ccc',\n  tick: true,\n  tickCount: 5,\n  type: 'number'\n};"], "mappings": "AAAA,OAAO,IAAIA,2BAA2B,GAAG;EACvCC,iBAAiB,EAAE,KAAK;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,IAAI;EACdC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,WAAW,EAAE,OAAO;EACpBC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC;EACZC,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "module.exports = require('../dist/compat/math/minBy.js').minBy;", "map": {"version": 3, "names": ["module", "exports", "require", "minBy"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/compat/minBy.js"], "sourcesContent": ["module.exports = require('../dist/compat/math/minBy.js').minBy;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
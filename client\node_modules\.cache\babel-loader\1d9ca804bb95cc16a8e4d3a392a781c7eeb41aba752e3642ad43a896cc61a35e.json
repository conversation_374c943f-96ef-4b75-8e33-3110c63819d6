{"ast": null, "code": "export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}", "map": {"version": 3, "names": ["extent", "values", "valueof", "min", "max", "undefined", "value", "index"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/d3-array/src/extent.js"], "sourcesContent": ["export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9C,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,MAAMC,KAAK,IAAIL,MAAM,EAAE;MAC1B,IAAIK,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIH,GAAG,KAAKE,SAAS,EAAE;UACrB,IAAIC,KAAK,IAAIA,KAAK,EAAEH,GAAG,GAAGC,GAAG,GAAGE,KAAK;QACvC,CAAC,MAAM;UACL,IAAIH,GAAG,GAAGG,KAAK,EAAEH,GAAG,GAAGG,KAAK;UAC5B,IAAIF,GAAG,GAAGE,KAAK,EAAEF,GAAG,GAAGE,KAAK;QAC9B;MACF;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIL,MAAM,EAAE;MACxB,IAAI,CAACK,KAAK,GAAGJ,OAAO,CAACI,KAAK,EAAE,EAAEC,KAAK,EAAEN,MAAM,CAAC,KAAK,IAAI,EAAE;QACrD,IAAIE,GAAG,KAAKE,SAAS,EAAE;UACrB,IAAIC,KAAK,IAAIA,KAAK,EAAEH,GAAG,GAAGC,GAAG,GAAGE,KAAK;QACvC,CAAC,MAAM;UACL,IAAIH,GAAG,GAAGG,KAAK,EAAEH,GAAG,GAAGG,KAAK;UAC5B,IAAIF,GAAG,GAAGE,KAAK,EAAEF,GAAG,GAAGE,KAAK;QAC9B;MACF;IACF;EACF;EACA,OAAO,CAACH,GAAG,EAAEC,GAAG,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
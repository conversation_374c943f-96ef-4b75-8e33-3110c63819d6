{"ast": null, "code": "import { words } from './words.mjs';\nfunction constantCase(str) {\n  const words$1 = words(str);\n  return words$1.map(word => word.toUpperCase()).join('_');\n}\nexport { constantCase };", "map": {"version": 3, "names": ["words", "constantCase", "str", "words$1", "map", "word", "toUpperCase", "join"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/constantCase.mjs"], "sourcesContent": ["import { words } from './words.mjs';\n\nfunction constantCase(str) {\n    const words$1 = words(str);\n    return words$1.map(word => word.toUpperCase()).join('_');\n}\n\nexport { constantCase };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,SAASC,YAAYA,CAACC,GAAG,EAAE;EACvB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,OAAOC,OAAO,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC5D;AAEA,SAASN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
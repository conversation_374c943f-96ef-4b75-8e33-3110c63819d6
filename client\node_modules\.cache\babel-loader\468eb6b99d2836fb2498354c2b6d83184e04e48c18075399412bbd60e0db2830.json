{"ast": null, "code": "function groupBy(arr, getKeyFromItem) {\n  const result = {};\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const key = getKeyFromItem(item);\n    if (!Object.hasOwn(result, key)) {\n      result[key] = [];\n    }\n    result[key].push(item);\n  }\n  return result;\n}\nexport { groupBy };", "map": {"version": 3, "names": ["groupBy", "arr", "getKeyFromItem", "result", "i", "length", "item", "key", "Object", "hasOwn", "push"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/groupBy.mjs"], "sourcesContent": ["function groupBy(arr, getKeyFromItem) {\n    const result = {};\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const key = getKeyFromItem(item);\n        if (!Object.hasOwn(result, key)) {\n            result[key] = [];\n        }\n        result[key].push(item);\n    }\n    return result;\n}\n\nexport { groupBy };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAEC,cAAc,EAAE;EAClC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,MAAMG,GAAG,GAAGL,cAAc,CAACI,IAAI,CAAC;IAChC,IAAI,CAACE,MAAM,CAACC,MAAM,CAACN,MAAM,EAAEI,GAAG,CAAC,EAAE;MAC7BJ,MAAM,CAACI,GAAG,CAAC,GAAG,EAAE;IACpB;IACAJ,MAAM,CAACI,GAAG,CAAC,CAACG,IAAI,CAACJ,IAAI,CAAC;EAC1B;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
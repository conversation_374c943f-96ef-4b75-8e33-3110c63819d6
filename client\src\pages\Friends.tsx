import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Users, UserPlus, Search, Check, X, UserMinus } from 'lucide-react';

interface Friend {
  _id: string;
  user: {
    _id: string;
    username: string;
    displayName: string;
    avatar?: string;
  };
  status: 'accepted' | 'pending' | 'blocked';
  addedAt: string;
}

interface SearchResult {
  _id: string;
  username: string;
  displayName: string;
  avatar?: string;
  relationshipStatus: 'none' | 'pending' | 'accepted' | 'blocked';
}

const Friends: React.FC = () => {
  const [friends, setFriends] = useState<Friend[]>([]);
  const [pendingRequests, setPendingRequests] = useState<Friend[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchFriends();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery.length >= 2) {
        searchUsers();
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  const fetchFriends = async () => {
    try {
      const response = await axios.get('/friends');
      setFriends(response.data.friends);
      setPendingRequests(response.data.pendingRequests);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch friends');
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async () => {
    setSearchLoading(true);
    try {
      const response = await axios.get(`/friends/search?q=${encodeURIComponent(searchQuery)}`);
      setSearchResults(response.data.users);
    } catch (err: any) {
      console.error('Search failed:', err);
    } finally {
      setSearchLoading(false);
    }
  };

  const sendFriendRequest = async (username: string) => {
    try {
      await axios.post('/friends/request', { username });
      // Update search results to reflect new status
      setSearchResults(prev => 
        prev.map(user => 
          user.username === username 
            ? { ...user, relationshipStatus: 'pending' }
            : user
        )
      );
    } catch (err: any) {
      alert(err.response?.data?.message || 'Failed to send friend request');
    }
  };

  const acceptFriendRequest = async (friendId: string) => {
    try {
      await axios.put(`/friends/${friendId}/accept`);
      fetchFriends(); // Refresh the lists
    } catch (err: any) {
      alert(err.response?.data?.message || 'Failed to accept friend request');
    }
  };

  const rejectFriendRequest = async (friendId: string) => {
    try {
      await axios.put(`/friends/${friendId}/reject`);
      fetchFriends(); // Refresh the lists
    } catch (err: any) {
      alert(err.response?.data?.message || 'Failed to reject friend request');
    }
  };

  const removeFriend = async (friendId: string) => {
    if (window.confirm('Are you sure you want to remove this friend?')) {
      try {
        await axios.delete(`/friends/${friendId}`);
        fetchFriends(); // Refresh the lists
      } catch (err: any) {
        alert(err.response?.data?.message || 'Failed to remove friend');
      }
    }
  };

  const getStatusButton = (user: SearchResult) => {
    switch (user.relationshipStatus) {
      case 'none':
        return (
          <button
            onClick={() => sendFriendRequest(user.username)}
            className="btn-primary text-sm"
          >
            <UserPlus className="h-4 w-4 mr-1" />
            Add Friend
          </button>
        );
      case 'pending':
        return (
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-md">
            Request Sent
          </span>
        );
      case 'accepted':
        return (
          <span className="text-sm text-success-600 bg-success-100 px-3 py-1 rounded-md">
            Friends
          </span>
        );
      case 'blocked':
        return (
          <span className="text-sm text-danger-600 bg-danger-100 px-3 py-1 rounded-md">
            Blocked
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Friends</h1>
        <p className="text-gray-600">Connect with friends and compete together</p>
      </div>

      {/* Search */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Find Friends</h2>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10"
            placeholder="Search by username or display name..."
          />
        </div>

        {searchLoading && (
          <div className="mt-4 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
          </div>
        )}

        {searchResults.length > 0 && (
          <div className="mt-4 space-y-2">
            {searchResults.map((user) => (
              <div key={user._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user.displayName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{user.displayName}</p>
                    <p className="text-sm text-gray-500">@{user.username}</p>
                  </div>
                </div>
                {getStatusButton(user)}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pending Requests */}
      {pendingRequests.length > 0 && (
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Pending Requests</h2>
          <div className="space-y-3">
            {pendingRequests.map((request) => (
              <div key={request._id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {request.user.displayName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{request.user.displayName}</p>
                    <p className="text-sm text-gray-500">@{request.user.username}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => acceptFriendRequest(request.user._id)}
                    className="btn-success text-sm"
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Accept
                  </button>
                  <button
                    onClick={() => rejectFriendRequest(request.user._id)}
                    className="btn-secondary text-sm"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Decline
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Friends List */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Your Friends ({friends.length})
          </h2>
          <Users className="h-5 w-5 text-gray-400" />
        </div>

        {friends.length > 0 ? (
          <div className="space-y-3">
            {friends.map((friend) => (
              <div key={friend._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {friend.user.displayName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{friend.user.displayName}</p>
                    <p className="text-sm text-gray-500">@{friend.user.username}</p>
                    <p className="text-xs text-gray-400">
                      Friends since {new Date(friend.addedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFriend(friend.user._id)}
                  className="btn-danger text-sm"
                >
                  <UserMinus className="h-4 w-4 mr-1" />
                  Remove
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <p className="text-gray-500">No friends yet</p>
            <p className="text-sm text-gray-400 mt-2">
              Search for friends above to start building your network!
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Friends;

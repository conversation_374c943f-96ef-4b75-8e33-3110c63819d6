{"ast": null, "code": "function uniqBy(arr, mapper) {\n  const map = new Map();\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const key = mapper(item);\n    if (!map.has(key)) {\n      map.set(key, item);\n    }\n  }\n  return Array.from(map.values());\n}\nexport { uniqBy };", "map": {"version": 3, "names": ["uniqBy", "arr", "mapper", "map", "Map", "i", "length", "item", "key", "has", "set", "Array", "from", "values"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/uniqBy.mjs"], "sourcesContent": ["function uniqBy(arr, mapper) {\n    const map = new Map();\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const key = mapper(item);\n        if (!map.has(key)) {\n            map.set(key, item);\n        }\n    }\n    return Array.from(map.values());\n}\n\nexport { uniqBy };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACzB,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGN,GAAG,CAACI,CAAC,CAAC;IACnB,MAAMG,GAAG,GAAGN,MAAM,CAACK,IAAI,CAAC;IACxB,IAAI,CAACJ,GAAG,CAACM,GAAG,CAACD,GAAG,CAAC,EAAE;MACfL,GAAG,CAACO,GAAG,CAACF,GAAG,EAAED,IAAI,CAAC;IACtB;EACJ;EACA,OAAOI,KAAK,CAACC,IAAI,CAACT,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC;AACnC;AAEA,SAASb,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
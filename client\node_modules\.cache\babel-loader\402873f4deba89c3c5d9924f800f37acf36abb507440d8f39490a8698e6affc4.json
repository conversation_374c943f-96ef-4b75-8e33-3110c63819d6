{"ast": null, "code": "function isBoolean(x) {\n  return typeof x === 'boolean';\n}\nexport { isBoolean };", "map": {"version": 3, "names": ["isBoolean", "x"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isBoolean.mjs"], "sourcesContent": ["function isBoolean(x) {\n    return typeof x === 'boolean';\n}\n\nexport { isBoolean };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,CAAC,EAAE;EAClB,OAAO,OAAOA,CAAC,KAAK,SAAS;AACjC;AAEA,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { uniq } from './uniq.mjs';\nfunction union(arr1, arr2) {\n  return uniq(arr1.concat(arr2));\n}\nexport { union };", "map": {"version": 3, "names": ["uniq", "union", "arr1", "arr2", "concat"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/union.mjs"], "sourcesContent": ["import { uniq } from './uniq.mjs';\n\nfunction union(arr1, arr2) {\n    return uniq(arr1.concat(arr2));\n}\n\nexport { union };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAEjC,SAASC,KAAKA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACvB,OAAOH,IAAI,CAACE,IAAI,CAACE,MAAM,CAACD,IAAI,CAAC,CAAC;AAClC;AAEA,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
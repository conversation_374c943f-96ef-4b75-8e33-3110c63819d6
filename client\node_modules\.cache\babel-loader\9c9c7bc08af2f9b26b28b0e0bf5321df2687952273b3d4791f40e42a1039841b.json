{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { updatePolarOptions } from './polarOptionsSlice';\nexport function ReportPolarOptions(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updatePolarOptions(props));\n  }, [dispatch, props]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "useAppDispatch", "updatePolarOptions", "ReportPolarOptions", "props", "dispatch"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/ReportPolarOptions.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { updatePolarOptions } from './polarOptionsSlice';\nexport function ReportPolarOptions(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(updatePolarOptions(props));\n  }, [dispatch, props]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAIC,QAAQ,GAAGJ,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACdK,QAAQ,CAACH,kBAAkB,CAACE,KAAK,CAAC,CAAC;EACrC,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { words } from './words.mjs';\nfunction startCase(str) {\n  const words$1 = words(str.trim());\n  let result = '';\n  for (let i = 0; i < words$1.length; i++) {\n    const word = words$1[i];\n    if (result) {\n      result += ' ';\n    }\n    result += word[0].toUpperCase() + word.slice(1).toLowerCase();\n  }\n  return result;\n}\nexport { startCase };", "map": {"version": 3, "names": ["words", "startCase", "str", "words$1", "trim", "result", "i", "length", "word", "toUpperCase", "slice", "toLowerCase"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/startCase.mjs"], "sourcesContent": ["import { words } from './words.mjs';\n\nfunction startCase(str) {\n    const words$1 = words(str.trim());\n    let result = '';\n    for (let i = 0; i < words$1.length; i++) {\n        const word = words$1[i];\n        if (result) {\n            result += ' ';\n        }\n        result += word[0].toUpperCase() + word.slice(1).toLowerCase();\n    }\n    return result;\n}\n\nexport { startCase };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC;EACjC,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,IAAI,GAAGL,OAAO,CAACG,CAAC,CAAC;IACvB,IAAID,MAAM,EAAE;MACRA,MAAM,IAAI,GAAG;IACjB;IACAA,MAAM,IAAIG,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGD,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACjE;EACA,OAAON,MAAM;AACjB;AAEA,SAASJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { combineReducers, configureStore } from '@reduxjs/toolkit';\nimport { optionsReducer } from './optionsSlice';\nimport { tooltipReducer } from './tooltipSlice';\nimport { chartDataReducer } from './chartDataSlice';\nimport { chartLayoutReducer } from './layoutSlice';\nimport { mouseClickMiddleware, mouseMoveMiddleware } from './mouseEventsMiddleware';\nimport { reduxDevtoolsJsonStringifyReplacer } from './reduxDevtoolsJsonStringifyReplacer';\nimport { cartesianAxisReducer } from './cartesianAxisSlice';\nimport { graphicalItemsReducer } from './graphicalItemsSlice';\nimport { referenceElementsReducer } from './referenceElementsSlice';\nimport { brushReducer } from './brushSlice';\nimport { legendReducer } from './legendSlice';\nimport { rootPropsReducer } from './rootPropsSlice';\nimport { polarAxisReducer } from './polarAxisSlice';\nimport { polarOptionsReducer } from './polarOptionsSlice';\nimport { keyboardEventsMiddleware } from './keyboardEventsMiddleware';\nimport { externalEventsMiddleware } from './externalEventsMiddleware';\nimport { touchEventMiddleware } from './touchEventsMiddleware';\nimport { errorBarReducer } from './errorBarSlice';\nvar rootReducer = combineReducers({\n  brush: brushReducer,\n  cartesianAxis: cartesianAxisReducer,\n  chartData: chartDataReducer,\n  errorBars: errorBarReducer,\n  graphicalItems: graphicalItemsReducer,\n  layout: chartLayoutReducer,\n  legend: legendReducer,\n  options: optionsReducer,\n  polarAxis: polarAxisReducer,\n  polarOptions: polarOptionsReducer,\n  referenceElements: referenceElementsReducer,\n  rootProps: rootPropsReducer,\n  tooltip: tooltipReducer\n});\nexport var createRechartsStore = function createRechartsStore(preloadedState) {\n  var chartName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Chart';\n  return configureStore({\n    reducer: rootReducer,\n    // redux-toolkit v1 types are unhappy with the preloadedState type. Remove the `as any` when bumping to v2\n    preloadedState: preloadedState,\n    // @ts-expect-error redux-toolkit v1 types are unhappy with the middleware array. Remove this comment when bumping to v2\n    middleware: getDefaultMiddleware => getDefaultMiddleware({\n      serializableCheck: false\n    }).concat([mouseClickMiddleware.middleware, mouseMoveMiddleware.middleware, keyboardEventsMiddleware.middleware, externalEventsMiddleware.middleware, touchEventMiddleware.middleware]),\n    devTools: {\n      serialize: {\n        replacer: reduxDevtoolsJsonStringifyReplacer\n      },\n      name: \"recharts-\".concat(chartName)\n    }\n  });\n};", "map": {"version": 3, "names": ["combineReducers", "configureStore", "optionsReducer", "tooltipReducer", "chartDataReducer", "chartLayoutReducer", "mouseClickMiddleware", "mouseMoveMiddleware", "reduxDevtoolsJsonStringifyReplacer", "cartesianAxisReducer", "graphicalItemsReducer", "referenceElementsReducer", "brushReducer", "legendReducer", "rootPropsReducer", "polarAxisReducer", "polarOptionsReducer", "keyboardEventsMiddleware", "externalEventsMiddleware", "touchEventMiddleware", "errorBarReducer", "rootReducer", "brush", "cartesianAxis", "chartData", "errorBars", "graphicalItems", "layout", "legend", "options", "polarAxis", "polarOptions", "referenceElements", "rootProps", "tooltip", "createRechartsStore", "preloadedState", "chartName", "arguments", "length", "undefined", "reducer", "middleware", "getDefaultMiddleware", "serializableCheck", "concat", "devTools", "serialize", "replacer", "name"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/store.js"], "sourcesContent": ["import { combineReducers, configureStore } from '@reduxjs/toolkit';\nimport { optionsReducer } from './optionsSlice';\nimport { tooltipReducer } from './tooltipSlice';\nimport { chartDataReducer } from './chartDataSlice';\nimport { chartLayoutReducer } from './layoutSlice';\nimport { mouseClickMiddleware, mouseMoveMiddleware } from './mouseEventsMiddleware';\nimport { reduxDevtoolsJsonStringifyReplacer } from './reduxDevtoolsJsonStringifyReplacer';\nimport { cartesianAxisReducer } from './cartesianAxisSlice';\nimport { graphicalItemsReducer } from './graphicalItemsSlice';\nimport { referenceElementsReducer } from './referenceElementsSlice';\nimport { brushReducer } from './brushSlice';\nimport { legendReducer } from './legendSlice';\nimport { rootPropsReducer } from './rootPropsSlice';\nimport { polarAxisReducer } from './polarAxisSlice';\nimport { polarOptionsReducer } from './polarOptionsSlice';\nimport { keyboardEventsMiddleware } from './keyboardEventsMiddleware';\nimport { externalEventsMiddleware } from './externalEventsMiddleware';\nimport { touchEventMiddleware } from './touchEventsMiddleware';\nimport { errorBarReducer } from './errorBarSlice';\nvar rootReducer = combineReducers({\n  brush: brushReducer,\n  cartesianAxis: cartesianAxisReducer,\n  chartData: chartDataReducer,\n  errorBars: errorBarReducer,\n  graphicalItems: graphicalItemsReducer,\n  layout: chartLayoutReducer,\n  legend: legendReducer,\n  options: optionsReducer,\n  polarAxis: polarAxisReducer,\n  polarOptions: polarOptionsReducer,\n  referenceElements: referenceElementsReducer,\n  rootProps: rootPropsReducer,\n  tooltip: tooltipReducer\n});\nexport var createRechartsStore = function createRechartsStore(preloadedState) {\n  var chartName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Chart';\n  return configureStore({\n    reducer: rootReducer,\n    // redux-toolkit v1 types are unhappy with the preloadedState type. Remove the `as any` when bumping to v2\n    preloadedState: preloadedState,\n    // @ts-expect-error redux-toolkit v1 types are unhappy with the middleware array. Remove this comment when bumping to v2\n    middleware: getDefaultMiddleware => getDefaultMiddleware({\n      serializableCheck: false\n    }).concat([mouseClickMiddleware.middleware, mouseMoveMiddleware.middleware, keyboardEventsMiddleware.middleware, externalEventsMiddleware.middleware, touchEventMiddleware.middleware]),\n    devTools: {\n      serialize: {\n        replacer: reduxDevtoolsJsonStringifyReplacer\n      },\n      name: \"recharts-\".concat(chartName)\n    }\n  });\n};"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,kBAAkB;AAClE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACnF,SAASC,kCAAkC,QAAQ,sCAAsC;AACzF,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,wBAAwB,QAAQ,0BAA0B;AACnE,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,aAAa,QAAQ,eAAe;AAC7C,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,eAAe,QAAQ,iBAAiB;AACjD,IAAIC,WAAW,GAAGrB,eAAe,CAAC;EAChCsB,KAAK,EAAEV,YAAY;EACnBW,aAAa,EAAEd,oBAAoB;EACnCe,SAAS,EAAEpB,gBAAgB;EAC3BqB,SAAS,EAAEL,eAAe;EAC1BM,cAAc,EAAEhB,qBAAqB;EACrCiB,MAAM,EAAEtB,kBAAkB;EAC1BuB,MAAM,EAAEf,aAAa;EACrBgB,OAAO,EAAE3B,cAAc;EACvB4B,SAAS,EAAEf,gBAAgB;EAC3BgB,YAAY,EAAEf,mBAAmB;EACjCgB,iBAAiB,EAAErB,wBAAwB;EAC3CsB,SAAS,EAAEnB,gBAAgB;EAC3BoB,OAAO,EAAE/B;AACX,CAAC,CAAC;AACF,OAAO,IAAIgC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,cAAc,EAAE;EAC5E,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO;EAC3F,OAAOrC,cAAc,CAAC;IACpBwC,OAAO,EAAEpB,WAAW;IACpB;IACAe,cAAc,EAAEA,cAAc;IAC9B;IACAM,UAAU,EAAEC,oBAAoB,IAAIA,oBAAoB,CAAC;MACvDC,iBAAiB,EAAE;IACrB,CAAC,CAAC,CAACC,MAAM,CAAC,CAACvC,oBAAoB,CAACoC,UAAU,EAAEnC,mBAAmB,CAACmC,UAAU,EAAEzB,wBAAwB,CAACyB,UAAU,EAAExB,wBAAwB,CAACwB,UAAU,EAAEvB,oBAAoB,CAACuB,UAAU,CAAC,CAAC;IACvLI,QAAQ,EAAE;MACRC,SAAS,EAAE;QACTC,QAAQ,EAAExC;MACZ,CAAC;MACDyC,IAAI,EAAE,WAAW,CAACJ,MAAM,CAACR,SAAS;IACpC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
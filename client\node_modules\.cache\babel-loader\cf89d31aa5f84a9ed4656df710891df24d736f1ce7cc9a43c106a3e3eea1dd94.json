{"ast": null, "code": "function minBy(items, getValue) {\n  if (items.length === 0) {\n    return undefined;\n  }\n  let minElement = items[0];\n  let min = getValue(minElement);\n  for (let i = 1; i < items.length; i++) {\n    const element = items[i];\n    const value = getValue(element);\n    if (value < min) {\n      min = value;\n      minElement = element;\n    }\n  }\n  return minElement;\n}\nexport { minBy };", "map": {"version": 3, "names": ["minBy", "items", "getValue", "length", "undefined", "minElement", "min", "i", "element", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/minBy.mjs"], "sourcesContent": ["function minBy(items, getValue) {\n    if (items.length === 0) {\n        return undefined;\n    }\n    let minElement = items[0];\n    let min = getValue(minElement);\n    for (let i = 1; i < items.length; i++) {\n        const element = items[i];\n        const value = getValue(element);\n        if (value < min) {\n            min = value;\n            minElement = element;\n        }\n    }\n    return minElement;\n}\n\nexport { minBy };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC5B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOC,SAAS;EACpB;EACA,IAAIC,UAAU,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACzB,IAAIK,GAAG,GAAGJ,QAAQ,CAACG,UAAU,CAAC;EAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACE,MAAM,EAAEI,CAAC,EAAE,EAAE;IACnC,MAAMC,OAAO,GAAGP,KAAK,CAACM,CAAC,CAAC;IACxB,MAAME,KAAK,GAAGP,QAAQ,CAACM,OAAO,CAAC;IAC/B,IAAIC,KAAK,GAAGH,GAAG,EAAE;MACbA,GAAG,GAAGG,KAAK;MACXJ,UAAU,GAAGG,OAAO;IACxB;EACJ;EACA,OAAOH,UAAU;AACrB;AAEA,SAASL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
[{"C:\\vibe coding\\screentime_management_app\\client\\src\\index.tsx": "1", "C:\\vibe coding\\screentime_management_app\\client\\src\\reportWebVitals.ts": "2", "C:\\vibe coding\\screentime_management_app\\client\\src\\App.tsx": "3", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Dashboard.tsx": "4", "C:\\vibe coding\\screentime_management_app\\client\\src\\contexts\\SocketContext.tsx": "5", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Friends.tsx": "6", "C:\\vibe coding\\screentime_management_app\\client\\src\\contexts\\AuthContext.tsx": "7", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Leaderboard.tsx": "8", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Login.tsx": "9", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Register.tsx": "10", "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Profile.tsx": "11", "C:\\vibe coding\\screentime_management_app\\client\\src\\components\\Auth\\ProtectedRoute.tsx": "12", "C:\\vibe coding\\screentime_management_app\\client\\src\\components\\Layout\\Navbar.tsx": "13"}, {"size": 554, "mtime": 1755014195516, "results": "14", "hashOfConfig": "15"}, {"size": 425, "mtime": 1755014194999, "results": "16", "hashOfConfig": "15"}, {"size": 1892, "mtime": 1755015214152, "results": "17", "hashOfConfig": "15"}, {"size": 10195, "mtime": 1755014510147, "results": "18", "hashOfConfig": "15"}, {"size": 2086, "mtime": 1755015189271, "results": "19", "hashOfConfig": "15"}, {"size": 10333, "mtime": 1755016673542, "results": "20", "hashOfConfig": "15"}, {"size": 4451, "mtime": 1755014374112, "results": "21", "hashOfConfig": "15"}, {"size": 9941, "mtime": 1755014555929, "results": "22", "hashOfConfig": "15"}, {"size": 4855, "mtime": 1755014434652, "results": "23", "hashOfConfig": "15"}, {"size": 7680, "mtime": 1755014461246, "results": "24", "hashOfConfig": "15"}, {"size": 16019, "mtime": 1755016694025, "results": "25", "hashOfConfig": "15"}, {"size": 654, "mtime": 1755014393201, "results": "26", "hashOfConfig": "15"}, {"size": 5707, "mtime": 1755014414983, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "n1n5d7", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\vibe coding\\screentime_management_app\\client\\src\\index.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\reportWebVitals.ts", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\App.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Dashboard.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\contexts\\SocketContext.tsx", ["67"], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Friends.tsx", ["68", "69"], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\contexts\\AuthContext.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Leaderboard.tsx", ["70"], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Login.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Register.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\pages\\Profile.tsx", ["71"], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\vibe coding\\screentime_management_app\\client\\src\\components\\Layout\\Navbar.tsx", [], [], {"ruleId": "72", "severity": 1, "message": "73", "line": 72, "column": 6, "nodeType": "74", "endLine": 72, "endColumn": 19, "suggestions": "75"}, {"ruleId": "76", "severity": 1, "message": "77", "line": 32, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 32, "endColumn": 15}, {"ruleId": "72", "severity": 1, "message": "80", "line": 48, "column": 6, "nodeType": "74", "endLine": 48, "endColumn": 19, "suggestions": "81"}, {"ruleId": "72", "severity": 1, "message": "82", "line": 36, "column": 6, "nodeType": "74", "endLine": 36, "endColumn": 14, "suggestions": "83"}, {"ruleId": "76", "severity": 1, "message": "84", "line": 4, "column": 16, "nodeType": "78", "messageId": "79", "endLine": 4, "endColumn": 24}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", "ArrayExpression", ["85"], "@typescript-eslint/no-unused-vars", "'error' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'searchUsers'. Either include it or remove the dependency array.", ["86"], "React Hook useEffect has a missing dependency: 'fetchLeaderboardData'. Either include it or remove the dependency array.", ["87"], "'Settings' is defined but never used.", {"desc": "88", "fix": "89"}, {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, "Update the dependencies array to be: [user, token, socket]", {"range": "94", "text": "95"}, "Update the dependencies array to be: [searchQuery, searchUsers]", {"range": "96", "text": "97"}, "Update the dependencies array to be: [fetchLeaderboardData, period]", {"range": "98", "text": "99"}, [1891, 1904], "[user, token, socket]", [1267, 1280], "[searchQuery, searchUsers]", [1058, 1066], "[fetchLeaderboardData, period]"]
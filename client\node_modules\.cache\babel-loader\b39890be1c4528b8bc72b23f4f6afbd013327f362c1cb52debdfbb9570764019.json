{"ast": null, "code": "import { isValidElement } from 'react';\nimport { isEventKey } from './excludeEventProps';\n\n/**\n * Determines how values are stacked:\n *\n * - `none` is the default, it adds values on top of each other. No smarts. Negative values will overlap.\n * - `expand` make it so that the values always add up to 1 - so the chart will look like a rectangle.\n * - `wiggle` and `silhouette` tries to keep the chart centered.\n * - `sign` stacks positive values above zero and negative values below zero. Similar to `none` but handles negatives.\n * - `positive` ignores all negative values, and then behaves like \\`none\\`.\n *\n * Also see https://d3js.org/d3-shape/stack#stack-offsets\n * (note that the `diverging` offset in d3 is named `sign` in recharts)\n */\n\n/**\n * @deprecated use either `CartesianLayout` or `PolarLayout` instead.\n * Mixing both charts families leads to ambiguity in the type system.\n * These two layouts share very few properties, so it is best to keep them separate.\n */\n\n/**\n * @deprecated do not use: too many properties, mixing too many concepts, cartesian and polar together, everything optional.\n */\n\n//\n// Event Handler Types -- Copied from @types/react/index.d.ts and adapted for Props.\n//\n\nvar SVGContainerPropKeys = ['viewBox', 'children'];\nvar PolyElementKeys = ['points', 'pathLength'];\n\n/** svg element types that have specific attribute filtration requirements */\n\n/** map of svg element types to unique svg attributes that belong to that element */\nexport var FilteredElementKeyMap = {\n  svg: SVGContainerPropKeys,\n  polygon: PolyElementKeys,\n  polyline: PolyElementKeys\n};\n\n/** The type of easing function to use for animations */\n\n/** Specifies the duration of animation, the unit of this option is ms. */\n\n/**\n * This object defines the offset of the chart area and width and height and brush and ... it's a bit too much information all in one.\n * We use it internally but let's not expose it to the outside world.\n * If you are looking for this information, instead import `ChartOffset` or `PlotArea` from `recharts`.\n */\n\n/**\n * The domain of axis.\n * This is the definition\n *\n * Numeric domain is always defined by an array of exactly two values, for the min and the max of the axis.\n * Categorical domain is defined as array of all possible values.\n *\n * Can be specified in many ways:\n * - array of numbers\n * - with special strings like 'dataMin' and 'dataMax'\n * - with special string math like 'dataMin - 100'\n * - with keyword 'auto'\n * - or a function\n * - array of functions\n * - or a combination of the above\n */\n\n/**\n * NumberDomain is an evaluated {@link AxisDomain}.\n * Unlike {@link AxisDomain}, it has no variety - it's a tuple of two number.\n * This is after all the keywords and functions were evaluated and what is left is [min, max].\n *\n * Know that the min, max values are not guaranteed to be nice numbers - values like -Infinity or NaN are possible.\n *\n * There are also `category` axes that have different things than numbers in their domain.\n */\n\n/** The props definition of base axis */\n\n/** Defines how ticks are placed and whether / how tick collisions are handled.\n * 'preserveStart' keeps the left tick on collision and ensures that the first tick is always shown.\n * 'preserveEnd' keeps the right tick on collision and ensures that the last tick is always shown.\n * 'preserveStartEnd' keeps the left tick on collision and ensures that the first and last ticks always show.\n * 'equidistantPreserveStart' selects a number N such that every nTh tick will be shown without collision.\n */\n\n/**\n * Ticks can be any type when the axis is the type of category.\n *\n * Ticks must be numbers when the axis is the type of number.\n */\n\nexport var adaptEventHandlers = (props, newHandler) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n  Object.keys(inputProps).forEach(key => {\n    if (isEventKey(key)) {\n      out[key] = newHandler || (e => inputProps[key](inputProps, e));\n    }\n  });\n  return out;\n};\nvar getEventHandlerOfChild = (originalHandler, data, index) => e => {\n  originalHandler(data, index, e);\n  return null;\n};\nexport var adaptEventsOfChild = (props, data, index) => {\n  if (props === null || typeof props !== 'object' && typeof props !== 'function') {\n    return null;\n  }\n  var out = null;\n  Object.keys(props).forEach(key => {\n    var item = props[key];\n    if (isEventKey(key) && typeof item === 'function') {\n      if (!out) out = {};\n      out[key] = getEventHandlerOfChild(item, data, index);\n    }\n  });\n  return out;\n};\n\n/**\n * 'axis' means that all graphical items belonging to this axis tick will be highlighted,\n * and all will be present in the tooltip.\n * Tooltip with 'axis' will display when hovering on the chart background.\n *\n * 'item' means only the one graphical item being hovered will show in the tooltip.\n * Tooltip with 'item' will display when hovering over individual graphical items.\n *\n * This is calculated internally;\n * charts have a `defaultTooltipEventType` and `validateTooltipEventTypes` options.\n *\n * Users then use <Tooltip shared={true} /> or <Tooltip shared={false} /> to control their preference,\n * and charts will then see what is allowed and what is not.\n */\n\n/**\n * These are the props we are going to pass to an `activeDot` if it is a function or a custom Component\n */\n\n/**\n * This is the type of `activeDot` prop on:\n * - Area\n * - Line\n * - Radar\n */\n\n// TODO we need two different range objects, one for polar and another for cartesian layouts\n\n/**\n * Simplified version of the MouseEvent so that we don't have to mock the whole thing in tests.\n *\n * This is meant to represent the React.MouseEvent\n * which is a wrapper on top of https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n */\n\n/**\n * Coordinates relative to the top-left corner of the chart.\n * Also include scale which means that a chart that's scaled will return the same coordinates as a chart that's not scaled.\n */", "map": {"version": 3, "names": ["isValidElement", "isEventKey", "SVGContainerPropKeys", "PolyElement<PERSON><PERSON>s", "FilteredElementKeyMap", "svg", "polygon", "polyline", "adaptEventHandlers", "props", "<PERSON><PERSON><PERSON><PERSON>", "inputProps", "out", "Object", "keys", "for<PERSON>ach", "key", "e", "getEventHandlerOfChild", "<PERSON><PERSON><PERSON><PERSON>", "data", "index", "adaptEventsOfChild", "item"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/types.js"], "sourcesContent": ["import { isValidElement } from 'react';\nimport { isEventKey } from './excludeEventProps';\n\n/**\n * Determines how values are stacked:\n *\n * - `none` is the default, it adds values on top of each other. No smarts. Negative values will overlap.\n * - `expand` make it so that the values always add up to 1 - so the chart will look like a rectangle.\n * - `wiggle` and `silhouette` tries to keep the chart centered.\n * - `sign` stacks positive values above zero and negative values below zero. Similar to `none` but handles negatives.\n * - `positive` ignores all negative values, and then behaves like \\`none\\`.\n *\n * Also see https://d3js.org/d3-shape/stack#stack-offsets\n * (note that the `diverging` offset in d3 is named `sign` in recharts)\n */\n\n/**\n * @deprecated use either `CartesianLayout` or `PolarLayout` instead.\n * Mixing both charts families leads to ambiguity in the type system.\n * These two layouts share very few properties, so it is best to keep them separate.\n */\n\n/**\n * @deprecated do not use: too many properties, mixing too many concepts, cartesian and polar together, everything optional.\n */\n\n//\n// Event Handler Types -- Copied from @types/react/index.d.ts and adapted for Props.\n//\n\nvar SVGContainerPropKeys = ['viewBox', 'children'];\nvar PolyElementKeys = ['points', 'pathLength'];\n\n/** svg element types that have specific attribute filtration requirements */\n\n/** map of svg element types to unique svg attributes that belong to that element */\nexport var FilteredElementKeyMap = {\n  svg: SVGContainerPropKeys,\n  polygon: PolyElementKeys,\n  polyline: PolyElementKeys\n};\n\n/** The type of easing function to use for animations */\n\n/** Specifies the duration of animation, the unit of this option is ms. */\n\n/**\n * This object defines the offset of the chart area and width and height and brush and ... it's a bit too much information all in one.\n * We use it internally but let's not expose it to the outside world.\n * If you are looking for this information, instead import `ChartOffset` or `PlotArea` from `recharts`.\n */\n\n/**\n * The domain of axis.\n * This is the definition\n *\n * Numeric domain is always defined by an array of exactly two values, for the min and the max of the axis.\n * Categorical domain is defined as array of all possible values.\n *\n * Can be specified in many ways:\n * - array of numbers\n * - with special strings like 'dataMin' and 'dataMax'\n * - with special string math like 'dataMin - 100'\n * - with keyword 'auto'\n * - or a function\n * - array of functions\n * - or a combination of the above\n */\n\n/**\n * NumberDomain is an evaluated {@link AxisDomain}.\n * Unlike {@link AxisDomain}, it has no variety - it's a tuple of two number.\n * This is after all the keywords and functions were evaluated and what is left is [min, max].\n *\n * Know that the min, max values are not guaranteed to be nice numbers - values like -Infinity or NaN are possible.\n *\n * There are also `category` axes that have different things than numbers in their domain.\n */\n\n/** The props definition of base axis */\n\n/** Defines how ticks are placed and whether / how tick collisions are handled.\n * 'preserveStart' keeps the left tick on collision and ensures that the first tick is always shown.\n * 'preserveEnd' keeps the right tick on collision and ensures that the last tick is always shown.\n * 'preserveStartEnd' keeps the left tick on collision and ensures that the first and last ticks always show.\n * 'equidistantPreserveStart' selects a number N such that every nTh tick will be shown without collision.\n */\n\n/**\n * Ticks can be any type when the axis is the type of category.\n *\n * Ticks must be numbers when the axis is the type of number.\n */\n\nexport var adaptEventHandlers = (props, newHandler) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n  Object.keys(inputProps).forEach(key => {\n    if (isEventKey(key)) {\n      out[key] = newHandler || (e => inputProps[key](inputProps, e));\n    }\n  });\n  return out;\n};\nvar getEventHandlerOfChild = (originalHandler, data, index) => e => {\n  originalHandler(data, index, e);\n  return null;\n};\nexport var adaptEventsOfChild = (props, data, index) => {\n  if (props === null || typeof props !== 'object' && typeof props !== 'function') {\n    return null;\n  }\n  var out = null;\n  Object.keys(props).forEach(key => {\n    var item = props[key];\n    if (isEventKey(key) && typeof item === 'function') {\n      if (!out) out = {};\n      out[key] = getEventHandlerOfChild(item, data, index);\n    }\n  });\n  return out;\n};\n\n/**\n * 'axis' means that all graphical items belonging to this axis tick will be highlighted,\n * and all will be present in the tooltip.\n * Tooltip with 'axis' will display when hovering on the chart background.\n *\n * 'item' means only the one graphical item being hovered will show in the tooltip.\n * Tooltip with 'item' will display when hovering over individual graphical items.\n *\n * This is calculated internally;\n * charts have a `defaultTooltipEventType` and `validateTooltipEventTypes` options.\n *\n * Users then use <Tooltip shared={true} /> or <Tooltip shared={false} /> to control their preference,\n * and charts will then see what is allowed and what is not.\n */\n\n/**\n * These are the props we are going to pass to an `activeDot` if it is a function or a custom Component\n */\n\n/**\n * This is the type of `activeDot` prop on:\n * - Area\n * - Line\n * - Radar\n */\n\n// TODO we need two different range objects, one for polar and another for cartesian layouts\n\n/**\n * Simplified version of the MouseEvent so that we don't have to mock the whole thing in tests.\n *\n * This is meant to represent the React.MouseEvent\n * which is a wrapper on top of https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n */\n\n/**\n * Coordinates relative to the top-left corner of the chart.\n * Also include scale which means that a chart that's scaled will return the same coordinates as a chart that's not scaled.\n */"], "mappings": "AAAA,SAASA,cAAc,QAAQ,OAAO;AACtC,SAASC,UAAU,QAAQ,qBAAqB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,oBAAoB,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC;AAClD,IAAIC,eAAe,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC;;AAE9C;;AAEA;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCC,GAAG,EAAEH,oBAAoB;EACzBI,OAAO,EAAEH,eAAe;EACxBI,QAAQ,EAAEJ;AACZ,CAAC;;AAED;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIK,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,UAAU,KAAK;EACrD,IAAI,CAACD,KAAK,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IACvE,OAAO,IAAI;EACb;EACA,IAAIE,UAAU,GAAGF,KAAK;EACtB,IAAI,aAAaT,cAAc,CAACS,KAAK,CAAC,EAAE;IACtCE,UAAU,GAAGF,KAAK,CAACA,KAAK;EAC1B;EACA,IAAI,OAAOE,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IACtE,OAAO,IAAI;EACb;EACA,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACrC,IAAIf,UAAU,CAACe,GAAG,CAAC,EAAE;MACnBJ,GAAG,CAACI,GAAG,CAAC,GAAGN,UAAU,KAAKO,CAAC,IAAIN,UAAU,CAACK,GAAG,CAAC,CAACL,UAAU,EAAEM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;EACF,OAAOL,GAAG;AACZ,CAAC;AACD,IAAIM,sBAAsB,GAAGA,CAACC,eAAe,EAAEC,IAAI,EAAEC,KAAK,KAAKJ,CAAC,IAAI;EAClEE,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAEJ,CAAC,CAAC;EAC/B,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIK,kBAAkB,GAAGA,CAACb,KAAK,EAAEW,IAAI,EAAEC,KAAK,KAAK;EACtD,IAAIZ,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,IAAIG,GAAG,GAAG,IAAI;EACdC,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;IAChC,IAAIO,IAAI,GAAGd,KAAK,CAACO,GAAG,CAAC;IACrB,IAAIf,UAAU,CAACe,GAAG,CAAC,IAAI,OAAOO,IAAI,KAAK,UAAU,EAAE;MACjD,IAAI,CAACX,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;MAClBA,GAAG,CAACI,GAAG,CAAC,GAAGE,sBAAsB,CAACK,IAAI,EAAEH,IAAI,EAAEC,KAAK,CAAC;IACtD;EACF,CAAC,CAAC;EACF,OAAOT,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
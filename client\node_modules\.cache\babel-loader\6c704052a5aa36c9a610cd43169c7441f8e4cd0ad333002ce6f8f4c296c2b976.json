{"ast": null, "code": "import { flatten } from './flatten.mjs';\nfunction flattenDeep(arr) {\n  return flatten(arr, Infinity);\n}\nexport { flattenDeep };", "map": {"version": 3, "names": ["flatten", "flattenDeep", "arr", "Infinity"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/flattenDeep.mjs"], "sourcesContent": ["import { flatten } from './flatten.mjs';\n\nfunction flattenDeep(arr) {\n    return flatten(arr, Infinity);\n}\n\nexport { flattenDeep };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AAEvC,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOF,OAAO,CAACE,GAAG,EAAEC,QAAQ,CAAC;AACjC;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
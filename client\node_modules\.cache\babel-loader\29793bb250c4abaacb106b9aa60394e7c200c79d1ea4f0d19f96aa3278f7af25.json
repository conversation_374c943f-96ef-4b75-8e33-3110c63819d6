{"ast": null, "code": "function isArray(value) {\n  return Array.isArray(value);\n}\nexport { isArray };", "map": {"version": 3, "names": ["isArray", "value", "Array"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/predicate/isArray.mjs"], "sourcesContent": ["function isArray(value) {\n    return Array.isArray(value);\n}\n\nexport { isArray };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpB,OAAOC,KAAK,CAACF,OAAO,CAACC,KAAK,CAAC;AAC/B;AAEA,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
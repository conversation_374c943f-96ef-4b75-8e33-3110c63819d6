{"ast": null, "code": "function isLength(value) {\n  return Number.isSafeInteger(value) && value >= 0;\n}\nexport { isLength };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "value", "Number", "isSafeInteger"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isLength.mjs"], "sourcesContent": ["function isLength(value) {\n    return Number.isSafeInteger(value) && value >= 0;\n}\n\nexport { isLength };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOC,MAAM,CAACC,aAAa,CAACF,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC;AACpD;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import uniqBy from 'es-toolkit/compat/uniqBy';\n\n/**\n * This is configuration option that decides how to filter for unique values only:\n *\n * - `false` means \"no filter\"\n * - `true` means \"use recharts default filter\"\n * - function means \"use return of this function as the default key\"\n */\n\nexport function getUniqPayload(payload, option, defaultUniqBy) {\n  if (option === true) {\n    return uniqBy(payload, defaultUniqBy);\n  }\n  if (typeof option === 'function') {\n    return uniqBy(payload, option);\n  }\n  return payload;\n}", "map": {"version": 3, "names": ["uniqBy", "getUniqPayload", "payload", "option", "defaultUniqBy"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/payload/getUniqPayload.js"], "sourcesContent": ["import uniqBy from 'es-toolkit/compat/uniqBy';\n\n/**\n * This is configuration option that decides how to filter for unique values only:\n *\n * - `false` means \"no filter\"\n * - `true` means \"use recharts default filter\"\n * - function means \"use return of this function as the default key\"\n */\n\nexport function getUniqPayload(payload, option, defaultUniqBy) {\n  if (option === true) {\n    return uniqBy(payload, defaultUniqBy);\n  }\n  if (typeof option === 'function') {\n    return uniqBy(payload, option);\n  }\n  return payload;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,0BAA0B;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAE;EAC7D,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOH,MAAM,CAACE,OAAO,EAAEE,aAAa,CAAC;EACvC;EACA,IAAI,OAAOD,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOH,MAAM,CAACE,OAAO,EAAEC,MAAM,CAAC;EAChC;EACA,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
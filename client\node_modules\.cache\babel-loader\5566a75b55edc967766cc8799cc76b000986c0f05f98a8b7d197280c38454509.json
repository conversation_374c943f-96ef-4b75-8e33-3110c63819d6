{"ast": null, "code": "var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { createContext, useContext, useEffect } from 'react';\nimport { addErrorBar, removeErrorBar } from '../state/errorBarSlice';\nimport { useAppDispatch } from '../state/hooks';\nimport { useGraphicalItemId } from './RegisterGraphicalItemId';\nvar noop = () => {};\nvar initialContextState = {\n  data: [],\n  xAxisId: 'xAxis-0',\n  yAxisId: 'yAxis-0',\n  dataPointFormatter: () => ({\n    x: 0,\n    y: 0,\n    value: 0\n  }),\n  errorBarOffset: 0\n};\nvar ErrorBarContext = /*#__PURE__*/createContext(initialContextState);\nexport function SetErrorBarContext(props) {\n  var {\n      children\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(ErrorBarContext.Provider, {\n    value: rest\n  }, children);\n}\nexport var useErrorBarContext = () => useContext(ErrorBarContext);\nexport function ReportErrorBarSettings(props) {\n  var dispatch = useAppDispatch();\n  var graphicalItemId = useGraphicalItemId();\n  useEffect(() => {\n    if (graphicalItemId == null) {\n      // ErrorBar outside a graphical item context does not do anything.\n      return noop;\n    }\n    var payload = {\n      itemId: graphicalItemId,\n      errorBar: props\n    };\n    dispatch(addErrorBar(payload));\n    return () => {\n      dispatch(removeErrorBar(payload));\n    };\n  }, [dispatch, graphicalItemId, props]);\n  return null;\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "React", "createContext", "useContext", "useEffect", "addErrorBar", "removeErrorBar", "useAppDispatch", "useGraphicalItemId", "noop", "initialContextState", "data", "xAxisId", "yAxisId", "dataPointFormatter", "x", "y", "value", "errorBarOffset", "ErrorBarContext", "SetErrorBarContext", "props", "children", "rest", "createElement", "Provider", "useErrorBarContext", "ReportErrorBarSettings", "dispatch", "graphicalItemId", "payload", "itemId", "errorBar"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/context/ErrorBarContext.js"], "sourcesContent": ["var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { createContext, useContext, useEffect } from 'react';\nimport { addErrorBar, removeErrorBar } from '../state/errorBarSlice';\nimport { useAppDispatch } from '../state/hooks';\nimport { useGraphicalItemId } from './RegisterGraphicalItemId';\nvar noop = () => {};\nvar initialContextState = {\n  data: [],\n  xAxisId: 'xAxis-0',\n  yAxisId: 'yAxis-0',\n  dataPointFormatter: () => ({\n    x: 0,\n    y: 0,\n    value: 0\n  }),\n  errorBarOffset: 0\n};\nvar ErrorBarContext = /*#__PURE__*/createContext(initialContextState);\nexport function SetErrorBarContext(props) {\n  var {\n      children\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(ErrorBarContext.Provider, {\n    value: rest\n  }, children);\n}\nexport var useErrorBarContext = () => useContext(ErrorBarContext);\nexport function ReportErrorBarSettings(props) {\n  var dispatch = useAppDispatch();\n  var graphicalItemId = useGraphicalItemId();\n  useEffect(() => {\n    if (graphicalItemId == null) {\n      // ErrorBar outside a graphical item context does not do anything.\n      return noop;\n    }\n    var payload = {\n      itemId: graphicalItemId,\n      errorBar: props\n    };\n    dispatch(addErrorBar(payload));\n    return () => {\n      dispatch(removeErrorBar(payload));\n    };\n  }, [dispatch, graphicalItemId, props]);\n  return null;\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,OAAO,KAAKa,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC5D,SAASC,WAAW,EAAEC,cAAc,QAAQ,wBAAwB;AACpE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,IAAIC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnB,IAAIC,mBAAmB,GAAG;EACxBC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,kBAAkB,EAAEA,CAAA,MAAO;IACzBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE;EACT,CAAC,CAAC;EACFC,cAAc,EAAE;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,aAAajB,aAAa,CAACQ,mBAAmB,CAAC;AACrE,OAAO,SAASU,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAI;MACAC;IACF,CAAC,GAAGD,KAAK;IACTE,IAAI,GAAGrC,wBAAwB,CAACmC,KAAK,EAAEpC,SAAS,CAAC;EACnD,OAAO,aAAagB,KAAK,CAACuB,aAAa,CAACL,eAAe,CAACM,QAAQ,EAAE;IAChER,KAAK,EAAEM;EACT,CAAC,EAAED,QAAQ,CAAC;AACd;AACA,OAAO,IAAII,kBAAkB,GAAGA,CAAA,KAAMvB,UAAU,CAACgB,eAAe,CAAC;AACjE,OAAO,SAASQ,sBAAsBA,CAACN,KAAK,EAAE;EAC5C,IAAIO,QAAQ,GAAGrB,cAAc,CAAC,CAAC;EAC/B,IAAIsB,eAAe,GAAGrB,kBAAkB,CAAC,CAAC;EAC1CJ,SAAS,CAAC,MAAM;IACd,IAAIyB,eAAe,IAAI,IAAI,EAAE;MAC3B;MACA,OAAOpB,IAAI;IACb;IACA,IAAIqB,OAAO,GAAG;MACZC,MAAM,EAAEF,eAAe;MACvBG,QAAQ,EAAEX;IACZ,CAAC;IACDO,QAAQ,CAACvB,WAAW,CAACyB,OAAO,CAAC,CAAC;IAC9B,OAAO,MAAM;MACXF,QAAQ,CAACtB,cAAc,CAACwB,OAAO,CAAC,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,EAAEC,eAAe,EAAER,KAAK,CAAC,CAAC;EACtC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
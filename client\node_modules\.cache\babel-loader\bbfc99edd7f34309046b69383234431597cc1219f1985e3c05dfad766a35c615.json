{"ast": null, "code": "function pickBy(obj, shouldPick) {\n  const result = {};\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = obj[key];\n    if (shouldPick(value, key)) {\n      result[key] = value;\n    }\n  }\n  return result;\n}\nexport { pickBy };", "map": {"version": 3, "names": ["pickBy", "obj", "<PERSON><PERSON><PERSON>", "result", "keys", "Object", "i", "length", "key", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/pickBy.mjs"], "sourcesContent": ["function pickBy(obj, shouldPick) {\n    const result = {};\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = obj[key];\n        if (shouldPick(value, key)) {\n            result[key] = value;\n        }\n    }\n    return result;\n}\n\nexport { pickBy };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,GAAG,EAAEC,UAAU,EAAE;EAC7B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,GAAG,CAAC;EAC7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,MAAMG,KAAK,GAAGR,GAAG,CAACO,GAAG,CAAC;IACtB,IAAIN,UAAU,CAACO,KAAK,EAAED,GAAG,CAAC,EAAE;MACxBL,MAAM,CAACK,GAAG,CAAC,GAAGC,KAAK;IACvB;EACJ;EACA,OAAON,MAAM;AACjB;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
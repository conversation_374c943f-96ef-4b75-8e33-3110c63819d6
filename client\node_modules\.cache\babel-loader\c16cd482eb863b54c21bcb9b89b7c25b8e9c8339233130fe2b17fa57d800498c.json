{"ast": null, "code": "function capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\nexport { capitalize };", "map": {"version": 3, "names": ["capitalize", "str", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/capitalize.mjs"], "sourcesContent": ["function capitalize(str) {\n    return (str.charAt(0).toUpperCase() + str.slice(1).toLowerCase());\n}\n\nexport { capitalize };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAQA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACpE;AAEA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
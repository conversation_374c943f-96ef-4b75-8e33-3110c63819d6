{"ast": null, "code": "function at(arr, indices) {\n  const result = new Array(indices.length);\n  const length = arr.length;\n  for (let i = 0; i < indices.length; i++) {\n    let index = indices[i];\n    index = Number.isInteger(index) ? index : Math.trunc(index) || 0;\n    if (index < 0) {\n      index += length;\n    }\n    result[i] = arr[index];\n  }\n  return result;\n}\nexport { at };", "map": {"version": 3, "names": ["at", "arr", "indices", "result", "Array", "length", "i", "index", "Number", "isInteger", "Math", "trunc"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/at.mjs"], "sourcesContent": ["function at(arr, indices) {\n    const result = new Array(indices.length);\n    const length = arr.length;\n    for (let i = 0; i < indices.length; i++) {\n        let index = indices[i];\n        index = Number.isInteger(index) ? index : Math.trunc(index) || 0;\n        if (index < 0) {\n            index += length;\n        }\n        result[i] = arr[index];\n    }\n    return result;\n}\n\nexport { at };\n"], "mappings": "AAAA,SAASA,EAAEA,CAACC,GAAG,EAAEC,OAAO,EAAE;EACtB,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACF,OAAO,CAACG,MAAM,CAAC;EACxC,MAAMA,MAAM,GAAGJ,GAAG,CAACI,MAAM;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAIC,KAAK,GAAGL,OAAO,CAACI,CAAC,CAAC;IACtBC,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,IAAI,CAAC;IAChE,IAAIA,KAAK,GAAG,CAAC,EAAE;MACXA,KAAK,IAAIF,MAAM;IACnB;IACAF,MAAM,CAACG,CAAC,CAAC,GAAGL,GAAG,CAACM,KAAK,CAAC;EAC1B;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASH,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
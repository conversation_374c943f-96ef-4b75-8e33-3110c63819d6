{"ast": null, "code": "function windowed(arr, size, step = 1, {\n  partialWindows = false\n} = {}) {\n  if (size <= 0 || !Number.isInteger(size)) {\n    throw new Error('Size must be a positive integer.');\n  }\n  if (step <= 0 || !Number.isInteger(step)) {\n    throw new Error('Step must be a positive integer.');\n  }\n  const result = [];\n  const end = partialWindows ? arr.length : arr.length - size + 1;\n  for (let i = 0; i < end; i += step) {\n    result.push(arr.slice(i, i + size));\n  }\n  return result;\n}\nexport { windowed };", "map": {"version": 3, "names": ["windowed", "arr", "size", "step", "partialWindows", "Number", "isInteger", "Error", "result", "end", "length", "i", "push", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/windowed.mjs"], "sourcesContent": ["function windowed(arr, size, step = 1, { partialWindows = false } = {}) {\n    if (size <= 0 || !Number.isInteger(size)) {\n        throw new Error('Size must be a positive integer.');\n    }\n    if (step <= 0 || !Number.isInteger(step)) {\n        throw new Error('Step must be a positive integer.');\n    }\n    const result = [];\n    const end = partialWindows ? arr.length : arr.length - size + 1;\n    for (let i = 0; i < end; i += step) {\n        result.push(arr.slice(i, i + size));\n    }\n    return result;\n}\n\nexport { windowed };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,GAAG,CAAC,EAAE;EAAEC,cAAc,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EACpE,IAAIF,IAAI,IAAI,CAAC,IAAI,CAACG,MAAM,CAACC,SAAS,CAACJ,IAAI,CAAC,EAAE;IACtC,MAAM,IAAIK,KAAK,CAAC,kCAAkC,CAAC;EACvD;EACA,IAAIJ,IAAI,IAAI,CAAC,IAAI,CAACE,MAAM,CAACC,SAAS,CAACH,IAAI,CAAC,EAAE;IACtC,MAAM,IAAII,KAAK,CAAC,kCAAkC,CAAC;EACvD;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,GAAG,GAAGL,cAAc,GAAGH,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,GAAGR,IAAI,GAAG,CAAC;EAC/D,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAIR,IAAI,EAAE;IAChCK,MAAM,CAACI,IAAI,CAACX,GAAG,CAACY,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAAC,CAAC;EACvC;EACA,OAAOM,MAAM;AACjB;AAEA,SAASR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { difference } from './difference.mjs';\nimport { intersection } from './intersection.mjs';\nimport { union } from './union.mjs';\nfunction xor(arr1, arr2) {\n  return difference(union(arr1, arr2), intersection(arr1, arr2));\n}\nexport { xor };", "map": {"version": 3, "names": ["difference", "intersection", "union", "xor", "arr1", "arr2"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/xor.mjs"], "sourcesContent": ["import { difference } from './difference.mjs';\nimport { intersection } from './intersection.mjs';\nimport { union } from './union.mjs';\n\nfunction xor(arr1, arr2) {\n    return difference(union(arr1, arr2), intersection(arr1, arr2));\n}\n\nexport { xor };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACrB,OAAOL,UAAU,CAACE,KAAK,CAACE,IAAI,EAAEC,IAAI,CAAC,EAAEJ,YAAY,CAACG,IAAI,EAAEC,IAAI,CAAC,CAAC;AAClE;AAEA,SAASF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
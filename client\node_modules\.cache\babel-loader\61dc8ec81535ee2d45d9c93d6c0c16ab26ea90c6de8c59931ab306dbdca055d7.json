{"ast": null, "code": "const htmlUnescapes = {\n  '&amp;': '&',\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&#39;': \"'\"\n};\nfunction unescape(str) {\n  return str.replace(/&(?:amp|lt|gt|quot|#(0+)?39);/g, match => htmlUnescapes[match] || \"'\");\n}\nexport { unescape };", "map": {"version": 3, "names": ["htmlUnescapes", "unescape", "str", "replace", "match"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/unescape.mjs"], "sourcesContent": ["const htmlUnescapes = {\n    '&amp;': '&',\n    '&lt;': '<',\n    '&gt;': '>',\n    '&quot;': '\"',\n    '&#39;': \"'\",\n};\nfunction unescape(str) {\n    return str.replace(/&(?:amp|lt|gt|quot|#(0+)?39);/g, match => htmlUnescapes[match] || \"'\");\n}\n\nexport { unescape };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClB,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,MAAM,EAAE,GAAG;EACX,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE;AACb,CAAC;AACD,SAASC,QAAQA,CAACC,GAAG,EAAE;EACnB,OAAOA,GAAG,CAACC,OAAO,CAAC,gCAAgC,EAAEC,KAAK,IAAIJ,aAAa,CAACI,KAAK,CAAC,IAAI,GAAG,CAAC;AAC9F;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
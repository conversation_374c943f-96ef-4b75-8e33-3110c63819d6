{"ast": null, "code": "function unzipWith(target, iteratee) {\n  const maxLength = Math.max(...target.map(innerArray => innerArray.length));\n  const result = new Array(maxLength);\n  for (let i = 0; i < maxLength; i++) {\n    const group = new Array(target.length);\n    for (let j = 0; j < target.length; j++) {\n      group[j] = target[j][i];\n    }\n    result[i] = iteratee(...group);\n  }\n  return result;\n}\nexport { unzipWith };", "map": {"version": 3, "names": ["unzipWith", "target", "iteratee", "max<PERSON><PERSON><PERSON>", "Math", "max", "map", "innerArray", "length", "result", "Array", "i", "group", "j"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/unzipWith.mjs"], "sourcesContent": ["function unzipWith(target, iteratee) {\n    const maxLength = Math.max(...target.map(innerArray => innerArray.length));\n    const result = new Array(maxLength);\n    for (let i = 0; i < maxLength; i++) {\n        const group = new Array(target.length);\n        for (let j = 0; j < target.length; j++) {\n            group[j] = target[j][i];\n        }\n        result[i] = iteratee(...group);\n    }\n    return result;\n}\n\nexport { unzipWith };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACjC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGJ,MAAM,CAACK,GAAG,CAACC,UAAU,IAAIA,UAAU,CAACC,MAAM,CAAC,CAAC;EAC1E,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACP,SAAS,CAAC;EACnC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,EAAEQ,CAAC,EAAE,EAAE;IAChC,MAAMC,KAAK,GAAG,IAAIF,KAAK,CAACT,MAAM,CAACO,MAAM,CAAC;IACtC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACO,MAAM,EAAEK,CAAC,EAAE,EAAE;MACpCD,KAAK,CAACC,CAAC,CAAC,GAAGZ,MAAM,CAACY,CAAC,CAAC,CAACF,CAAC,CAAC;IAC3B;IACAF,MAAM,CAACE,CAAC,CAAC,GAAGT,QAAQ,CAAC,GAAGU,KAAK,CAAC;EAClC;EACA,OAAOH,MAAM;AACjB;AAEA,SAAST,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
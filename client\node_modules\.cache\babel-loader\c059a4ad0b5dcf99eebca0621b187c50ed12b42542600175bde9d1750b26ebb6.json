{"ast": null, "code": "function isNull(x) {\n  return x === null;\n}\nexport { isNull };", "map": {"version": 3, "names": ["isNull", "x"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isNull.mjs"], "sourcesContent": ["function isNull(x) {\n    return x === null;\n}\n\nexport { isNull };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACf,OAAOA,CAAC,KAAK,IAAI;AACrB;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
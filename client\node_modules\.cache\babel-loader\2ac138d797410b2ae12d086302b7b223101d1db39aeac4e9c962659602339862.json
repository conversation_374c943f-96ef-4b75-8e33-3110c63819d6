{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks } from './tooltipSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineActiveProps, selectOrderedTooltipTicks } from './selectors';\nimport { selectPolarViewBox } from './polarAxisSelectors';\nimport { selectTooltipAxisType } from './selectTooltipAxisType';\nvar pickChartPointer = (_state, chartPointer) => chartPointer;\nexport var selectActivePropsFromChartPointer = createSelector([pickChartPointer, selectChartLayout, selectPolarViewBox, selectTooltipAxisType, selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks, selectOrderedTooltipTicks, selectChartOffsetInternal], combineActiveProps);", "map": {"version": 3, "names": ["createSelector", "selectChartLayout", "selectTooltipAxisRangeWithReverse", "selectTooltipAxisTicks", "selectChartOffsetInternal", "combineActiveProps", "selectOrderedTooltipTicks", "selectPolarViewBox", "selectTooltipAxisType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_state", "chartPointer", "selectActivePropsFromChartPointer"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/selectActivePropsFromChartPointer.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks } from './tooltipSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineActiveProps, selectOrderedTooltipTicks } from './selectors';\nimport { selectPolarViewBox } from './polarAxisSelectors';\nimport { selectTooltipAxisType } from './selectTooltipAxisType';\nvar pickChartPointer = (_state, chartPointer) => chartPointer;\nexport var selectActivePropsFromChartPointer = createSelector([pickChartPointer, selectChartLayout, selectPolarViewBox, selectTooltipAxisType, selectTooltipAxisRangeWithReverse, selectTooltipAxisTicks, selectOrderedTooltipTicks, selectChartOffsetInternal], combineActiveProps);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iCAAiC,EAAEC,sBAAsB,QAAQ,oBAAoB;AAC9F,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,aAAa;AAC3E,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,IAAIC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,YAAY,KAAKA,YAAY;AAC7D,OAAO,IAAIC,iCAAiC,GAAGZ,cAAc,CAAC,CAACS,gBAAgB,EAAER,iBAAiB,EAAEM,kBAAkB,EAAEC,qBAAqB,EAAEN,iCAAiC,EAAEC,sBAAsB,EAAEG,yBAAyB,EAAEF,yBAAyB,CAAC,EAAEC,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
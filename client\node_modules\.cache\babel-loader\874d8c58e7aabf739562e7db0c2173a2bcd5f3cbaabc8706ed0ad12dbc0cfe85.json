{"ast": null, "code": "function drop(arr, itemsCount) {\n  itemsCount = Math.max(itemsCount, 0);\n  return arr.slice(itemsCount);\n}\nexport { drop };", "map": {"version": 3, "names": ["drop", "arr", "itemsCount", "Math", "max", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/drop.mjs"], "sourcesContent": ["function drop(arr, itemsCount) {\n    itemsCount = Math.max(itemsCount, 0);\n    return arr.slice(itemsCount);\n}\n\nexport { drop };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAEC,UAAU,EAAE;EAC3BA,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EACpC,OAAOD,GAAG,CAACI,KAAK,CAACH,UAAU,CAAC;AAChC;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
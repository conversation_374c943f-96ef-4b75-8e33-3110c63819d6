{"ast": null, "code": "function lowerFirst(str) {\n  return str.substring(0, 1).toLowerCase() + str.substring(1);\n}\nexport { lowerFirst };", "map": {"version": 3, "names": ["lowerFirst", "str", "substring", "toLowerCase"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/lowerFirst.mjs"], "sourcesContent": ["function lowerFirst(str) {\n    return str.substring(0, 1).toLowerCase() + str.substring(1);\n}\n\nexport { lowerFirst };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC;AAC/D;AAEA,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
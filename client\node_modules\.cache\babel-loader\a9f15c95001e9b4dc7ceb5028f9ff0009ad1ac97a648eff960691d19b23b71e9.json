{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h.01\",\n  key: \"zekei9\"\n}], [\"path\", {\n  d: \"M2 8.82a15 15 0 0 1 20 0\",\n  key: \"dnpr2z\"\n}], [\"path\", {\n  d: \"M5 12.859a10 10 0 0 1 14 0\",\n  key: \"1x1e6c\"\n}], [\"path\", {\n  d: \"M8.5 16.429a5 5 0 0 1 7 0\",\n  key: \"1bycff\"\n}]];\nconst Wifi = createLucideIcon(\"wifi\", __iconNode);\nexport { __iconNode, Wifi as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Wifi", "createLucideIcon"], "sources": ["C:\\vibe coding\\screentime_management_app\\client\\node_modules\\lucide-react\\src\\icons\\wifi.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 20 0', key: 'dnpr2z' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 14 0', key: '1x1e6c' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n];\n\n/**\n * @component @name Wifi\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgMjAgMCIgLz4KICA8cGF0aCBkPSJNNSAxMi44NTlhMTAgMTAgMCAwIDEgMTQgMCIgLz4KICA8cGF0aCBkPSJNOC41IDE2LjQyOWE1IDUgMCAwIDEgNyAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wifi\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wifi = createLucideIcon('wifi', __iconNode);\n\nexport default Wifi;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA8BC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,EAC5D;AAaA,MAAMC,IAAA,GAAOC,gBAAA,CAAiB,QAAQJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
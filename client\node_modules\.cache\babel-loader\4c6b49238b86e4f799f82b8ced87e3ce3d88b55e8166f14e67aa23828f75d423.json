{"ast": null, "code": "function intersectionBy(firstArr, secondArr, mapper) {\n  const mappedSecondSet = new Set(secondArr.map(mapper));\n  return firstArr.filter(item => mappedSecondSet.has(mapper(item)));\n}\nexport { intersectionBy };", "map": {"version": 3, "names": ["intersectionBy", "firstArr", "secondArr", "mapper", "mappedSecondSet", "Set", "map", "filter", "item", "has"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/intersectionBy.mjs"], "sourcesContent": ["function intersectionBy(firstArr, secondArr, mapper) {\n    const mappedSecondSet = new Set(secondArr.map(mapper));\n    return firstArr.filter(item => mappedSecondSet.has(mapper(item)));\n}\n\nexport { intersectionBy };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAE;EACjD,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACH,SAAS,CAACI,GAAG,CAACH,MAAM,CAAC,CAAC;EACtD,OAAOF,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIJ,eAAe,CAACK,GAAG,CAACN,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC;AACrE;AAEA,SAASR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
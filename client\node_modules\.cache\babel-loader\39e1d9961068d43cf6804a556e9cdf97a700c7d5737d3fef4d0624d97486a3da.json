{"ast": null, "code": "function before(n, func) {\n  if (!Number.isInteger(n) || n < 0) {\n    throw new Error('n must be a non-negative integer.');\n  }\n  let counter = 0;\n  return (...args) => {\n    if (++counter < n) {\n      return func(...args);\n    }\n    return undefined;\n  };\n}\nexport { before };", "map": {"version": 3, "names": ["before", "n", "func", "Number", "isInteger", "Error", "counter", "args", "undefined"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/before.mjs"], "sourcesContent": ["function before(n, func) {\n    if (!Number.isInteger(n) || n < 0) {\n        throw new Error('n must be a non-negative integer.');\n    }\n    let counter = 0;\n    return (...args) => {\n        if (++counter < n) {\n            return func(...args);\n        }\n        return undefined;\n    };\n}\n\nexport { before };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACrB,IAAI,CAACC,MAAM,CAACC,SAAS,CAACH,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC/B,MAAM,IAAII,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACA,IAAIC,OAAO,GAAG,CAAC;EACf,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChB,IAAI,EAAED,OAAO,GAAGL,CAAC,EAAE;MACf,OAAOC,IAAI,CAAC,GAAGK,IAAI,CAAC;IACxB;IACA,OAAOC,SAAS;EACpB,CAAC;AACL;AAEA,SAASR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
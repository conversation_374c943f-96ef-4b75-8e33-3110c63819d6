{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isMatchWith = require('./isMatchWith.js');\nfunction isMatch(target, source) {\n  return isMatchWith.isMatchWith(target, source, () => undefined);\n}\nexports.isMatch = isMatch;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isMatchWith", "require", "isMatch", "target", "source", "undefined"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/predicate/isMatch.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isMatchWith = require('./isMatchWith.js');\n\nfunction isMatch(target, source) {\n    return isMatchWith.isMatchWith(target, source, () => undefined);\n}\n\nexports.isMatch = isMatch;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,WAAW,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE/C,SAASC,OAAOA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC7B,OAAOJ,WAAW,CAACA,WAAW,CAACG,MAAM,EAAEC,MAAM,EAAE,MAAMC,SAAS,CAAC;AACnE;AAEAT,OAAO,CAACM,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
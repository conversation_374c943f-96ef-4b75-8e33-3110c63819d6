{"ast": null, "code": "function attempt(func) {\n  try {\n    return [null, func()];\n  } catch (error) {\n    return [error, null];\n  }\n}\nexport { attempt };", "map": {"version": 3, "names": ["attempt", "func", "error"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/util/attempt.mjs"], "sourcesContent": ["function attempt(func) {\n    try {\n        return [null, func()];\n    }\n    catch (error) {\n        return [error, null];\n    }\n}\n\nexport { attempt };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnB,IAAI;IACA,OAAO,CAAC,IAAI,EAAEA,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC,CACD,OAAOC,KAAK,EAAE;IACV,OAAO,CAACA,KAAK,EAAE,IAAI,CAAC;EACxB;AACJ;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
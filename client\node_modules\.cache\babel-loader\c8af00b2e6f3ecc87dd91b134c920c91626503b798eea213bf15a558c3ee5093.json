{"ast": null, "code": "function isJSON(value) {\n  if (typeof value !== 'string') {\n    return false;\n  }\n  try {\n    JSON.parse(value);\n    return true;\n  } catch {\n    return false;\n  }\n}\nexport { isJSON };", "map": {"version": 3, "names": ["isJSON", "value", "JSON", "parse"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isJSON.mjs"], "sourcesContent": ["function isJSON(value) {\n    if (typeof value !== 'string') {\n        return false;\n    }\n    try {\n        JSON.parse(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n\nexport { isJSON };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,KAAK,EAAE;EACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAO,KAAK;EAChB;EACA,IAAI;IACAC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;IACjB,OAAO,IAAI;EACf,CAAC,CACD,MAAM;IACF,OAAO,KAAK;EAChB;AACJ;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { useAppSelector } from '../hooks';\nimport { calculateActiveTickIndex, calculateTooltipPos, getActiveCoordinate, inRange } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectTooltipAxisTicks, selectTooltipDisplayedData } from './tooltipSelectors';\nimport { selectChartName } from './rootPropsSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nimport { selectTooltipAxis } from './selectTooltipAxis';\nexport var useChartName = () => {\n  return useAppSelector(selectChartName);\n};\nvar pickTooltipEventType = (_state, tooltipEventType) => tooltipEventType;\nvar pickTrigger = (_state, _tooltipEventType, trigger) => trigger;\nvar pickDefaultIndex = (_state, _tooltipEventType, _trigger, defaultIndex) => defaultIndex;\nexport var selectOrderedTooltipTicks = createSelector(selectTooltipAxisTicks, ticks => sortBy(ticks, o => o.coordinate));\nexport var selectTooltipInteractionState = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectTooltipDataKey = (state, tooltipEventType, trigger) => {\n  if (tooltipEventType == null) {\n    return undefined;\n  }\n  var tooltipState = selectTooltipState(state);\n  if (tooltipEventType === 'axis') {\n    if (trigger === 'hover') {\n      return tooltipState.axisInteraction.hover.dataKey;\n    }\n    return tooltipState.axisInteraction.click.dataKey;\n  }\n  if (trigger === 'hover') {\n    return tooltipState.itemInteraction.hover.dataKey;\n  }\n  return tooltipState.itemInteraction.click.dataKey;\n};\nexport var selectTooltipPayloadConfigurations = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipPayloadConfigurations);\nexport var selectCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, pickDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveCoordinate = createSelector([selectTooltipInteractionState, selectCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  var _tooltipInteractionSt;\n  return (_tooltipInteractionSt = tooltipInteractionState.coordinate) !== null && _tooltipInteractionSt !== void 0 ? _tooltipInteractionSt : defaultIndexCoordinate;\n});\nexport var selectActiveLabel = createSelector(selectTooltipAxisTicks, selectActiveIndex, combineActiveLabel);\nexport var selectTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, pickTooltipEventType], combineTooltipPayload);\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => {\n  return {\n    isActive: tooltipInteractionState.active,\n    activeIndex: tooltipInteractionState.index\n  };\n});\nexport var combineActiveProps = (chartEvent, layout, polarViewBox, tooltipAxisType, tooltipAxisRange, tooltipTicks, orderedTooltipTicks, offset) => {\n  if (!chartEvent || !layout || !tooltipAxisType || !tooltipAxisRange || !tooltipTicks) {\n    return undefined;\n  }\n  var rangeObj = inRange(chartEvent.chartX, chartEvent.chartY, layout, polarViewBox, offset);\n  if (!rangeObj) {\n    return undefined;\n  }\n  var pos = calculateTooltipPos(rangeObj, layout);\n  var activeIndex = calculateActiveTickIndex(pos, orderedTooltipTicks, tooltipTicks, tooltipAxisType, tooltipAxisRange);\n  var activeCoordinate = getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj);\n  return {\n    activeIndex: String(activeIndex),\n    activeCoordinate\n  };\n};", "map": {"version": 3, "names": ["createSelector", "sortBy", "useAppSelector", "calculateActiveTickIndex", "calculateTooltipPos", "getActiveCoordinate", "inRange", "selectChartDataWithIndexes", "selectTooltipAxisTicks", "selectTooltipDisplayedData", "selectChartName", "selectChartLayout", "selectChartOffsetInternal", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "combineActiveLabel", "combineTooltipInteractionState", "combineActiveTooltipIndex", "combineCoordinateForDefaultIndex", "combineTooltipPayloadConfigurations", "selectTooltipPayloadSearcher", "selectTooltipState", "combineTooltipPayload", "selectTooltipAxis", "useChartName", "pickTooltipEventType", "_state", "tooltipEventType", "pickTrigger", "_tooltipEventType", "trigger", "pickDefaultIndex", "_trigger", "defaultIndex", "selectOrderedTooltipTicks", "ticks", "o", "coordinate", "selectTooltipInteractionState", "selectActiveIndex", "selectTooltipDataKey", "state", "undefined", "tooltipState", "axisInteraction", "hover", "dataKey", "click", "itemInteraction", "selectTooltipPayloadConfigurations", "selectCoordinateForDefaultIndex", "selectActiveCoordinate", "tooltipInteractionState", "defaultIndexCoordinate", "_tooltipInteractionSt", "selectActiveLabel", "selectTooltipPayload", "selectIsTooltipActive", "isActive", "active", "activeIndex", "index", "combineActiveProps", "chartEvent", "layout", "polarViewBox", "tooltipAxisType", "tooltipAxisRange", "tooltipTicks", "orderedTooltipTicks", "offset", "rangeObj", "chartX", "chartY", "pos", "activeCoordinate", "String"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/selectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { useAppSelector } from '../hooks';\nimport { calculateActiveTickIndex, calculateTooltipPos, getActiveCoordinate, inRange } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectTooltipAxisTicks, selectTooltipDisplayedData } from './tooltipSelectors';\nimport { selectChartName } from './rootPropsSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nimport { selectTooltipAxis } from './selectTooltipAxis';\nexport var useChartName = () => {\n  return useAppSelector(selectChartName);\n};\nvar pickTooltipEventType = (_state, tooltipEventType) => tooltipEventType;\nvar pickTrigger = (_state, _tooltipEventType, trigger) => trigger;\nvar pickDefaultIndex = (_state, _tooltipEventType, _trigger, defaultIndex) => defaultIndex;\nexport var selectOrderedTooltipTicks = createSelector(selectTooltipAxisTicks, ticks => sortBy(ticks, o => o.coordinate));\nexport var selectTooltipInteractionState = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectTooltipDataKey = (state, tooltipEventType, trigger) => {\n  if (tooltipEventType == null) {\n    return undefined;\n  }\n  var tooltipState = selectTooltipState(state);\n  if (tooltipEventType === 'axis') {\n    if (trigger === 'hover') {\n      return tooltipState.axisInteraction.hover.dataKey;\n    }\n    return tooltipState.axisInteraction.click.dataKey;\n  }\n  if (trigger === 'hover') {\n    return tooltipState.itemInteraction.hover.dataKey;\n  }\n  return tooltipState.itemInteraction.click.dataKey;\n};\nexport var selectTooltipPayloadConfigurations = createSelector([selectTooltipState, pickTooltipEventType, pickTrigger, pickDefaultIndex], combineTooltipPayloadConfigurations);\nexport var selectCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, pickDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveCoordinate = createSelector([selectTooltipInteractionState, selectCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  var _tooltipInteractionSt;\n  return (_tooltipInteractionSt = tooltipInteractionState.coordinate) !== null && _tooltipInteractionSt !== void 0 ? _tooltipInteractionSt : defaultIndexCoordinate;\n});\nexport var selectActiveLabel = createSelector(selectTooltipAxisTicks, selectActiveIndex, combineActiveLabel);\nexport var selectTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, pickTooltipEventType], combineTooltipPayload);\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => {\n  return {\n    isActive: tooltipInteractionState.active,\n    activeIndex: tooltipInteractionState.index\n  };\n});\nexport var combineActiveProps = (chartEvent, layout, polarViewBox, tooltipAxisType, tooltipAxisRange, tooltipTicks, orderedTooltipTicks, offset) => {\n  if (!chartEvent || !layout || !tooltipAxisType || !tooltipAxisRange || !tooltipTicks) {\n    return undefined;\n  }\n  var rangeObj = inRange(chartEvent.chartX, chartEvent.chartY, layout, polarViewBox, offset);\n  if (!rangeObj) {\n    return undefined;\n  }\n  var pos = calculateTooltipPos(rangeObj, layout);\n  var activeIndex = calculateActiveTickIndex(pos, orderedTooltipTicks, tooltipTicks, tooltipAxisType, tooltipAxisRange);\n  var activeCoordinate = getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj);\n  return {\n    activeIndex: String(activeIndex),\n    activeCoordinate\n  };\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,wBAAwB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,OAAO,QAAQ,uBAAuB;AACnH,SAASC,0BAA0B,QAAQ,iBAAiB;AAC5D,SAASC,sBAAsB,EAAEC,0BAA0B,QAAQ,oBAAoB;AACvF,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,gCAAgC,QAAQ,8CAA8C;AAC/F,SAASC,mCAAmC,QAAQ,iDAAiD;AACrG,SAASC,4BAA4B,QAAQ,gCAAgC;AAC7E,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAO,IAAIC,YAAY,GAAGA,CAAA,KAAM;EAC9B,OAAOtB,cAAc,CAACQ,eAAe,CAAC;AACxC,CAAC;AACD,IAAIe,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,gBAAgB,KAAKA,gBAAgB;AACzE,IAAIC,WAAW,GAAGA,CAACF,MAAM,EAAEG,iBAAiB,EAAEC,OAAO,KAAKA,OAAO;AACjE,IAAIC,gBAAgB,GAAGA,CAACL,MAAM,EAAEG,iBAAiB,EAAEG,QAAQ,EAAEC,YAAY,KAAKA,YAAY;AAC1F,OAAO,IAAIC,yBAAyB,GAAGlC,cAAc,CAACQ,sBAAsB,EAAE2B,KAAK,IAAIlC,MAAM,CAACkC,KAAK,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC;AACxH,OAAO,IAAIC,6BAA6B,GAAGtC,cAAc,CAAC,CAACqB,kBAAkB,EAAEI,oBAAoB,EAAEG,WAAW,EAAEG,gBAAgB,CAAC,EAAEf,8BAA8B,CAAC;AACpK,OAAO,IAAIuB,iBAAiB,GAAGvC,cAAc,CAAC,CAACsC,6BAA6B,EAAE7B,0BAA0B,CAAC,EAAEQ,yBAAyB,CAAC;AACrI,OAAO,IAAIuB,oBAAoB,GAAGA,CAACC,KAAK,EAAEd,gBAAgB,EAAEG,OAAO,KAAK;EACtE,IAAIH,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAOe,SAAS;EAClB;EACA,IAAIC,YAAY,GAAGtB,kBAAkB,CAACoB,KAAK,CAAC;EAC5C,IAAId,gBAAgB,KAAK,MAAM,EAAE;IAC/B,IAAIG,OAAO,KAAK,OAAO,EAAE;MACvB,OAAOa,YAAY,CAACC,eAAe,CAACC,KAAK,CAACC,OAAO;IACnD;IACA,OAAOH,YAAY,CAACC,eAAe,CAACG,KAAK,CAACD,OAAO;EACnD;EACA,IAAIhB,OAAO,KAAK,OAAO,EAAE;IACvB,OAAOa,YAAY,CAACK,eAAe,CAACH,KAAK,CAACC,OAAO;EACnD;EACA,OAAOH,YAAY,CAACK,eAAe,CAACD,KAAK,CAACD,OAAO;AACnD,CAAC;AACD,OAAO,IAAIG,kCAAkC,GAAGjD,cAAc,CAAC,CAACqB,kBAAkB,EAAEI,oBAAoB,EAAEG,WAAW,EAAEG,gBAAgB,CAAC,EAAEZ,mCAAmC,CAAC;AAC9K,OAAO,IAAI+B,+BAA+B,GAAGlD,cAAc,CAAC,CAACc,gBAAgB,EAAED,iBAAiB,EAAEF,iBAAiB,EAAEC,yBAAyB,EAAEJ,sBAAsB,EAAEuB,gBAAgB,EAAEkB,kCAAkC,EAAE7B,4BAA4B,CAAC,EAAEF,gCAAgC,CAAC;AAC9R,OAAO,IAAIiC,sBAAsB,GAAGnD,cAAc,CAAC,CAACsC,6BAA6B,EAAEY,+BAA+B,CAAC,EAAE,CAACE,uBAAuB,EAAEC,sBAAsB,KAAK;EACxK,IAAIC,qBAAqB;EACzB,OAAO,CAACA,qBAAqB,GAAGF,uBAAuB,CAACf,UAAU,MAAM,IAAI,IAAIiB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,sBAAsB;AACnK,CAAC,CAAC;AACF,OAAO,IAAIE,iBAAiB,GAAGvD,cAAc,CAACQ,sBAAsB,EAAE+B,iBAAiB,EAAExB,kBAAkB,CAAC;AAC5G,OAAO,IAAIyC,oBAAoB,GAAGxD,cAAc,CAAC,CAACiD,kCAAkC,EAAEV,iBAAiB,EAAEhC,0BAA0B,EAAEgB,iBAAiB,EAAEgC,iBAAiB,EAAEnC,4BAA4B,EAAEK,oBAAoB,CAAC,EAAEH,qBAAqB,CAAC;AACtP,OAAO,IAAImC,qBAAqB,GAAGzD,cAAc,CAAC,CAACsC,6BAA6B,CAAC,EAAEc,uBAAuB,IAAI;EAC5G,OAAO;IACLM,QAAQ,EAAEN,uBAAuB,CAACO,MAAM;IACxCC,WAAW,EAAER,uBAAuB,CAACS;EACvC,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,MAAM,KAAK;EAClJ,IAAI,CAACP,UAAU,IAAI,CAACC,MAAM,IAAI,CAACE,eAAe,IAAI,CAACC,gBAAgB,IAAI,CAACC,YAAY,EAAE;IACpF,OAAO1B,SAAS;EAClB;EACA,IAAI6B,QAAQ,GAAGjE,OAAO,CAACyD,UAAU,CAACS,MAAM,EAAET,UAAU,CAACU,MAAM,EAAET,MAAM,EAAEC,YAAY,EAAEK,MAAM,CAAC;EAC1F,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO7B,SAAS;EAClB;EACA,IAAIgC,GAAG,GAAGtE,mBAAmB,CAACmE,QAAQ,EAAEP,MAAM,CAAC;EAC/C,IAAIJ,WAAW,GAAGzD,wBAAwB,CAACuE,GAAG,EAAEL,mBAAmB,EAAED,YAAY,EAAEF,eAAe,EAAEC,gBAAgB,CAAC;EACrH,IAAIQ,gBAAgB,GAAGtE,mBAAmB,CAAC2D,MAAM,EAAEI,YAAY,EAAER,WAAW,EAAEW,QAAQ,CAAC;EACvF,OAAO;IACLX,WAAW,EAAEgB,MAAM,CAAChB,WAAW,CAAC;IAChCe;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
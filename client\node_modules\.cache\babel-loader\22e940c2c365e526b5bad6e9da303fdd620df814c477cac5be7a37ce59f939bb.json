{"ast": null, "code": "import { difference } from './difference.mjs';\nfunction without(array, ...values) {\n  return difference(array, values);\n}\nexport { without };", "map": {"version": 3, "names": ["difference", "without", "array", "values"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/without.mjs"], "sourcesContent": ["import { difference } from './difference.mjs';\n\nfunction without(array, ...values) {\n    return difference(array, values);\n}\n\nexport { without };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,OAAOA,CAACC,KAAK,EAAE,GAAGC,MAAM,EAAE;EAC/B,OAAOH,UAAU,CAACE,KAAK,EAAEC,MAAM,CAAC;AACpC;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
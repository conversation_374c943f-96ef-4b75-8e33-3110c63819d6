{"ast": null, "code": "function mapKeys(object, getNew<PERSON>ey) {\n  const result = {};\n  const keys = Object.keys(object);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = object[key];\n    result[getNew<PERSON>ey(value, key, object)] = value;\n  }\n  return result;\n}\nexport { mapKeys };", "map": {"version": 3, "names": ["mapKeys", "object", "get<PERSON>ew<PERSON>ey", "result", "keys", "Object", "i", "length", "key", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/mapKeys.mjs"], "sourcesContent": ["function mapKeys(object, getNew<PERSON>ey) {\n    const result = {};\n    const keys = Object.keys(object);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = object[key];\n        result[getNew<PERSON>ey(value, key, object)] = value;\n    }\n    return result;\n}\n\nexport { mapKeys };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAChC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,MAAM,CAAC;EAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,MAAMG,KAAK,GAAGR,MAAM,CAACO,GAAG,CAAC;IACzBL,MAAM,CAACD,SAAS,CAACO,KAAK,EAAED,GAAG,EAAEP,MAAM,CAAC,CAAC,GAAGQ,KAAK;EACjD;EACA,OAAON,MAAM;AACjB;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isObjectLike(value) {\n  return typeof value === 'object' && value !== null;\n}\nexports.isObjectLike = isObjectLike;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isObjectLike"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/predicate/isObjectLike.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\n\nexports.isObjectLike = isObjectLike;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,YAAYA,CAACD,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD;AAEAH,OAAO,CAACI,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "function curry(func) {\n  if (func.length === 0 || func.length === 1) {\n    return func;\n  }\n  return function (arg) {\n    return makeCurry(func, func.length, [arg]);\n  };\n}\nfunction makeCurry(origin, argsLength, args) {\n  if (args.length === argsLength) {\n    return origin(...args);\n  } else {\n    const next = function (arg) {\n      return makeCurry(origin, argsLength, [...args, arg]);\n    };\n    return next;\n  }\n}\nexport { curry };", "map": {"version": 3, "names": ["curry", "func", "length", "arg", "<PERSON><PERSON><PERSON><PERSON>", "origin", "arg<PERSON><PERSON><PERSON><PERSON>", "args", "next"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/curry.mjs"], "sourcesContent": ["function curry(func) {\n    if (func.length === 0 || func.length === 1) {\n        return func;\n    }\n    return function (arg) {\n        return makeCurry(func, func.length, [arg]);\n    };\n}\nfunction makeCurry(origin, argsLength, args) {\n    if (args.length === argsLength) {\n        return origin(...args);\n    }\n    else {\n        const next = function (arg) {\n            return makeCurry(origin, argsLength, [...args, arg]);\n        };\n        return next;\n    }\n}\n\nexport { curry };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACjB,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAID,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACxC,OAAOD,IAAI;EACf;EACA,OAAO,UAAUE,GAAG,EAAE;IAClB,OAAOC,SAAS,CAACH,IAAI,EAAEA,IAAI,CAACC,MAAM,EAAE,CAACC,GAAG,CAAC,CAAC;EAC9C,CAAC;AACL;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAE;EACzC,IAAIA,IAAI,CAACL,MAAM,KAAKI,UAAU,EAAE;IAC5B,OAAOD,MAAM,CAAC,GAAGE,IAAI,CAAC;EAC1B,CAAC,MACI;IACD,MAAMC,IAAI,GAAG,SAAAA,CAAUL,GAAG,EAAE;MACxB,OAAOC,SAAS,CAACC,MAAM,EAAEC,UAAU,EAAE,CAAC,GAAGC,IAAI,EAAEJ,GAAG,CAAC,CAAC;IACxD,CAAC;IACD,OAAOK,IAAI;EACf;AACJ;AAEA,SAASR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
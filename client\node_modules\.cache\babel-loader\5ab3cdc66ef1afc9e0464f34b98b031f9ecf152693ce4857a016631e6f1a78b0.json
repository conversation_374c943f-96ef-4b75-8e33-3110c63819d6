{"ast": null, "code": "import { words } from './words.mjs';\nfunction kebabCase(str) {\n  const words$1 = words(str);\n  return words$1.map(word => word.toLowerCase()).join('-');\n}\nexport { kebabCase };", "map": {"version": 3, "names": ["words", "kebabCase", "str", "words$1", "map", "word", "toLowerCase", "join"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/kebabCase.mjs"], "sourcesContent": ["import { words } from './words.mjs';\n\nfunction kebabCase(str) {\n    const words$1 = words(str);\n    return words$1.map(word => word.toLowerCase()).join('-');\n}\n\nexport { kebabCase };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,OAAOC,OAAO,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC5D;AAEA,SAASN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
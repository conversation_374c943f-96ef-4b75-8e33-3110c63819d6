{"ast": null, "code": "function inRange(value, minimum, maximum) {\n  if (maximum == null) {\n    maximum = minimum;\n    minimum = 0;\n  }\n  if (minimum >= maximum) {\n    throw new Error('The maximum value must be greater than the minimum value.');\n  }\n  return minimum <= value && value < maximum;\n}\nexport { inRange };", "map": {"version": 3, "names": ["inRange", "value", "minimum", "maximum", "Error"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/math/inRange.mjs"], "sourcesContent": ["function inRange(value, minimum, maximum) {\n    if (maximum == null) {\n        maximum = minimum;\n        minimum = 0;\n    }\n    if (minimum >= maximum) {\n        throw new Error('The maximum value must be greater than the minimum value.');\n    }\n    return minimum <= value && value < maximum;\n}\n\nexport { inRange };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtC,IAAIA,OAAO,IAAI,IAAI,EAAE;IACjBA,OAAO,GAAGD,OAAO;IACjBA,OAAO,GAAG,CAAC;EACf;EACA,IAAIA,OAAO,IAAIC,OAAO,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;EAChF;EACA,OAAOF,OAAO,IAAID,KAAK,IAAIA,KAAK,GAAGE,OAAO;AAC9C;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
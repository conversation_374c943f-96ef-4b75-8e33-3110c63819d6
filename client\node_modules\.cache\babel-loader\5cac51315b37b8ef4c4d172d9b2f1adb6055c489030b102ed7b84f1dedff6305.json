{"ast": null, "code": "/**\n * Returns identifier for stack series which is one individual graphical item in the stack.\n * @param graphicalItem - The graphical item representing the series in the stack.\n * @return The identifier for the series in the stack\n */\nexport function getStackSeriesIdentifier(graphicalItem) {\n  return graphicalItem === null || graphicalItem === void 0 ? void 0 : graphicalItem.id;\n}", "map": {"version": 3, "names": ["getStackSeriesIdentifier", "graphicalItem", "id"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/stacks/getStackSeriesIdentifier.js"], "sourcesContent": ["/**\n * Returns identifier for stack series which is one individual graphical item in the stack.\n * @param graphicalItem - The graphical item representing the series in the stack.\n * @return The identifier for the series in the stack\n */\nexport function getStackSeriesIdentifier(graphicalItem) {\n  return graphicalItem === null || graphicalItem === void 0 ? void 0 : graphicalItem.id;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,wBAAwBA,CAACC,aAAa,EAAE;EACtD,OAAOA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,EAAE;AACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
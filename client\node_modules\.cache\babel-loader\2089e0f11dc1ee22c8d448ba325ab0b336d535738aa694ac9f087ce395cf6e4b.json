{"ast": null, "code": "function isObjectLike(value) {\n  return typeof value === 'object' && value !== null;\n}\nexport { isObjectLike };", "map": {"version": 3, "names": ["isObjectLike", "value"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs"], "sourcesContent": ["function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\n\nexport { isObjectLike };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
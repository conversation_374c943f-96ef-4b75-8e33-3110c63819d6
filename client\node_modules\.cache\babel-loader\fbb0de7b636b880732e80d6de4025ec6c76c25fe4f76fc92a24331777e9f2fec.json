{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = string => string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = string => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = props => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "toCamelCase", "match", "p1", "p2", "toUpperCase", "toPascalCase", "camelCase", "char<PERSON>t", "slice", "mergeClasses", "classes", "filter", "className", "index", "array", "Boolean", "trim", "indexOf", "join", "hasA11yProp", "props", "prop", "startsWith"], "sources": ["C:\\vibe coding\\screentime_management_app\\client\\node_modules\\shared\\src\\utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Is empty string\n *\n * @param {unknown} value\n * @returns {boolean} Whether the value is an empty string\n */\nexport const isEmptyString = (value: unknown): boolean => value === '';\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "mappings": ";;;;;;;AAQO,MAAMA,WAAA,GAAeC,MAAA,IAC1BA,MAAA,CAAOC,OAAA,CAAQ,sBAAsB,OAAO,EAAEC,WAAA;AAQzC,MAAMC,WAAA,GAAiCH,MAAA,IAC5CA,MAAA,CAAOC,OAAA,CAAQ,yBAAyB,CAACG,KAAA,EAAOC,EAAA,EAAIC,EAAA,KAClDA,EAAA,GAAKA,EAAA,CAAGC,WAAA,EAAY,GAAIF,EAAA,CAAGH,WAAA,EAC7B;AAQK,MAAMM,YAAA,GAAkCR,MAAA,IAAgC;EAC7E,MAAMS,SAAA,GAAYN,WAAA,CAAYH,MAAM;EAEpC,OAAQS,SAAA,CAAUC,MAAA,CAAO,CAAC,EAAEH,WAAA,EAAY,GAAIE,SAAA,CAAUE,KAAA,CAAM,CAAC;AAC/D;AAQO,MAAMC,YAAA,GAAeA,CAAA,GAA2CC,OAAA,KACrEA,OAAA,CACGC,MAAA,CAAO,CAACC,SAAA,EAAWC,KAAA,EAAOC,KAAA,KAAU;EACnC,OACEC,OAAA,CAAQH,SAAS,KAChBA,SAAA,CAAqBI,IAAA,OAAW,MACjCF,KAAA,CAAMG,OAAA,CAAQL,SAAS,MAAMC,KAAA;AAEjC,CAAC,EACAK,IAAA,CAAK,GAAG,EACRF,IAAA;AAgBE,MAAMG,WAAA,GAAeC,KAAA,IAA+B;EACzD,WAAWC,IAAA,IAAQD,KAAA,EAAO;IACxB,IAAIC,IAAA,CAAKC,UAAA,CAAW,OAAO,KAAKD,IAAA,KAAS,UAAUA,IAAA,KAAS,SAAS;MACnE,OAAO;IACT;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
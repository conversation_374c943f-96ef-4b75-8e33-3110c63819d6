{"ast": null, "code": "function omit(obj, keys) {\n  const result = {\n    ...obj\n  };\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    delete result[key];\n  }\n  return result;\n}\nexport { omit };", "map": {"version": 3, "names": ["omit", "obj", "keys", "result", "i", "length", "key"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/omit.mjs"], "sourcesContent": ["function omit(obj, keys) {\n    const result = { ...obj };\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        delete result[key];\n    }\n    return result;\n}\n\nexport { omit };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACrB,MAAMC,MAAM,GAAG;IAAE,GAAGF;EAAI,CAAC;EACzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,OAAOD,MAAM,CAACG,GAAG,CAAC;EACtB;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
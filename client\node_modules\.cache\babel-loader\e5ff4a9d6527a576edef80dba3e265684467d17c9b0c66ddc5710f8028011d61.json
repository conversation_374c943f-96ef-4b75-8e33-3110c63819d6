{"ast": null, "code": "import { toFinite } from './toFinite.mjs';\nfunction toInteger(value) {\n  const finite = toFinite(value);\n  const remainder = finite % 1;\n  return remainder ? finite - remainder : finite;\n}\nexport { toInteger };", "map": {"version": 3, "names": ["toFinite", "toInteger", "value", "finite", "remainder"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/util/toInteger.mjs"], "sourcesContent": ["import { toFinite } from './toFinite.mjs';\n\nfunction toInteger(value) {\n    const finite = toFinite(value);\n    const remainder = finite % 1;\n    return remainder ? finite - remainder : finite;\n}\n\nexport { toInteger };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,SAASA,CAACC,KAAK,EAAE;EACtB,MAAMC,MAAM,GAAGH,QAAQ,CAACE,KAAK,CAAC;EAC9B,MAAME,SAAS,GAAGD,MAAM,GAAG,CAAC;EAC5B,OAAOC,SAAS,GAAGD,MAAM,GAAGC,SAAS,GAAGD,MAAM;AAClD;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
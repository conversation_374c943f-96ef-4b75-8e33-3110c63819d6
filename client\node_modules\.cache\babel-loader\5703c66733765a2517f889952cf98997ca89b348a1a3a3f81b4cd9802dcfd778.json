{"ast": null, "code": "import { uniqBy } from './uniqBy.mjs';\nfunction unionBy(arr1, arr2, mapper) {\n  return uniqBy(arr1.concat(arr2), mapper);\n}\nexport { unionBy };", "map": {"version": 3, "names": ["uniqBy", "unionBy", "arr1", "arr2", "mapper", "concat"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/unionBy.mjs"], "sourcesContent": ["import { uniqBy } from './uniqBy.mjs';\n\nfunction unionBy(arr1, arr2, mapper) {\n    return uniqBy(arr1.concat(arr2), mapper);\n}\n\nexport { unionBy };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACjC,OAAOJ,MAAM,CAACE,IAAI,CAACG,MAAM,CAACF,IAAI,CAAC,EAAEC,MAAM,CAAC;AAC5C;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var EventKeys = ['dangerouslySetInnerHTML', 'onCopy', 'onCopyCapture', 'onCut', 'onCutCapture', 'onPaste', 'onPasteCapture', 'onCompositionEnd', 'onCompositionEndCapture', 'onCompositionStart', 'onCompositionStartCapture', 'onCompositionUpdate', 'onCompositionUpdateCapture', 'onFocus', 'onFocusCapture', 'onBlur', 'onBlurCapture', 'onChange', 'onChangeCapture', 'onBeforeInput', 'onBeforeInputCapture', 'onInput', 'onInputCapture', 'onReset', 'onResetCapture', 'onSubmit', 'onSubmitCapture', 'onInvalid', 'onInvalidCapture', 'onLoad', 'onLoadCapture', 'onError', 'onErrorCapture', 'onKeyDown', 'onKeyDownCapture', 'onKeyPress', 'onKeyPressCapture', 'onKeyUp', 'onKeyUpCapture', 'onAbort', 'onAbortCapture', 'onCanPlay', 'onCanPlayCapture', 'onCanPlayThrough', 'onCanPlayThroughCapture', 'onDurationChange', 'onDurationChangeCapture', 'onEmptied', 'onEmptiedCapture', 'onEncrypted', 'onEncryptedCapture', 'onEnded', 'onEndedCapture', 'onLoadedData', 'onLoadedDataCapture', 'onLoadedMetadata', 'onLoadedMetadataCapture', 'onLoadStart', 'onLoadStartCapture', 'onPause', 'onPauseCapture', 'onPlay', 'onPlayCapture', 'onPlaying', 'onPlayingCapture', 'onProgress', 'onProgressCapture', 'onRateChange', 'onRateChangeCapture', 'onSeeked', 'onSeekedCapture', 'onSeeking', 'onSeekingCapture', 'onStalled', 'onStalledCapture', 'onSuspend', 'onSuspendCapture', 'onTimeUpdate', 'onTimeUpdateCapture', 'onVolumeChange', 'onVolumeChangeCapture', 'onWaiting', 'onWaitingCapture', 'onAuxClick', 'onAuxClickCapture', 'onClick', 'onClickCapture', 'onContextMenu', 'onContextMenuCapture', 'onDoubleClick', 'onDoubleClickCapture', 'onDrag', 'onDragCapture', 'onDragEnd', 'onDragEndCapture', 'onDragEnter', 'onDragEnterCapture', 'onDragExit', 'onDragExitCapture', 'onDragLeave', 'onDragLeaveCapture', 'onDragOver', 'onDragOverCapture', 'onDragStart', 'onDragStartCapture', 'onDrop', 'onDropCapture', 'onMouseDown', 'onMouseDownCapture', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseMoveCapture', 'onMouseOut', 'onMouseOutCapture', 'onMouseOver', 'onMouseOverCapture', 'onMouseUp', 'onMouseUpCapture', 'onSelect', 'onSelectCapture', 'onTouchCancel', 'onTouchCancelCapture', 'onTouchEnd', 'onTouchEndCapture', 'onTouchMove', 'onTouchMoveCapture', 'onTouchStart', 'onTouchStartCapture', 'onPointerDown', 'onPointerDownCapture', 'onPointerMove', 'onPointerMoveCapture', 'onPointerUp', 'onPointerUpCapture', 'onPointerCancel', 'onPointerCancelCapture', 'onPointerEnter', 'onPointerEnterCapture', 'onPointerLeave', 'onPointerLeaveCapture', 'onPointerOver', 'onPointerOverCapture', 'onPointerOut', 'onPointerOutCapture', 'onGotPointerCapture', 'onGotPointerCaptureCapture', 'onLostPointerCapture', 'onLostPointerCaptureCapture', 'onScroll', 'onScrollCapture', 'onWheel', 'onWheelCapture', 'onAnimationStart', 'onAnimationStartCapture', 'onAnimationEnd', 'onAnimationEndCapture', 'onAnimationIteration', 'onAnimationIterationCapture', 'onTransitionEnd', 'onTransitionEndCapture'];\nexport function isEventKey(key) {\n  if (typeof key !== 'string') {\n    return false;\n  }\n  var allowedEventKeys = EventKeys;\n  return allowedEventKeys.includes(key);\n}\n\n/**\n * Filters out event properties from the given object.\n * This function is useful for cleaning up props before passing them to a React component,\n * @param obj - The object containing properties to filter.\n * @returns A new object containing only the properties that are not event handlers.\n */\nexport function excludeEventProps(obj) {\n  var filteredEntries = Object.entries(obj).filter(_ref => {\n    var [key] = _ref;\n    return !isEventKey(key);\n  });\n  return Object.fromEntries(filteredEntries);\n}", "map": {"version": 3, "names": ["EventKeys", "isEventKey", "key", "allowed<PERSON><PERSON><PERSON><PERSON><PERSON>", "includes", "excludeEventProps", "obj", "filteredEntries", "Object", "entries", "filter", "_ref", "fromEntries"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/excludeEventProps.js"], "sourcesContent": ["var EventKeys = ['dangerouslySetInnerHTML', 'onCopy', 'onCopyCapture', 'onCut', 'onCutCapture', 'onPaste', 'onPasteCapture', 'onCompositionEnd', 'onCompositionEndCapture', 'onCompositionStart', 'onCompositionStartCapture', 'onCompositionUpdate', 'onCompositionUpdateCapture', 'onFocus', 'onFocusCapture', 'onBlur', 'onBlurCapture', 'onChange', 'onChangeCapture', 'onBeforeInput', 'onBeforeInputCapture', 'onInput', 'onInputCapture', 'onReset', 'onResetCapture', 'onSubmit', 'onSubmitCapture', 'onInvalid', 'onInvalidCapture', 'onLoad', 'onLoadCapture', 'onError', 'onErrorCapture', 'onKeyDown', 'onKeyDownCapture', 'onKeyPress', 'onKeyPressCapture', 'onKeyUp', 'onKeyUpCapture', 'onAbort', 'onAbortCapture', 'onCanPlay', 'onCanPlayCapture', 'onCanPlayThrough', 'onCanPlayThroughCapture', 'onDurationChange', 'onDurationChangeCapture', 'onEmptied', 'onEmptiedCapture', 'onEncrypted', 'onEncryptedCapture', 'onEnded', 'onEndedCapture', 'onLoadedData', 'onLoadedDataCapture', 'onLoadedMetadata', 'onLoadedMetadataCapture', 'onLoadStart', 'onLoadStartCapture', 'onPause', 'onPauseCapture', 'onPlay', 'onPlayCapture', 'onPlaying', 'onPlayingCapture', 'onProgress', 'onProgressCapture', 'onRateChange', 'onRateChangeCapture', 'onSeeked', 'onSeekedCapture', 'onSeeking', 'onSeekingCapture', 'onStalled', 'onStalledCapture', 'onSuspend', 'onSuspendCapture', 'onTimeUpdate', 'onTimeUpdateCapture', 'onVolumeChange', 'onVolumeChangeCapture', 'onWaiting', 'onWaitingCapture', 'onAuxClick', 'onAuxClickCapture', 'onClick', 'onClickCapture', 'onContextMenu', 'onContextMenuCapture', 'onDoubleClick', 'onDoubleClickCapture', 'onDrag', 'onDragCapture', 'onDragEnd', 'onDragEndCapture', 'onDragEnter', 'onDragEnterCapture', 'onDragExit', 'onDragExitCapture', 'onDragLeave', 'onDragLeaveCapture', 'onDragOver', 'onDragOverCapture', 'onDragStart', 'onDragStartCapture', 'onDrop', 'onDropCapture', 'onMouseDown', 'onMouseDownCapture', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseMoveCapture', 'onMouseOut', 'onMouseOutCapture', 'onMouseOver', 'onMouseOverCapture', 'onMouseUp', 'onMouseUpCapture', 'onSelect', 'onSelectCapture', 'onTouchCancel', 'onTouchCancelCapture', 'onTouchEnd', 'onTouchEndCapture', 'onTouchMove', 'onTouchMoveCapture', 'onTouchStart', 'onTouchStartCapture', 'onPointerDown', 'onPointerDownCapture', 'onPointerMove', 'onPointerMoveCapture', 'onPointerUp', 'onPointerUpCapture', 'onPointerCancel', 'onPointerCancelCapture', 'onPointerEnter', 'onPointerEnterCapture', 'onPointerLeave', 'onPointerLeaveCapture', 'onPointerOver', 'onPointerOverCapture', 'onPointerOut', 'onPointerOutCapture', 'onGotPointerCapture', 'onGotPointerCaptureCapture', 'onLostPointerCapture', 'onLostPointerCaptureCapture', 'onScroll', 'onScrollCapture', 'onWheel', 'onWheelCapture', 'onAnimationStart', 'onAnimationStartCapture', 'onAnimationEnd', 'onAnimationEndCapture', 'onAnimationIteration', 'onAnimationIterationCapture', 'onTransitionEnd', 'onTransitionEndCapture'];\nexport function isEventKey(key) {\n  if (typeof key !== 'string') {\n    return false;\n  }\n  var allowedEventKeys = EventKeys;\n  return allowedEventKeys.includes(key);\n}\n\n/**\n * Filters out event properties from the given object.\n * This function is useful for cleaning up props before passing them to a React component,\n * @param obj - The object containing properties to filter.\n * @returns A new object containing only the properties that are not event handlers.\n */\nexport function excludeEventProps(obj) {\n  var filteredEntries = Object.entries(obj).filter(_ref => {\n    var [key] = _ref;\n    return !isEventKey(key);\n  });\n  return Object.fromEntries(filteredEntries);\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,yBAAyB,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAE,cAAc,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,cAAc,EAAE,qBAAqB,EAAE,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAE,cAAc,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAE,sBAAsB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,qBAAqB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAE,sBAAsB,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,eAAe,EAAE,sBAAsB,EAAE,cAAc,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,4BAA4B,EAAE,sBAAsB,EAAE,6BAA6B,EAAE,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,6BAA6B,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;AACh5F,OAAO,SAASC,UAAUA,CAACC,GAAG,EAAE;EAC9B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,IAAIC,gBAAgB,GAAGH,SAAS;EAChC,OAAOG,gBAAgB,CAACC,QAAQ,CAACF,GAAG,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,iBAAiBA,CAACC,GAAG,EAAE;EACrC,IAAIC,eAAe,GAAGC,MAAM,CAACC,OAAO,CAACH,GAAG,CAAC,CAACI,MAAM,CAACC,IAAI,IAAI;IACvD,IAAI,CAACT,GAAG,CAAC,GAAGS,IAAI;IAChB,OAAO,CAACV,UAAU,CAACC,GAAG,CAAC;EACzB,CAAC,CAAC;EACF,OAAOM,MAAM,CAACI,WAAW,CAACL,eAAe,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
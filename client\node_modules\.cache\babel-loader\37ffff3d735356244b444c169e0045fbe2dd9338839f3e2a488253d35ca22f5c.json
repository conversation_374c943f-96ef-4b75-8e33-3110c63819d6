{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * Unique ID of the graphical item.\n * This is used to identify the graphical item in the state and in the React tree.\n * This is required for every graphical item - it's either provided by the user or generated automatically.\n */\n\nvar initialState = {\n  cartesianItems: [],\n  polarItems: []\n};\nvar graphicalItemsSlice = createSlice({\n  name: 'graphicalItems',\n  initialState,\n  reducers: {\n    addCartesianGraphicalItem(state, action) {\n      state.cartesianItems.push(castDraft(action.payload));\n    },\n    replaceCartesianGraphicalItem(state, action) {\n      var {\n        prev,\n        next\n      } = action.payload;\n      var index = current(state).cartesianItems.indexOf(castDraft(prev));\n      if (index > -1) {\n        state.cartesianItems[index] = castDraft(next);\n      }\n    },\n    removeCartesianGraphicalItem(state, action) {\n      var index = current(state).cartesianItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.cartesianItems.splice(index, 1);\n      }\n    },\n    addPolarGraphicalItem(state, action) {\n      state.polarItems.push(castDraft(action.payload));\n    },\n    removePolarGraphicalItem(state, action) {\n      var index = current(state).polarItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.polarItems.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addCartesianGraphicalItem,\n  replaceCartesianGraphicalItem,\n  removeCartesianGraphicalItem,\n  addPolarGraphicalItem,\n  removePolarGraphicalItem\n} = graphicalItemsSlice.actions;\nexport var graphicalItemsReducer = graphicalItemsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "castDraft", "initialState", "cartesianItems", "polarItems", "graphicalItemsSlice", "name", "reducers", "addCartesianGraphicalItem", "state", "action", "push", "payload", "replaceCartesianGraphicalItem", "prev", "next", "index", "indexOf", "removeCartesianGraphicalItem", "splice", "addPolarGraphicalItem", "removePolarGraphicalItem", "actions", "graphicalItemsReducer", "reducer"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/graphicalItemsSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * Unique ID of the graphical item.\n * This is used to identify the graphical item in the state and in the React tree.\n * This is required for every graphical item - it's either provided by the user or generated automatically.\n */\n\nvar initialState = {\n  cartesianItems: [],\n  polarItems: []\n};\nvar graphicalItemsSlice = createSlice({\n  name: 'graphicalItems',\n  initialState,\n  reducers: {\n    addCartesianGraphicalItem(state, action) {\n      state.cartesianItems.push(castDraft(action.payload));\n    },\n    replaceCartesianGraphicalItem(state, action) {\n      var {\n        prev,\n        next\n      } = action.payload;\n      var index = current(state).cartesianItems.indexOf(castDraft(prev));\n      if (index > -1) {\n        state.cartesianItems[index] = castDraft(next);\n      }\n    },\n    removeCartesianGraphicalItem(state, action) {\n      var index = current(state).cartesianItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.cartesianItems.splice(index, 1);\n      }\n    },\n    addPolarGraphicalItem(state, action) {\n      state.polarItems.push(castDraft(action.payload));\n    },\n    removePolarGraphicalItem(state, action) {\n      var index = current(state).polarItems.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.polarItems.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addCartesianGraphicalItem,\n  replaceCartesianGraphicalItem,\n  removeCartesianGraphicalItem,\n  addPolarGraphicalItem,\n  removePolarGraphicalItem\n} = graphicalItemsSlice.actions;\nexport var graphicalItemsReducer = graphicalItemsSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG;EACjBC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,mBAAmB,GAAGN,WAAW,CAAC;EACpCO,IAAI,EAAE,gBAAgB;EACtBJ,YAAY;EACZK,QAAQ,EAAE;IACRC,yBAAyBA,CAACC,KAAK,EAAEC,MAAM,EAAE;MACvCD,KAAK,CAACN,cAAc,CAACQ,IAAI,CAACV,SAAS,CAACS,MAAM,CAACE,OAAO,CAAC,CAAC;IACtD,CAAC;IACDC,6BAA6BA,CAACJ,KAAK,EAAEC,MAAM,EAAE;MAC3C,IAAI;QACFI,IAAI;QACJC;MACF,CAAC,GAAGL,MAAM,CAACE,OAAO;MAClB,IAAII,KAAK,GAAGhB,OAAO,CAACS,KAAK,CAAC,CAACN,cAAc,CAACc,OAAO,CAAChB,SAAS,CAACa,IAAI,CAAC,CAAC;MAClE,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;QACdP,KAAK,CAACN,cAAc,CAACa,KAAK,CAAC,GAAGf,SAAS,CAACc,IAAI,CAAC;MAC/C;IACF,CAAC;IACDG,4BAA4BA,CAACT,KAAK,EAAEC,MAAM,EAAE;MAC1C,IAAIM,KAAK,GAAGhB,OAAO,CAACS,KAAK,CAAC,CAACN,cAAc,CAACc,OAAO,CAAChB,SAAS,CAACS,MAAM,CAACE,OAAO,CAAC,CAAC;MAC5E,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACdP,KAAK,CAACN,cAAc,CAACgB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACvC;IACF,CAAC;IACDI,qBAAqBA,CAACX,KAAK,EAAEC,MAAM,EAAE;MACnCD,KAAK,CAACL,UAAU,CAACO,IAAI,CAACV,SAAS,CAACS,MAAM,CAACE,OAAO,CAAC,CAAC;IAClD,CAAC;IACDS,wBAAwBA,CAACZ,KAAK,EAAEC,MAAM,EAAE;MACtC,IAAIM,KAAK,GAAGhB,OAAO,CAACS,KAAK,CAAC,CAACL,UAAU,CAACa,OAAO,CAAChB,SAAS,CAACS,MAAM,CAACE,OAAO,CAAC,CAAC;MACxE,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACdP,KAAK,CAACL,UAAU,CAACe,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACnC;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTR,yBAAyB;EACzBK,6BAA6B;EAC7BK,4BAA4B;EAC5BE,qBAAqB;EACrBC;AACF,CAAC,GAAGhB,mBAAmB,CAACiB,OAAO;AAC/B,OAAO,IAAIC,qBAAqB,GAAGlB,mBAAmB,CAACmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
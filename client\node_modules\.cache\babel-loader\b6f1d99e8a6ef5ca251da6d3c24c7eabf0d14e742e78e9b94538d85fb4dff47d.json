{"ast": null, "code": "function negate(func) {\n  return (...args) => !func(...args);\n}\nexport { negate };", "map": {"version": 3, "names": ["negate", "func", "args"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/negate.mjs"], "sourcesContent": ["function negate(func) {\n    return ((...args) => !func(...args));\n}\n\nexport { negate };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,IAAI,EAAE;EAClB,OAAQ,CAAC,GAAGC,IAAI,KAAK,CAACD,IAAI,CAAC,GAAGC,IAAI,CAAC;AACvC;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function escapeRegExp(str) {\n  return str.replace(/[\\\\^$.*+?()[\\]{}|]/g, '\\\\$&');\n}\nexport { escapeRegExp };", "map": {"version": 3, "names": ["escapeRegExp", "str", "replace"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/escapeRegExp.mjs"], "sourcesContent": ["function escapeRegExp(str) {\n    return str.replace(/[\\\\^$.*+?()[\\]{}|]/g, '\\\\$&');\n}\n\nexport { escapeRegExp };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACrD;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
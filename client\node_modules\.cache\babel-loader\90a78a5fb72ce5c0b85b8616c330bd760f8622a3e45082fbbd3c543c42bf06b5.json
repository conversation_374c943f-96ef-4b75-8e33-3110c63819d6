{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;", "map": {"version": 3, "names": ["ascending", "bisector", "number", "ascendingBisect", "bisectRight", "right", "bisectLeft", "left", "bisectCenter", "center"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/d3-array/src/bisect.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,MAAMC,eAAe,GAAGF,QAAQ,CAACD,SAAS,CAAC;AAC3C,OAAO,MAAMI,WAAW,GAAGD,eAAe,CAACE,KAAK;AAChD,OAAO,MAAMC,UAAU,GAAGH,eAAe,CAACI,IAAI;AAC9C,OAAO,MAAMC,YAAY,GAAGP,QAAQ,CAACC,MAAM,CAAC,CAACO,MAAM;AACnD,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
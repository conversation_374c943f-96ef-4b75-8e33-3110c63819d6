{"ast": null, "code": "function isUnsafeProperty(key) {\n  return key === '__proto__';\n}\nexport { isUnsafeProperty };", "map": {"version": 3, "names": ["isUnsafeProperty", "key"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/_internal/isUnsafeProperty.mjs"], "sourcesContent": ["function isUnsafeProperty(key) {\n    return key === '__proto__';\n}\n\nexport { isUnsafeProperty };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAOA,GAAG,KAAK,WAAW;AAC9B;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function isBlob(x) {\n  if (typeof Blob === 'undefined') {\n    return false;\n  }\n  return x instanceof Blob;\n}\nexport { isBlob };", "map": {"version": 3, "names": ["isBlob", "x", "Blob"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isBlob.mjs"], "sourcesContent": ["function isBlob(x) {\n    if (typeof Blob === 'undefined') {\n        return false;\n    }\n    return x instanceof Blob;\n}\n\nexport { isBlob };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,CAAC,EAAE;EACf,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAO,KAAK;EAChB;EACA,OAAOD,CAAC,YAAYC,IAAI;AAC5B;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
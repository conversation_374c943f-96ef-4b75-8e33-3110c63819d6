{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport range from 'es-toolkit/compat/range';\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { checkDomainOfScale, getDomainOfStackGroups, getStackedData, getValueByDataKey, isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes, selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { isWellFormedNumberDomain, numericalDomainSpecifiedWithoutRequiringData, parseNumericalUserDomain } from '../../util/isDomainSpecifiedByUser';\nimport { getPercentValue, hasDuplicate, isNan, isNumber, isNumOrStr, mathSign, upperFirst } from '../../util/DataUtils';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getNiceTickValues, getTickValuesFixedDomain } from '../../util/scale';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBrushDimensions, selectBrushSettings } from './brushSelectors';\nimport { selectBarCategoryGap, selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { selectAngleAxis, selectAngleAxisRange, selectRadiusAxis, selectRadiusAxisRange } from './polarAxisSelectors';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nimport { selectTooltipAxis } from './selectTooltipAxis';\nimport { combineDisplayedStackedData } from './combiners/combineDisplayedStackedData';\nimport { isStacked } from '../types/StackedGraphicalItem';\nvar defaultNumericDomain = [0, 'auto'];\n\n/**\n * angle, radius, X, Y, and Z axes all have domain and range and scale and associated settings\n */\n\n/**\n * X and Y axes have ticks. Z axis is never displayed and so it lacks ticks\n * and tick settings.\n */\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitXAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: undefined,\n  height: 30,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'bottom',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectXAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.xAxis[axisId];\n  if (axis == null) {\n    return implicitXAxis;\n  }\n  return axis;\n};\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitYAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: defaultNumericDomain,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'left',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined,\n  width: DEFAULT_Y_AXIS_WIDTH\n};\nexport var selectYAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.yAxis[axisId];\n  if (axis == null) {\n    return implicitYAxis;\n  }\n  return axis;\n};\nexport var implicitZAxis = {\n  domain: [0, 'auto'],\n  includeHidden: false,\n  reversed: false,\n  allowDataOverflow: false,\n  allowDuplicatedCategory: false,\n  dataKey: undefined,\n  id: 0,\n  name: '',\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number',\n  unit: ''\n};\nexport var selectZAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.zAxis[axisId];\n  if (axis == null) {\n    return implicitZAxis;\n  }\n  return axis;\n};\nexport var selectBaseAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'zAxis':\n      {\n        return selectZAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\nvar selectCartesianAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * Selects either an X or Y axis. Doesn't work with Z axis - for that, instead use selectBaseAxis.\n * @param state Root state\n * @param axisType xAxis | yAxis\n * @param axisId xAxisId | yAxisId\n * @returns axis settings object\n */\nexport var selectAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * @param state RechartsRootState\n * @return boolean true if there is at least one Bar or RadialBar\n */\nexport var selectHasBar = state => state.graphicalItems.cartesianItems.some(item => item.type === 'bar') || state.graphicalItems.polarItems.some(item => item.type === 'radialBar');\n\n/**\n * Filters CartesianGraphicalItemSettings by the relevant axis ID\n * @param axisType 'xAxis' | 'yAxis' | 'zAxis' | 'radiusAxis' | 'angleAxis'\n * @param axisId from props, defaults to 0\n *\n * @returns Predicate function that return true for CartesianGraphicalItemSettings that are relevant to the specified axis\n */\nexport function itemAxisPredicate(axisType, axisId) {\n  return item => {\n    switch (axisType) {\n      case 'xAxis':\n        // This is sensitive to the data type, as 0 !== '0'. I wonder if we should be more flexible. How does 2.x branch behave? TODO write test for that\n        return 'xAxisId' in item && item.xAxisId === axisId;\n      case 'yAxis':\n        return 'yAxisId' in item && item.yAxisId === axisId;\n      case 'zAxis':\n        return 'zAxisId' in item && item.zAxisId === axisId;\n      case 'angleAxis':\n        return 'angleAxisId' in item && item.angleAxisId === axisId;\n      case 'radiusAxis':\n        return 'radiusAxisId' in item && item.radiusAxisId === axisId;\n      default:\n        return false;\n    }\n  };\n}\nexport var selectUnfilteredCartesianItems = state => state.graphicalItems.cartesianItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var combineGraphicalItemsSettings = (graphicalItems, axisSettings, axisPredicate) => graphicalItems.filter(axisPredicate).filter(item => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.includeHidden) === true) {\n    return true;\n  }\n  return !item.hide;\n});\nexport var selectCartesianItemsSettings = createSelector([selectUnfilteredCartesianItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nexport var selectStackedCartesianItemsSettings = createSelector([selectCartesianItemsSettings], cartesianItems => {\n  return cartesianItems.filter(item => item.type === 'area' || item.type === 'bar').filter(isStacked);\n});\nexport var filterGraphicalNotStackedItems = cartesianItems => cartesianItems.filter(item => !('stackId' in item) || item.stackId === undefined);\nvar selectCartesianItemsSettingsExceptStacked = createSelector([selectCartesianItemsSettings], filterGraphicalNotStackedItems);\nexport var combineGraphicalItemsData = cartesianItems => cartesianItems.map(item => item.data).filter(Boolean).flat(1);\n\n/**\n * This is a \"cheap\" selector - it returns the data but doesn't iterate them, so it is not sensitive on the array length.\n * Also does not apply dataKey yet.\n * @param state RechartsRootState\n * @returns data defined on the chart graphical items, such as Line or Scatter or Pie, and filtered with appropriate dataKey\n */\nexport var selectCartesianGraphicalItemsData = createSelector([selectCartesianItemsSettings], combineGraphicalItemsData);\nexport var combineDisplayedData = (graphicalItemsData, _ref) => {\n  var {\n    chartData = [],\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (graphicalItemsData.length > 0) {\n    /*\n     * There is no slicing when data is defined on graphical items. Why?\n     * Because Brush ignores data defined on graphical items,\n     * and does not render.\n     * So Brush will never show up in a Scatter chart for example.\n     * This is something we will need to fix.\n     *\n     * Now, when the root chart data is not defined, the dataEndIndex is 0,\n     * which means the itemsData will be sliced to an empty array anyway.\n     * But that's an implementation detail, and we can fix that too.\n     *\n     * Also, in absence of Axis dataKey, we use the dataKey from each item, respectively.\n     * This is the usual pattern for numerical axis, that is the one where bars go up:\n     * users don't specify any dataKey by default and expect the axis to \"just match the data\".\n     */\n    return graphicalItemsData;\n  }\n  return chartData.slice(dataStartIndex, dataEndIndex + 1);\n};\n\n/**\n * This selector will return all data there is in the chart: graphical items, chart root, all together.\n * Useful for figuring out an axis domain (because that needs to know of everything),\n * not useful for rendering individual graphical elements (because they need to know which data is theirs and which is not).\n *\n * This function will discard the original indexes, so it is also not useful for anything that depends on ordering.\n */\nexport var selectDisplayedData = createSelector([selectCartesianGraphicalItemsData, selectChartDataWithIndexesIfNotInPanorama], combineDisplayedData);\nexport var combineAppliedValues = (data, axisSettings, items) => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey)\n    }));\n  }\n  if (items.length > 0) {\n    return items.map(item => item.dataKey).flatMap(dataKey => data.map(entry => ({\n      value: getValueByDataKey(entry, dataKey)\n    })));\n  }\n  return data.map(entry => ({\n    value: entry\n  }));\n};\n\n/**\n * This selector will return all values with the appropriate dataKey applied on them.\n * Which dataKey is appropriate depends on where it is defined.\n *\n * This is an expensive selector - it will iterate all data and compute their value using the provided dataKey.\n */\nexport var selectAllAppliedValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettings], combineAppliedValues);\nexport function isErrorBarRelevantForAxisType(axisType, errorBar) {\n  switch (axisType) {\n    case 'xAxis':\n      return errorBar.direction === 'x';\n    case 'yAxis':\n      return errorBar.direction === 'y';\n    default:\n      return false;\n  }\n}\n\n/**\n * This is type of \"error\" in chart. It is set by using ErrorBar, and it can represent confidence interval,\n * or gap in the data, or standard deviation, or quartiles in boxplot, or whiskers or whatever.\n *\n * We will internally represent it as a tuple of two numbers, where the first number is the lower bound and the second number is the upper bound.\n *\n * It is also true that the first number should be lower than or equal to the associated \"main value\",\n * and the second number should be higher than or equal to the associated \"main value\".\n */\n\nexport function fromMainValueToError(value) {\n  if (isNumber(value) && Number.isFinite(value)) {\n    return [value, value];\n  }\n  if (Array.isArray(value)) {\n    var minError = Math.min(...value);\n    var maxError = Math.max(...value);\n    if (!isNan(minError) && !isNan(maxError) && Number.isFinite(minError) && Number.isFinite(maxError)) {\n      return [minError, maxError];\n    }\n  }\n  return undefined;\n}\nfunction onlyAllowNumbers(data) {\n  return data.filter(v => isNumOrStr(v) || v instanceof Date).map(Number).filter(n => isNan(n) === false);\n}\n\n/**\n * @param entry One item in the 'data' array. Could be anything really - this is defined externally. This is the raw, before dataKey application\n * @param appliedValue This is the result of applying the 'main' dataKey on the `entry`.\n * @param relevantErrorBars Error bars that are relevant for the current axis and layout and all that.\n * @return either undefined or an array of ErrorValue\n */\nexport function getErrorDomainByDataKey(entry, appliedValue, relevantErrorBars) {\n  if (!relevantErrorBars || typeof appliedValue !== 'number' || isNan(appliedValue)) {\n    return [];\n  }\n  if (!relevantErrorBars.length) {\n    return [];\n  }\n  return onlyAllowNumbers(relevantErrorBars.flatMap(eb => {\n    var errorValue = getValueByDataKey(entry, eb.dataKey);\n    var lowBound, highBound;\n    if (Array.isArray(errorValue)) {\n      [lowBound, highBound] = errorValue;\n    } else {\n      lowBound = highBound = errorValue;\n    }\n    if (!isWellBehavedNumber(lowBound) || !isWellBehavedNumber(highBound)) {\n      return undefined;\n    }\n    return [appliedValue - lowBound, appliedValue + highBound];\n  }));\n}\nexport var selectDisplayedStackedData = createSelector([selectStackedCartesianItemsSettings, selectChartDataWithIndexesIfNotInPanorama, selectTooltipAxis], combineDisplayedStackedData);\nexport var combineStackGroups = (displayedData, items, stackOffsetType) => {\n  var initialItemsGroups = {};\n  var itemsGroup = items.reduce((acc, item) => {\n    if (item.stackId == null) {\n      return acc;\n    }\n    if (acc[item.stackId] == null) {\n      acc[item.stackId] = [];\n    }\n    acc[item.stackId].push(item);\n    return acc;\n  }, initialItemsGroups);\n  return Object.fromEntries(Object.entries(itemsGroup).map(_ref2 => {\n    var [stackId, graphicalItems] = _ref2;\n    var dataKeys = graphicalItems.map(getStackSeriesIdentifier);\n    return [stackId, {\n      // @ts-expect-error getStackedData requires that the input is array of objects, Recharts does not test for that\n      stackedData: getStackedData(displayedData, dataKeys, stackOffsetType),\n      graphicalItems\n    }];\n  }));\n};\n\n/**\n * Stack groups are groups of graphical items that stack on each other.\n * Stack is a function of axis type (X, Y), axis ID, and stack ID.\n * Graphical items that do not have a stack ID are not going to be present in stack groups.\n */\nexport var selectStackGroups = createSelector([selectDisplayedStackedData, selectStackedCartesianItemsSettings, selectStackOffsetType], combineStackGroups);\nexport var combineDomainOfStackGroups = (stackGroups, _ref3, axisType) => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (axisType === 'zAxis') {\n    // ZAxis ignores stacks\n    return undefined;\n  }\n  var domainOfStackGroups = getDomainOfStackGroups(stackGroups, dataStartIndex, dataEndIndex);\n  if (domainOfStackGroups != null && domainOfStackGroups[0] === 0 && domainOfStackGroups[1] === 0) {\n    return undefined;\n  }\n  return domainOfStackGroups;\n};\nexport var selectDomainOfStackGroups = createSelector([selectStackGroups, selectChartDataWithIndexes, pickAxisType], combineDomainOfStackGroups);\nexport var combineAppliedNumericalValuesIncludingErrorValues = (data, axisSettings, items, errorBars, axisType) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _errorBars$item$id, _axisSettings$dataKey;\n        var relevantErrorBars = (_errorBars$item$id = errorBars[item.id]) === null || _errorBars$item$id === void 0 ? void 0 : _errorBars$item$id.filter(errorBar => isErrorBarRelevantForAxisType(axisType, errorBar));\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: getErrorDomainByDataKey(entry, valueByDataKey, relevantErrorBars)\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n};\nexport var selectAllErrorBarSettings = state => state.errorBars;\nvar combineRelevantErrorBarSettings = (cartesianItemsSettings, allErrorBarSettings, axisType) => {\n  return cartesianItemsSettings.flatMap(item => {\n    return allErrorBarSettings[item.id];\n  }).filter(Boolean).filter(e => {\n    return isErrorBarRelevantForAxisType(axisType, e);\n  });\n};\nexport var selectErrorBarsSettingsExceptStacked = createSelector([selectCartesianItemsSettingsExceptStacked, selectAllErrorBarSettings, pickAxisType], combineRelevantErrorBarSettings);\nexport var selectAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettingsExceptStacked, selectAllErrorBarSettings, pickAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nfunction onlyAllowNumbersAndStringsAndDates(item) {\n  var {\n    value\n  } = item;\n  if (isNumOrStr(value) || value instanceof Date) {\n    return value;\n  }\n  return undefined;\n}\nvar computeNumericalDomain = dataWithErrorDomains => {\n  var allDataSquished = dataWithErrorDomains\n  // This flatMap has to be flat because we're creating a new array in the return value\n  .flatMap(d => [d.value, d.errorDomain])\n  // This flat is needed because a) errorDomain is an array, and b) value may be a number, or it may be a range (for Area, for example)\n  .flat(1);\n  var onlyNumbers = onlyAllowNumbers(allDataSquished);\n  if (onlyNumbers.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...onlyNumbers), Math.max(...onlyNumbers)];\n};\nvar computeDomainOfTypeCategory = (allDataSquished, axisSettings, isCategorical) => {\n  var categoricalDomain = allDataSquished.map(onlyAllowNumbersAndStringsAndDates).filter(v => v != null);\n  if (isCategorical && (axisSettings.dataKey == null || axisSettings.allowDuplicatedCategory && hasDuplicate(categoricalDomain))) {\n    /*\n     * 1. In an absence of dataKey, Recharts will use array indexes as its categorical domain\n     * 2. When category axis has duplicated text, serial numbers are used to generate scale\n     */\n    return range(0, allDataSquished.length);\n  }\n  if (axisSettings.allowDuplicatedCategory) {\n    return categoricalDomain;\n  }\n  return Array.from(new Set(categoricalDomain));\n};\nexport var getDomainDefinition = axisSettings => {\n  var _axisSettings$domain;\n  if (axisSettings == null || !('domain' in axisSettings)) {\n    return defaultNumericDomain;\n  }\n  if (axisSettings.domain != null) {\n    return axisSettings.domain;\n  }\n  if (axisSettings.ticks != null) {\n    if (axisSettings.type === 'number') {\n      var allValues = onlyAllowNumbers(axisSettings.ticks);\n      return [Math.min(...allValues), Math.max(...allValues)];\n    }\n    if (axisSettings.type === 'category') {\n      return axisSettings.ticks.map(String);\n    }\n  }\n  return (_axisSettings$domain = axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.domain) !== null && _axisSettings$domain !== void 0 ? _axisSettings$domain : defaultNumericDomain;\n};\nexport var mergeDomains = function mergeDomains() {\n  for (var _len = arguments.length, domains = new Array(_len), _key = 0; _key < _len; _key++) {\n    domains[_key] = arguments[_key];\n  }\n  var allDomains = domains.filter(Boolean);\n  if (allDomains.length === 0) {\n    return undefined;\n  }\n  var allValues = allDomains.flat();\n  var min = Math.min(...allValues);\n  var max = Math.max(...allValues);\n  return [min, max];\n};\nexport var selectReferenceDots = state => state.referenceElements.dots;\nexport var filterReferenceElements = (elements, axisType, axisId) => {\n  return elements.filter(el => el.ifOverflow === 'extendDomain').filter(el => {\n    if (axisType === 'xAxis') {\n      return el.xAxisId === axisId;\n    }\n    return el.yAxisId === axisId;\n  });\n};\nexport var selectReferenceDotsByAxis = createSelector([selectReferenceDots, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceAreas = state => state.referenceElements.areas;\nexport var selectReferenceAreasByAxis = createSelector([selectReferenceAreas, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceLines = state => state.referenceElements.lines;\nexport var selectReferenceLinesByAxis = createSelector([selectReferenceLines, pickAxisType, pickAxisId], filterReferenceElements);\nexport var combineDotsDomain = (dots, axisType) => {\n  var allCoords = onlyAllowNumbers(dots.map(dot => axisType === 'xAxis' ? dot.x : dot.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceDotsDomain = createSelector(selectReferenceDotsByAxis, pickAxisType, combineDotsDomain);\nexport var combineAreasDomain = (areas, axisType) => {\n  var allCoords = onlyAllowNumbers(areas.flatMap(area => [axisType === 'xAxis' ? area.x1 : area.y1, axisType === 'xAxis' ? area.x2 : area.y2]));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceAreasDomain = createSelector([selectReferenceAreasByAxis, pickAxisType], combineAreasDomain);\nexport var combineLinesDomain = (lines, axisType) => {\n  var allCoords = onlyAllowNumbers(lines.map(line => axisType === 'xAxis' ? line.x : line.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceLinesDomain = createSelector(selectReferenceLinesByAxis, pickAxisType, combineLinesDomain);\nvar selectReferenceElementsDomain = createSelector(selectReferenceDotsDomain, selectReferenceLinesDomain, selectReferenceAreasDomain, (dotsDomain, linesDomain, areasDomain) => {\n  return mergeDomains(dotsDomain, areasDomain, linesDomain);\n});\nexport var selectDomainDefinition = createSelector([selectBaseAxis], getDomainDefinition);\nexport var combineNumericalDomain = (axisSettings, domainDefinition, domainOfStackGroups, allDataWithErrorDomains, referenceElementsDomain, layout, axisType) => {\n  var domainFromUserPreference = numericalDomainSpecifiedWithoutRequiringData(domainDefinition, axisSettings.allowDataOverflow);\n  if (domainFromUserPreference != null) {\n    // We're done! No need to compute anything else.\n    return domainFromUserPreference;\n  }\n  var shouldIncludeDomainOfStackGroups = layout === 'vertical' && axisType === 'xAxis' || layout === 'horizontal' && axisType === 'yAxis';\n  var mergedDomains = shouldIncludeDomainOfStackGroups ? mergeDomains(domainOfStackGroups, referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains)) : mergeDomains(referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains));\n  return parseNumericalUserDomain(domainDefinition, mergedDomains, axisSettings.allowDataOverflow);\n};\nexport var selectNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, selectDomainOfStackGroups, selectAllAppliedNumericalValuesIncludingErrorValues, selectReferenceElementsDomain, selectChartLayout, pickAxisType], combineNumericalDomain);\n\n/**\n * Expand by design maps everything between 0 and 1,\n * there is nothing to compute.\n * See https://d3js.org/d3-shape/stack#stack-offsets\n */\nvar expandDomain = [0, 1];\nexport var combineAxisDomain = (axisSettings, layout, displayedData, allAppliedValues, stackOffsetType, axisType, numericalDomain) => {\n  if ((axisSettings == null || displayedData == null || displayedData.length === 0) && numericalDomain === undefined) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    type\n  } = axisSettings;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && dataKey == null) {\n    return range(0, displayedData.length);\n  }\n  if (type === 'category') {\n    return computeDomainOfTypeCategory(allAppliedValues, axisSettings, isCategorical);\n  }\n  if (stackOffsetType === 'expand') {\n    return expandDomain;\n  }\n  return numericalDomain;\n};\nexport var selectAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectDisplayedData, selectAllAppliedValues, selectStackOffsetType, pickAxisType, selectNumericalDomain], combineAxisDomain);\nexport var combineRealScaleType = (axisConfig, layout, hasBar, chartType, axisType) => {\n  if (axisConfig == null) {\n    return undefined;\n  }\n  var {\n    scale,\n    type\n  } = axisConfig;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return 'band';\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return 'linear';\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return 'point';\n    }\n    if (type === 'category') {\n      return 'band';\n    }\n    return 'linear';\n  }\n  if (typeof scale === 'string') {\n    var name = \"scale\".concat(upperFirst(scale));\n    return name in d3Scales ? name : 'point';\n  }\n  return undefined;\n};\nexport var selectRealScaleType = createSelector([selectBaseAxis, selectChartLayout, selectHasBar, selectChartName, pickAxisType], combineRealScaleType);\nfunction getD3ScaleFromType(realScaleType) {\n  if (realScaleType == null) {\n    return undefined;\n  }\n  if (realScaleType in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[realScaleType]();\n  }\n  var name = \"scale\".concat(upperFirst(realScaleType));\n  if (name in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[name]();\n  }\n  return undefined;\n}\nexport function combineScaleFunction(axis, realScaleType, axisDomain, axisRange) {\n  if (axisDomain == null || axisRange == null) {\n    return undefined;\n  }\n  if (typeof axis.scale === 'function') {\n    // @ts-expect-error we're going to assume here that if axis.scale is a function then it is a d3Scale function\n    return axis.scale.copy().domain(axisDomain).range(axisRange);\n  }\n  var d3ScaleFunction = getD3ScaleFromType(realScaleType);\n  if (d3ScaleFunction == null) {\n    return undefined;\n  }\n  var scale = d3ScaleFunction.domain(axisDomain).range(axisRange);\n  // I don't like this function because it mutates the scale. We should come up with a way to compute the domain up front.\n  checkDomainOfScale(scale);\n  return scale;\n}\nexport var combineNiceTicks = (axisDomain, axisSettings, realScaleType) => {\n  var domainDefinition = getDomainDefinition(axisSettings);\n  if (realScaleType !== 'auto' && realScaleType !== 'linear') {\n    return undefined;\n  }\n  if (axisSettings != null && axisSettings.tickCount && Array.isArray(domainDefinition) && (domainDefinition[0] === 'auto' || domainDefinition[1] === 'auto') && isWellFormedNumberDomain(axisDomain)) {\n    return getNiceTickValues(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  if (axisSettings != null && axisSettings.tickCount && axisSettings.type === 'number' && isWellFormedNumberDomain(axisDomain)) {\n    return getTickValuesFixedDomain(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  return undefined;\n};\nexport var selectNiceTicks = createSelector([selectAxisDomain, selectAxisSettings, selectRealScaleType], combineNiceTicks);\nexport var combineAxisDomainWithNiceTicks = (axisSettings, domain, niceTicks, axisType) => {\n  if (\n  /*\n   * Angle axis for some reason uses nice ticks when rendering axis tick labels,\n   * but doesn't use nice ticks for extending domain like all the other axes do.\n   * Not really sure why? Is there a good reason,\n   * or is it just because someone added support for nice ticks to the other axes and forgot this one?\n   */\n  axisType !== 'angleAxis' && (axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.type) === 'number' && isWellFormedNumberDomain(domain) && Array.isArray(niceTicks) && niceTicks.length > 0) {\n    var minFromDomain = domain[0];\n    var minFromTicks = niceTicks[0];\n    var maxFromDomain = domain[1];\n    var maxFromTicks = niceTicks[niceTicks.length - 1];\n    return [Math.min(minFromDomain, minFromTicks), Math.max(maxFromDomain, maxFromTicks)];\n  }\n  return domain;\n};\nexport var selectAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectAxisDomain, selectNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);\n\n/**\n * Returns the smallest gap, between two numbers in the data, as a ratio of the whole range (max - min).\n * Ignores domain provided by user and only considers domain from data.\n *\n * The result is a number between 0 and 1.\n */\nexport var selectSmallestDistanceBetweenValues = createSelector(selectAllAppliedValues, selectBaseAxis, (allDataSquished, axisSettings) => {\n  if (!axisSettings || axisSettings.type !== 'number') {\n    return undefined;\n  }\n  var smallestDistanceBetweenValues = Infinity;\n  var sortedValues = Array.from(onlyAllowNumbers(allDataSquished.map(d => d.value))).sort((a, b) => a - b);\n  if (sortedValues.length < 2) {\n    return Infinity;\n  }\n  var diff = sortedValues[sortedValues.length - 1] - sortedValues[0];\n  if (diff === 0) {\n    return Infinity;\n  }\n  // Only do n - 1 distance calculations because there's only n - 1 distances between n values.\n  for (var i = 0; i < sortedValues.length - 1; i++) {\n    var distance = sortedValues[i + 1] - sortedValues[i];\n    smallestDistanceBetweenValues = Math.min(smallestDistanceBetweenValues, distance);\n  }\n  return smallestDistanceBetweenValues / diff;\n});\nvar selectCalculatedPadding = createSelector(selectSmallestDistanceBetweenValues, selectChartLayout, selectBarCategoryGap, selectChartOffsetInternal, (_1, _2, _3, padding) => padding, (smallestDistanceInPercent, layout, barCategoryGap, offset, padding) => {\n  if (!isWellBehavedNumber(smallestDistanceInPercent)) {\n    return 0;\n  }\n  var rangeWidth = layout === 'vertical' ? offset.height : offset.width;\n  if (padding === 'gap') {\n    return smallestDistanceInPercent * rangeWidth / 2;\n  }\n  if (padding === 'no-gap') {\n    var gap = getPercentValue(barCategoryGap, smallestDistanceInPercent * rangeWidth);\n    var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n    return halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n  }\n  return 0;\n});\nexport var selectCalculatedXAxisPadding = (state, axisId) => {\n  var xAxisSettings = selectXAxisSettings(state, axisId);\n  if (xAxisSettings == null || typeof xAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'xAxis', axisId, xAxisSettings.padding);\n};\nexport var selectCalculatedYAxisPadding = (state, axisId) => {\n  var yAxisSettings = selectYAxisSettings(state, axisId);\n  if (yAxisSettings == null || typeof yAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'yAxis', axisId, yAxisSettings.padding);\n};\nvar selectXAxisPadding = createSelector(selectXAxisSettings, selectCalculatedXAxisPadding, (xAxisSettings, calculated) => {\n  var _padding$left, _padding$right;\n  if (xAxisSettings == null) {\n    return {\n      left: 0,\n      right: 0\n    };\n  }\n  var {\n    padding\n  } = xAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      left: calculated,\n      right: calculated\n    };\n  }\n  return {\n    left: ((_padding$left = padding.left) !== null && _padding$left !== void 0 ? _padding$left : 0) + calculated,\n    right: ((_padding$right = padding.right) !== null && _padding$right !== void 0 ? _padding$right : 0) + calculated\n  };\n});\nvar selectYAxisPadding = createSelector(selectYAxisSettings, selectCalculatedYAxisPadding, (yAxisSettings, calculated) => {\n  var _padding$top, _padding$bottom;\n  if (yAxisSettings == null) {\n    return {\n      top: 0,\n      bottom: 0\n    };\n  }\n  var {\n    padding\n  } = yAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      top: calculated,\n      bottom: calculated\n    };\n  }\n  return {\n    top: ((_padding$top = padding.top) !== null && _padding$top !== void 0 ? _padding$top : 0) + calculated,\n    bottom: ((_padding$bottom = padding.bottom) !== null && _padding$bottom !== void 0 ? _padding$bottom : 0) + calculated\n  };\n});\nexport var combineXAxisRange = createSelector([selectChartOffsetInternal, selectXAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, padding, brushDimensions, _ref4, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref4;\n  if (isPanorama) {\n    return [brushPadding.left, brushDimensions.width - brushPadding.right];\n  }\n  return [offset.left + padding.left, offset.left + offset.width - padding.right];\n});\nexport var combineYAxisRange = createSelector([selectChartOffsetInternal, selectChartLayout, selectYAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, layout, padding, brushDimensions, _ref5, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref5;\n  if (isPanorama) {\n    return [brushDimensions.height - brushPadding.bottom, brushPadding.top];\n  }\n  if (layout === 'horizontal') {\n    return [offset.top + offset.height - padding.bottom, offset.top + padding.top];\n  }\n  return [offset.top + padding.top, offset.top + offset.height - padding.bottom];\n});\nexport var selectAxisRange = (state, axisType, axisId, isPanorama) => {\n  var _selectZAxisSettings;\n  switch (axisType) {\n    case 'xAxis':\n      return combineXAxisRange(state, axisId, isPanorama);\n    case 'yAxis':\n      return combineYAxisRange(state, axisId, isPanorama);\n    case 'zAxis':\n      return (_selectZAxisSettings = selectZAxisSettings(state, axisId)) === null || _selectZAxisSettings === void 0 ? void 0 : _selectZAxisSettings.range;\n    case 'angleAxis':\n      return selectAngleAxisRange(state);\n    case 'radiusAxis':\n      return selectRadiusAxisRange(state, axisId);\n    default:\n      return undefined;\n  }\n};\nexport var selectAxisRangeWithReverse = createSelector([selectBaseAxis, selectAxisRange], combineAxisRangeWithReverse);\nexport var selectAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomainIncludingNiceTicks, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectErrorBarsSettings = createSelector([selectCartesianItemsSettings, selectAllErrorBarSettings, pickAxisType], combineRelevantErrorBarSettings);\nfunction compareIds(a, b) {\n  if (a.id < b.id) {\n    return -1;\n  }\n  if (a.id > b.id) {\n    return 1;\n  }\n  return 0;\n}\nvar pickAxisOrientation = (_state, orientation) => orientation;\nvar pickMirror = (_state, _orientation, mirror) => mirror;\nvar selectAllXAxesWithOffsetType = createSelector(selectAllXAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar selectAllYAxesWithOffsetType = createSelector(selectAllYAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar getXAxisSize = (offset, axisSettings) => {\n  return {\n    width: offset.width,\n    height: axisSettings.height\n  };\n};\nvar getYAxisSize = (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n};\nexport var selectXAxisSize = createSelector(selectChartOffsetInternal, selectXAxisSettings, getXAxisSize);\nvar combineXAxisPositionStartingPoint = (offset, orientation, chartHeight) => {\n  switch (orientation) {\n    case 'top':\n      return offset.top;\n    case 'bottom':\n      return chartHeight - offset.bottom;\n    default:\n      return 0;\n  }\n};\nvar combineYAxisPositionStartingPoint = (offset, orientation, chartWidth) => {\n  switch (orientation) {\n    case 'left':\n      return offset.left;\n    case 'right':\n      return chartWidth - offset.right;\n    default:\n      return 0;\n  }\n};\nexport var selectAllXAxesOffsetSteps = createSelector(selectChartHeight, selectChartOffsetInternal, selectAllXAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartHeight, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getXAxisSize(offset, axis);\n    if (position == null) {\n      position = combineXAxisPositionStartingPoint(offset, orientation, chartHeight);\n    }\n    var needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.height;\n    position += (needSpace ? -1 : 1) * axisSize.height;\n  });\n  return steps;\n});\nexport var selectAllYAxesOffsetSteps = createSelector(selectChartWidth, selectChartOffsetInternal, selectAllYAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartWidth, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getYAxisSize(offset, axis);\n    if (position == null) {\n      position = combineYAxisPositionStartingPoint(offset, orientation, chartWidth);\n    }\n    var needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.width;\n    position += (needSpace ? -1 : 1) * axisSize.width;\n  });\n  return steps;\n});\nexport var selectXAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectXAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllXAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: offset.left,\n      y: 0\n    };\n  }\n  return {\n    x: offset.left,\n    y: stepOfThisAxis\n  };\n};\nexport var selectYAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectYAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllYAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: 0,\n      y: offset.top\n    };\n  }\n  return {\n    x: stepOfThisAxis,\n    y: offset.top\n  };\n};\nexport var selectYAxisSize = createSelector(selectChartOffsetInternal, selectYAxisSettings, (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n});\nexport var selectCartesianAxisSize = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSize(state, axisId).width;\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSize(state, axisId).height;\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n};\nexport var combineDuplicateDomain = (chartLayout, appliedValues, axis, axisType) => {\n  if (axis == null) {\n    return undefined;\n  }\n  var {\n    allowDuplicatedCategory,\n    type,\n    dataKey\n  } = axis;\n  var isCategorical = isCategoricalAxis(chartLayout, axisType);\n  var allData = appliedValues.map(av => av.value);\n  if (dataKey && isCategorical && type === 'category' && allowDuplicatedCategory && hasDuplicate(allData)) {\n    return allData;\n  }\n  return undefined;\n};\nexport var selectDuplicateDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectBaseAxis, pickAxisType], combineDuplicateDomain);\nexport var combineCategoricalDomain = (layout, appliedValues, axis, axisType) => {\n  if (axis == null || axis.dataKey == null) {\n    return undefined;\n  }\n  var {\n    type,\n    scale\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && (type === 'number' || scale !== 'auto')) {\n    return appliedValues.map(d => d.value);\n  }\n  return undefined;\n};\nexport var selectCategoricalDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectAxisPropsNeededForCartesianGridTicksGenerator = createSelector([selectChartLayout, selectCartesianAxisSettings, selectRealScaleType, selectAxisScale, selectDuplicateDomain, selectCategoricalDomain, selectAxisRange, selectNiceTicks, pickAxisType], (layout, axis, realScaleType, scale, duplicateDomain, categoricalDomain, axisRange, niceTicks, axisType) => {\n  if (axis == null) {\n    return null;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  return {\n    angle: axis.angle,\n    interval: axis.interval,\n    minTickGap: axis.minTickGap,\n    orientation: axis.orientation,\n    tick: axis.tick,\n    tickCount: axis.tickCount,\n    tickFormatter: axis.tickFormatter,\n    ticks: axis.ticks,\n    type: axis.type,\n    unit: axis.unit,\n    axisType,\n    categoricalDomain,\n    duplicateDomain,\n    isCategorical,\n    niceTicks,\n    range: axisRange,\n    realScaleType,\n    scale\n  };\n});\nexport var combineAxisTicks = (layout, axis, realScaleType, scale, niceTicks, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    type,\n    ticks,\n    tickCount\n  } = axis;\n\n  // This is testing for `scaleBand` but for band axis the type is reported as `band` so this looks like a dead code with a workaround elsewhere?\n  var offsetForBand = realScaleType === 'scaleBand' && typeof scale.bandwidth === 'function' ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && axisRange != null && axisRange.length >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  var ticksOrNiceTicks = ticks || niceTicks;\n  if (ticksOrNiceTicks) {\n    var result = ticksOrNiceTicks.map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        index,\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfAxis = createSelector([selectChartLayout, selectAxisSettings, selectRealScaleType, selectAxisScale, selectNiceTicks, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var combineGraphicalItemTicks = (layout, axis, scale, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null || axisRange == null || axisRange[0] === axisRange[1]) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    tickCount\n  } = axis;\n  var offset = 0;\n  offset = axisType === 'angleAxis' && (axisRange === null || axisRange === void 0 ? void 0 : axisRange.length) >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfGraphicalItem = createSelector([selectChartLayout, selectAxisSettings, selectAxisScale, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineGraphicalItemTicks);\nexport var selectAxisWithScale = createSelector(selectBaseAxis, selectAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\nvar selectZAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomain, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectZAxisWithScale = createSelector((state, _axisType, axisId) => selectZAxisSettings(state, axisId), selectZAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\n\n/**\n * We are also going to need to implement polar chart directions if we want to support keyboard controls for those.\n */\n\nexport var selectChartDirection = createSelector([selectChartLayout, selectAllXAxes, selectAllYAxes], (layout, allXAxes, allYAxes) => {\n  switch (layout) {\n    case 'horizontal':\n      {\n        return allXAxes.some(axis => axis.reversed) ? 'right-to-left' : 'left-to-right';\n      }\n    case 'vertical':\n      {\n        return allYAxes.some(axis => axis.reversed) ? 'bottom-to-top' : 'top-to-bottom';\n      }\n    // TODO: make this better. For now, right arrow triggers \"forward\", left arrow \"back\"\n    // however, the tooltip moves an unintuitive direction because of how the indices are rendered\n    case 'centric':\n    case 'radial':\n      {\n        return 'left-to-right';\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "range", "d3Scales", "selectChartLayout", "checkDomainOfScale", "getDomainOfStackGroups", "getStackedData", "getValueByDataKey", "isCategoricalAxis", "selectChartDataWithIndexes", "selectChartDataWithIndexesIfNotInPanorama", "isWellFormedNumberDomain", "numericalDomainSpecifiedWithoutRequiringData", "parseNumericalUserDomain", "getPercentValue", "hasDuplicate", "isNan", "isNumber", "isNumOrStr", "mathSign", "upperFirst", "isWellBehavedNumber", "getNiceTickValues", "getTickValuesFixedDomain", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAllXAxes", "selectAllYAxes", "selectChartOffsetInternal", "selectBrushDimensions", "selectBrushSettings", "selectBarCategoryGap", "selectChartName", "selectStackOffsetType", "selectAngleAxis", "selectAngleAxisRange", "selectRadiusAxis", "selectRadiusAxisRange", "pickAxisType", "pickAxisId", "combineAxisRangeWithReverse", "DEFAULT_Y_AXIS_WIDTH", "getStackSeriesIdentifier", "selectTooltipAxis", "combineDisplayedStackedData", "isStacked", "defaultNumericDomain", "implicitXAxis", "allowDataOverflow", "allowDecimals", "allowDuplicatedCategory", "angle", "dataKey", "undefined", "domain", "height", "hide", "id", "includeHidden", "interval", "minTickGap", "mirror", "name", "orientation", "padding", "left", "right", "reversed", "scale", "tick", "tickCount", "tick<PERSON><PERSON><PERSON><PERSON>", "ticks", "type", "unit", "selectXAxisSettings", "state", "axisId", "axis", "cartesianAxis", "xAxis", "implicitYAxis", "top", "bottom", "width", "selectYAxisSettings", "yAxis", "implicitZAxis", "selectZAxisSettings", "zAxis", "selectBaseAxis", "axisType", "Error", "concat", "selectCartesianAxisSettings", "selectAxisSettings", "selectHasBar", "graphicalItems", "cartesianItems", "some", "item", "polarItems", "itemAxisPredicate", "xAxisId", "yAxisId", "zAxisId", "angleAxisId", "radiusAxisId", "selectUnfilteredCartesianItems", "selectAxisPredicate", "combineGraphicalItemsSettings", "axisSettings", "axisPredicate", "selectCartesianItemsSettings", "selectStackedCartesianItemsSettings", "filterGraphicalNotStackedItems", "stackId", "selectCartesianItemsSettingsExceptStacked", "combineGraphicalItemsData", "map", "data", "Boolean", "flat", "selectCartesianGraphicalItemsData", "combineDisplayedData", "graphicalItemsData", "_ref", "chartData", "dataStartIndex", "dataEndIndex", "slice", "selectDisplayedData", "combineAppliedValues", "items", "flatMap", "entry", "selectAllAppliedValues", "isErrorBarRelevantForAxisType", "errorBar", "direction", "fromMainValueToError", "isFinite", "Array", "isArray", "minError", "Math", "min", "maxError", "max", "onlyAllow<PERSON>umbers", "v", "Date", "n", "getErrorDomainByDataKey", "appliedValue", "relevantErrorBars", "eb", "errorValue", "lowBound", "highBound", "selectDisplayedStackedData", "combineStackGroups", "displayedData", "stackOffsetType", "initialItemsGroups", "itemsGroup", "reduce", "acc", "fromEntries", "entries", "_ref2", "dataKeys", "stackedData", "selectStackGroups", "combineDomainOfStackGroups", "stackGroups", "_ref3", "domainOfStackGroups", "selectDomainOfStackGroups", "combineAppliedNumericalValuesIncludingErrorValues", "errorBars", "_errorBars$item$id", "_axisSettings$dataKey", "valueByDataKey", "errorDomain", "selectAllErrorBarSettings", "combineRelevantErrorBarSettings", "cartesianItemsSettings", "allErrorBarSettings", "selectErrorBarsSettingsExceptStacked", "selectAllAppliedNumericalValuesIncludingErrorValues", "onlyAllowNumbersAndStringsAndDates", "computeNumericalDomain", "dataWithErrorDomains", "allDataSquished", "d", "only<PERSON><PERSON>bers", "computeDomainOfTypeCategory", "isCategorical", "categoricalDomain", "from", "Set", "getDomainDefinition", "_axisSettings$domain", "allValues", "mergeDomains", "_len", "domains", "_key", "allDomains", "selectReferenceDots", "referenceElements", "dots", "filterReferenceElements", "elements", "el", "ifOverflow", "selectReferenceDotsByAxis", "selectReferenceAreas", "areas", "selectReferenceAreasByAxis", "selectReferenceLines", "lines", "selectReferenceLinesByAxis", "combineDotsDomain", "allCoords", "dot", "x", "y", "selectReferenceDotsDomain", "combineAreasDomain", "area", "x1", "y1", "x2", "y2", "selectReferenceAreasDomain", "combineLinesDomain", "line", "selectReferenceLinesDomain", "selectReferenceElementsDomain", "dotsDomain", "linesDomain", "areasDomain", "selectDomainDefinition", "combineNumericalDomain", "domainDefinition", "allDataWithErrorDomains", "referenceElementsDomain", "layout", "domainFromUserPreference", "shouldIncludeDomainOfStackGroups", "mergedDomains", "selectNumericalDomain", "expandDomain", "combineAxisDomain", "allAppliedValues", "numericalDomain", "selectAxisDomain", "combineRealScaleType", "axisConfig", "<PERSON><PERSON><PERSON>", "chartType", "indexOf", "selectRealScaleType", "getD3ScaleFromType", "realScaleType", "combineScaleFunction", "axisDomain", "axisRange", "copy", "d3ScaleFunction", "combineNiceTicks", "selectNiceTicks", "combineAxisDomainWithNiceTicks", "niceTicks", "minFromDomain", "minFromTicks", "maxFromDomain", "maxFromTicks", "selectAxisDomainIncludingNiceTicks", "selectSmallestDistanceBetweenValues", "smallestDistanceBetweenValues", "Infinity", "sortedValues", "sort", "a", "b", "diff", "distance", "selectCalculatedPadding", "_1", "_2", "_3", "smallestDistanceInPercent", "barCategoryGap", "offset", "rangeWidth", "gap", "halfBand", "selectCalculatedXAxisPadding", "xAxisSettings", "selectCalculatedYAxisPadding", "yAxisSettings", "selectXAxisPadding", "calculated", "_padding$left", "_padding$right", "selectYAxisPadding", "_padding$top", "_padding$bottom", "combineXAxisRange", "_state", "_axisId", "isPanorama", "brushDimensions", "_ref4", "brushPadding", "combineYAxisRange", "_ref5", "selectAxisRange", "_selectZAxisSettings", "selectAxisRangeWithReverse", "selectAxisScale", "selectErrorBarsSettings", "compareIds", "pickAxisOrientation", "pick<PERSON><PERSON><PERSON>r", "_orientation", "selectAllXAxesWithOffsetType", "allAxes", "selectAllYAxesWithOffsetType", "getXAxisSize", "getYAxisSize", "selectXAxisSize", "combineXAxisPositionStartingPoint", "chartHeight", "combineYAxisPositionStartingPoint", "chartWidth", "selectAllXAxesOffsetSteps", "allAxesWithSameOffsetType", "steps", "position", "axisSize", "needSpace", "selectAllYAxesOffsetSteps", "selectXAxisPosition", "allSteps", "stepOfThisAxis", "selectYAxisPosition", "selectYAxisSize", "selectCartesianAxisSize", "combineDuplicateDomain", "chartLayout", "appliedValues", "allData", "av", "selectDuplicateDomain", "combineCategoricalDomain", "selectCategoricalDomain", "selectAxisPropsNeededForCartesianGridTicksGenerator", "duplicateDomain", "combineAxisTicks", "offsetForBand", "bandwidth", "ticksOrNiceTicks", "result", "index", "scaleContent", "coordinate", "row", "selectTicksOfAxis", "combineGraphicalItemTicks", "selectTicksOfGraphicalItem", "selectAxisWithScale", "selectZAxisScale", "selectZAxisWithScale", "_axisType", "selectChartDirection", "allXAxes", "allYAxes"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/axisSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport range from 'es-toolkit/compat/range';\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { checkDomainOfScale, getDomainOfStackGroups, getStackedData, getValueByDataKey, isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes, selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { isWellFormedNumberDomain, numericalDomainSpecifiedWithoutRequiringData, parseNumericalUserDomain } from '../../util/isDomainSpecifiedByUser';\nimport { getPercentValue, hasDuplicate, isNan, isNumber, isNumOrStr, mathSign, upperFirst } from '../../util/DataUtils';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getNiceTickValues, getTickValuesFixedDomain } from '../../util/scale';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBrushDimensions, selectBrushSettings } from './brushSelectors';\nimport { selectBarCategoryGap, selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { selectAngleAxis, selectAngleAxisRange, selectRadiusAxis, selectRadiusAxisRange } from './polarAxisSelectors';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nimport { selectTooltipAxis } from './selectTooltipAxis';\nimport { combineDisplayedStackedData } from './combiners/combineDisplayedStackedData';\nimport { isStacked } from '../types/StackedGraphicalItem';\nvar defaultNumericDomain = [0, 'auto'];\n\n/**\n * angle, radius, X, Y, and Z axes all have domain and range and scale and associated settings\n */\n\n/**\n * X and Y axes have ticks. Z axis is never displayed and so it lacks ticks\n * and tick settings.\n */\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitXAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: undefined,\n  height: 30,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'bottom',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectXAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.xAxis[axisId];\n  if (axis == null) {\n    return implicitXAxis;\n  }\n  return axis;\n};\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitYAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: defaultNumericDomain,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'left',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined,\n  width: DEFAULT_Y_AXIS_WIDTH\n};\nexport var selectYAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.yAxis[axisId];\n  if (axis == null) {\n    return implicitYAxis;\n  }\n  return axis;\n};\nexport var implicitZAxis = {\n  domain: [0, 'auto'],\n  includeHidden: false,\n  reversed: false,\n  allowDataOverflow: false,\n  allowDuplicatedCategory: false,\n  dataKey: undefined,\n  id: 0,\n  name: '',\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number',\n  unit: ''\n};\nexport var selectZAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.zAxis[axisId];\n  if (axis == null) {\n    return implicitZAxis;\n  }\n  return axis;\n};\nexport var selectBaseAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'zAxis':\n      {\n        return selectZAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\nvar selectCartesianAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * Selects either an X or Y axis. Doesn't work with Z axis - for that, instead use selectBaseAxis.\n * @param state Root state\n * @param axisType xAxis | yAxis\n * @param axisId xAxisId | yAxisId\n * @returns axis settings object\n */\nexport var selectAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * @param state RechartsRootState\n * @return boolean true if there is at least one Bar or RadialBar\n */\nexport var selectHasBar = state => state.graphicalItems.cartesianItems.some(item => item.type === 'bar') || state.graphicalItems.polarItems.some(item => item.type === 'radialBar');\n\n/**\n * Filters CartesianGraphicalItemSettings by the relevant axis ID\n * @param axisType 'xAxis' | 'yAxis' | 'zAxis' | 'radiusAxis' | 'angleAxis'\n * @param axisId from props, defaults to 0\n *\n * @returns Predicate function that return true for CartesianGraphicalItemSettings that are relevant to the specified axis\n */\nexport function itemAxisPredicate(axisType, axisId) {\n  return item => {\n    switch (axisType) {\n      case 'xAxis':\n        // This is sensitive to the data type, as 0 !== '0'. I wonder if we should be more flexible. How does 2.x branch behave? TODO write test for that\n        return 'xAxisId' in item && item.xAxisId === axisId;\n      case 'yAxis':\n        return 'yAxisId' in item && item.yAxisId === axisId;\n      case 'zAxis':\n        return 'zAxisId' in item && item.zAxisId === axisId;\n      case 'angleAxis':\n        return 'angleAxisId' in item && item.angleAxisId === axisId;\n      case 'radiusAxis':\n        return 'radiusAxisId' in item && item.radiusAxisId === axisId;\n      default:\n        return false;\n    }\n  };\n}\nexport var selectUnfilteredCartesianItems = state => state.graphicalItems.cartesianItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var combineGraphicalItemsSettings = (graphicalItems, axisSettings, axisPredicate) => graphicalItems.filter(axisPredicate).filter(item => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.includeHidden) === true) {\n    return true;\n  }\n  return !item.hide;\n});\nexport var selectCartesianItemsSettings = createSelector([selectUnfilteredCartesianItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nexport var selectStackedCartesianItemsSettings = createSelector([selectCartesianItemsSettings], cartesianItems => {\n  return cartesianItems.filter(item => item.type === 'area' || item.type === 'bar').filter(isStacked);\n});\nexport var filterGraphicalNotStackedItems = cartesianItems => cartesianItems.filter(item => !('stackId' in item) || item.stackId === undefined);\nvar selectCartesianItemsSettingsExceptStacked = createSelector([selectCartesianItemsSettings], filterGraphicalNotStackedItems);\nexport var combineGraphicalItemsData = cartesianItems => cartesianItems.map(item => item.data).filter(Boolean).flat(1);\n\n/**\n * This is a \"cheap\" selector - it returns the data but doesn't iterate them, so it is not sensitive on the array length.\n * Also does not apply dataKey yet.\n * @param state RechartsRootState\n * @returns data defined on the chart graphical items, such as Line or Scatter or Pie, and filtered with appropriate dataKey\n */\nexport var selectCartesianGraphicalItemsData = createSelector([selectCartesianItemsSettings], combineGraphicalItemsData);\nexport var combineDisplayedData = (graphicalItemsData, _ref) => {\n  var {\n    chartData = [],\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (graphicalItemsData.length > 0) {\n    /*\n     * There is no slicing when data is defined on graphical items. Why?\n     * Because Brush ignores data defined on graphical items,\n     * and does not render.\n     * So Brush will never show up in a Scatter chart for example.\n     * This is something we will need to fix.\n     *\n     * Now, when the root chart data is not defined, the dataEndIndex is 0,\n     * which means the itemsData will be sliced to an empty array anyway.\n     * But that's an implementation detail, and we can fix that too.\n     *\n     * Also, in absence of Axis dataKey, we use the dataKey from each item, respectively.\n     * This is the usual pattern for numerical axis, that is the one where bars go up:\n     * users don't specify any dataKey by default and expect the axis to \"just match the data\".\n     */\n    return graphicalItemsData;\n  }\n  return chartData.slice(dataStartIndex, dataEndIndex + 1);\n};\n\n/**\n * This selector will return all data there is in the chart: graphical items, chart root, all together.\n * Useful for figuring out an axis domain (because that needs to know of everything),\n * not useful for rendering individual graphical elements (because they need to know which data is theirs and which is not).\n *\n * This function will discard the original indexes, so it is also not useful for anything that depends on ordering.\n */\nexport var selectDisplayedData = createSelector([selectCartesianGraphicalItemsData, selectChartDataWithIndexesIfNotInPanorama], combineDisplayedData);\nexport var combineAppliedValues = (data, axisSettings, items) => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey)\n    }));\n  }\n  if (items.length > 0) {\n    return items.map(item => item.dataKey).flatMap(dataKey => data.map(entry => ({\n      value: getValueByDataKey(entry, dataKey)\n    })));\n  }\n  return data.map(entry => ({\n    value: entry\n  }));\n};\n\n/**\n * This selector will return all values with the appropriate dataKey applied on them.\n * Which dataKey is appropriate depends on where it is defined.\n *\n * This is an expensive selector - it will iterate all data and compute their value using the provided dataKey.\n */\nexport var selectAllAppliedValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettings], combineAppliedValues);\nexport function isErrorBarRelevantForAxisType(axisType, errorBar) {\n  switch (axisType) {\n    case 'xAxis':\n      return errorBar.direction === 'x';\n    case 'yAxis':\n      return errorBar.direction === 'y';\n    default:\n      return false;\n  }\n}\n\n/**\n * This is type of \"error\" in chart. It is set by using ErrorBar, and it can represent confidence interval,\n * or gap in the data, or standard deviation, or quartiles in boxplot, or whiskers or whatever.\n *\n * We will internally represent it as a tuple of two numbers, where the first number is the lower bound and the second number is the upper bound.\n *\n * It is also true that the first number should be lower than or equal to the associated \"main value\",\n * and the second number should be higher than or equal to the associated \"main value\".\n */\n\nexport function fromMainValueToError(value) {\n  if (isNumber(value) && Number.isFinite(value)) {\n    return [value, value];\n  }\n  if (Array.isArray(value)) {\n    var minError = Math.min(...value);\n    var maxError = Math.max(...value);\n    if (!isNan(minError) && !isNan(maxError) && Number.isFinite(minError) && Number.isFinite(maxError)) {\n      return [minError, maxError];\n    }\n  }\n  return undefined;\n}\nfunction onlyAllowNumbers(data) {\n  return data.filter(v => isNumOrStr(v) || v instanceof Date).map(Number).filter(n => isNan(n) === false);\n}\n\n/**\n * @param entry One item in the 'data' array. Could be anything really - this is defined externally. This is the raw, before dataKey application\n * @param appliedValue This is the result of applying the 'main' dataKey on the `entry`.\n * @param relevantErrorBars Error bars that are relevant for the current axis and layout and all that.\n * @return either undefined or an array of ErrorValue\n */\nexport function getErrorDomainByDataKey(entry, appliedValue, relevantErrorBars) {\n  if (!relevantErrorBars || typeof appliedValue !== 'number' || isNan(appliedValue)) {\n    return [];\n  }\n  if (!relevantErrorBars.length) {\n    return [];\n  }\n  return onlyAllowNumbers(relevantErrorBars.flatMap(eb => {\n    var errorValue = getValueByDataKey(entry, eb.dataKey);\n    var lowBound, highBound;\n    if (Array.isArray(errorValue)) {\n      [lowBound, highBound] = errorValue;\n    } else {\n      lowBound = highBound = errorValue;\n    }\n    if (!isWellBehavedNumber(lowBound) || !isWellBehavedNumber(highBound)) {\n      return undefined;\n    }\n    return [appliedValue - lowBound, appliedValue + highBound];\n  }));\n}\nexport var selectDisplayedStackedData = createSelector([selectStackedCartesianItemsSettings, selectChartDataWithIndexesIfNotInPanorama, selectTooltipAxis], combineDisplayedStackedData);\nexport var combineStackGroups = (displayedData, items, stackOffsetType) => {\n  var initialItemsGroups = {};\n  var itemsGroup = items.reduce((acc, item) => {\n    if (item.stackId == null) {\n      return acc;\n    }\n    if (acc[item.stackId] == null) {\n      acc[item.stackId] = [];\n    }\n    acc[item.stackId].push(item);\n    return acc;\n  }, initialItemsGroups);\n  return Object.fromEntries(Object.entries(itemsGroup).map(_ref2 => {\n    var [stackId, graphicalItems] = _ref2;\n    var dataKeys = graphicalItems.map(getStackSeriesIdentifier);\n    return [stackId, {\n      // @ts-expect-error getStackedData requires that the input is array of objects, Recharts does not test for that\n      stackedData: getStackedData(displayedData, dataKeys, stackOffsetType),\n      graphicalItems\n    }];\n  }));\n};\n\n/**\n * Stack groups are groups of graphical items that stack on each other.\n * Stack is a function of axis type (X, Y), axis ID, and stack ID.\n * Graphical items that do not have a stack ID are not going to be present in stack groups.\n */\nexport var selectStackGroups = createSelector([selectDisplayedStackedData, selectStackedCartesianItemsSettings, selectStackOffsetType], combineStackGroups);\nexport var combineDomainOfStackGroups = (stackGroups, _ref3, axisType) => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (axisType === 'zAxis') {\n    // ZAxis ignores stacks\n    return undefined;\n  }\n  var domainOfStackGroups = getDomainOfStackGroups(stackGroups, dataStartIndex, dataEndIndex);\n  if (domainOfStackGroups != null && domainOfStackGroups[0] === 0 && domainOfStackGroups[1] === 0) {\n    return undefined;\n  }\n  return domainOfStackGroups;\n};\nexport var selectDomainOfStackGroups = createSelector([selectStackGroups, selectChartDataWithIndexes, pickAxisType], combineDomainOfStackGroups);\nexport var combineAppliedNumericalValuesIncludingErrorValues = (data, axisSettings, items, errorBars, axisType) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _errorBars$item$id, _axisSettings$dataKey;\n        var relevantErrorBars = (_errorBars$item$id = errorBars[item.id]) === null || _errorBars$item$id === void 0 ? void 0 : _errorBars$item$id.filter(errorBar => isErrorBarRelevantForAxisType(axisType, errorBar));\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: getErrorDomainByDataKey(entry, valueByDataKey, relevantErrorBars)\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n};\nexport var selectAllErrorBarSettings = state => state.errorBars;\nvar combineRelevantErrorBarSettings = (cartesianItemsSettings, allErrorBarSettings, axisType) => {\n  return cartesianItemsSettings.flatMap(item => {\n    return allErrorBarSettings[item.id];\n  }).filter(Boolean).filter(e => {\n    return isErrorBarRelevantForAxisType(axisType, e);\n  });\n};\nexport var selectErrorBarsSettingsExceptStacked = createSelector([selectCartesianItemsSettingsExceptStacked, selectAllErrorBarSettings, pickAxisType], combineRelevantErrorBarSettings);\nexport var selectAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettingsExceptStacked, selectAllErrorBarSettings, pickAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nfunction onlyAllowNumbersAndStringsAndDates(item) {\n  var {\n    value\n  } = item;\n  if (isNumOrStr(value) || value instanceof Date) {\n    return value;\n  }\n  return undefined;\n}\nvar computeNumericalDomain = dataWithErrorDomains => {\n  var allDataSquished = dataWithErrorDomains\n  // This flatMap has to be flat because we're creating a new array in the return value\n  .flatMap(d => [d.value, d.errorDomain])\n  // This flat is needed because a) errorDomain is an array, and b) value may be a number, or it may be a range (for Area, for example)\n  .flat(1);\n  var onlyNumbers = onlyAllowNumbers(allDataSquished);\n  if (onlyNumbers.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...onlyNumbers), Math.max(...onlyNumbers)];\n};\nvar computeDomainOfTypeCategory = (allDataSquished, axisSettings, isCategorical) => {\n  var categoricalDomain = allDataSquished.map(onlyAllowNumbersAndStringsAndDates).filter(v => v != null);\n  if (isCategorical && (axisSettings.dataKey == null || axisSettings.allowDuplicatedCategory && hasDuplicate(categoricalDomain))) {\n    /*\n     * 1. In an absence of dataKey, Recharts will use array indexes as its categorical domain\n     * 2. When category axis has duplicated text, serial numbers are used to generate scale\n     */\n    return range(0, allDataSquished.length);\n  }\n  if (axisSettings.allowDuplicatedCategory) {\n    return categoricalDomain;\n  }\n  return Array.from(new Set(categoricalDomain));\n};\nexport var getDomainDefinition = axisSettings => {\n  var _axisSettings$domain;\n  if (axisSettings == null || !('domain' in axisSettings)) {\n    return defaultNumericDomain;\n  }\n  if (axisSettings.domain != null) {\n    return axisSettings.domain;\n  }\n  if (axisSettings.ticks != null) {\n    if (axisSettings.type === 'number') {\n      var allValues = onlyAllowNumbers(axisSettings.ticks);\n      return [Math.min(...allValues), Math.max(...allValues)];\n    }\n    if (axisSettings.type === 'category') {\n      return axisSettings.ticks.map(String);\n    }\n  }\n  return (_axisSettings$domain = axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.domain) !== null && _axisSettings$domain !== void 0 ? _axisSettings$domain : defaultNumericDomain;\n};\nexport var mergeDomains = function mergeDomains() {\n  for (var _len = arguments.length, domains = new Array(_len), _key = 0; _key < _len; _key++) {\n    domains[_key] = arguments[_key];\n  }\n  var allDomains = domains.filter(Boolean);\n  if (allDomains.length === 0) {\n    return undefined;\n  }\n  var allValues = allDomains.flat();\n  var min = Math.min(...allValues);\n  var max = Math.max(...allValues);\n  return [min, max];\n};\nexport var selectReferenceDots = state => state.referenceElements.dots;\nexport var filterReferenceElements = (elements, axisType, axisId) => {\n  return elements.filter(el => el.ifOverflow === 'extendDomain').filter(el => {\n    if (axisType === 'xAxis') {\n      return el.xAxisId === axisId;\n    }\n    return el.yAxisId === axisId;\n  });\n};\nexport var selectReferenceDotsByAxis = createSelector([selectReferenceDots, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceAreas = state => state.referenceElements.areas;\nexport var selectReferenceAreasByAxis = createSelector([selectReferenceAreas, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceLines = state => state.referenceElements.lines;\nexport var selectReferenceLinesByAxis = createSelector([selectReferenceLines, pickAxisType, pickAxisId], filterReferenceElements);\nexport var combineDotsDomain = (dots, axisType) => {\n  var allCoords = onlyAllowNumbers(dots.map(dot => axisType === 'xAxis' ? dot.x : dot.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceDotsDomain = createSelector(selectReferenceDotsByAxis, pickAxisType, combineDotsDomain);\nexport var combineAreasDomain = (areas, axisType) => {\n  var allCoords = onlyAllowNumbers(areas.flatMap(area => [axisType === 'xAxis' ? area.x1 : area.y1, axisType === 'xAxis' ? area.x2 : area.y2]));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceAreasDomain = createSelector([selectReferenceAreasByAxis, pickAxisType], combineAreasDomain);\nexport var combineLinesDomain = (lines, axisType) => {\n  var allCoords = onlyAllowNumbers(lines.map(line => axisType === 'xAxis' ? line.x : line.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceLinesDomain = createSelector(selectReferenceLinesByAxis, pickAxisType, combineLinesDomain);\nvar selectReferenceElementsDomain = createSelector(selectReferenceDotsDomain, selectReferenceLinesDomain, selectReferenceAreasDomain, (dotsDomain, linesDomain, areasDomain) => {\n  return mergeDomains(dotsDomain, areasDomain, linesDomain);\n});\nexport var selectDomainDefinition = createSelector([selectBaseAxis], getDomainDefinition);\nexport var combineNumericalDomain = (axisSettings, domainDefinition, domainOfStackGroups, allDataWithErrorDomains, referenceElementsDomain, layout, axisType) => {\n  var domainFromUserPreference = numericalDomainSpecifiedWithoutRequiringData(domainDefinition, axisSettings.allowDataOverflow);\n  if (domainFromUserPreference != null) {\n    // We're done! No need to compute anything else.\n    return domainFromUserPreference;\n  }\n  var shouldIncludeDomainOfStackGroups = layout === 'vertical' && axisType === 'xAxis' || layout === 'horizontal' && axisType === 'yAxis';\n  var mergedDomains = shouldIncludeDomainOfStackGroups ? mergeDomains(domainOfStackGroups, referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains)) : mergeDomains(referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains));\n  return parseNumericalUserDomain(domainDefinition, mergedDomains, axisSettings.allowDataOverflow);\n};\nexport var selectNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, selectDomainOfStackGroups, selectAllAppliedNumericalValuesIncludingErrorValues, selectReferenceElementsDomain, selectChartLayout, pickAxisType], combineNumericalDomain);\n\n/**\n * Expand by design maps everything between 0 and 1,\n * there is nothing to compute.\n * See https://d3js.org/d3-shape/stack#stack-offsets\n */\nvar expandDomain = [0, 1];\nexport var combineAxisDomain = (axisSettings, layout, displayedData, allAppliedValues, stackOffsetType, axisType, numericalDomain) => {\n  if ((axisSettings == null || displayedData == null || displayedData.length === 0) && numericalDomain === undefined) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    type\n  } = axisSettings;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && dataKey == null) {\n    return range(0, displayedData.length);\n  }\n  if (type === 'category') {\n    return computeDomainOfTypeCategory(allAppliedValues, axisSettings, isCategorical);\n  }\n  if (stackOffsetType === 'expand') {\n    return expandDomain;\n  }\n  return numericalDomain;\n};\nexport var selectAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectDisplayedData, selectAllAppliedValues, selectStackOffsetType, pickAxisType, selectNumericalDomain], combineAxisDomain);\nexport var combineRealScaleType = (axisConfig, layout, hasBar, chartType, axisType) => {\n  if (axisConfig == null) {\n    return undefined;\n  }\n  var {\n    scale,\n    type\n  } = axisConfig;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return 'band';\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return 'linear';\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return 'point';\n    }\n    if (type === 'category') {\n      return 'band';\n    }\n    return 'linear';\n  }\n  if (typeof scale === 'string') {\n    var name = \"scale\".concat(upperFirst(scale));\n    return name in d3Scales ? name : 'point';\n  }\n  return undefined;\n};\nexport var selectRealScaleType = createSelector([selectBaseAxis, selectChartLayout, selectHasBar, selectChartName, pickAxisType], combineRealScaleType);\nfunction getD3ScaleFromType(realScaleType) {\n  if (realScaleType == null) {\n    return undefined;\n  }\n  if (realScaleType in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[realScaleType]();\n  }\n  var name = \"scale\".concat(upperFirst(realScaleType));\n  if (name in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[name]();\n  }\n  return undefined;\n}\nexport function combineScaleFunction(axis, realScaleType, axisDomain, axisRange) {\n  if (axisDomain == null || axisRange == null) {\n    return undefined;\n  }\n  if (typeof axis.scale === 'function') {\n    // @ts-expect-error we're going to assume here that if axis.scale is a function then it is a d3Scale function\n    return axis.scale.copy().domain(axisDomain).range(axisRange);\n  }\n  var d3ScaleFunction = getD3ScaleFromType(realScaleType);\n  if (d3ScaleFunction == null) {\n    return undefined;\n  }\n  var scale = d3ScaleFunction.domain(axisDomain).range(axisRange);\n  // I don't like this function because it mutates the scale. We should come up with a way to compute the domain up front.\n  checkDomainOfScale(scale);\n  return scale;\n}\nexport var combineNiceTicks = (axisDomain, axisSettings, realScaleType) => {\n  var domainDefinition = getDomainDefinition(axisSettings);\n  if (realScaleType !== 'auto' && realScaleType !== 'linear') {\n    return undefined;\n  }\n  if (axisSettings != null && axisSettings.tickCount && Array.isArray(domainDefinition) && (domainDefinition[0] === 'auto' || domainDefinition[1] === 'auto') && isWellFormedNumberDomain(axisDomain)) {\n    return getNiceTickValues(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  if (axisSettings != null && axisSettings.tickCount && axisSettings.type === 'number' && isWellFormedNumberDomain(axisDomain)) {\n    return getTickValuesFixedDomain(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  return undefined;\n};\nexport var selectNiceTicks = createSelector([selectAxisDomain, selectAxisSettings, selectRealScaleType], combineNiceTicks);\nexport var combineAxisDomainWithNiceTicks = (axisSettings, domain, niceTicks, axisType) => {\n  if (\n  /*\n   * Angle axis for some reason uses nice ticks when rendering axis tick labels,\n   * but doesn't use nice ticks for extending domain like all the other axes do.\n   * Not really sure why? Is there a good reason,\n   * or is it just because someone added support for nice ticks to the other axes and forgot this one?\n   */\n  axisType !== 'angleAxis' && (axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.type) === 'number' && isWellFormedNumberDomain(domain) && Array.isArray(niceTicks) && niceTicks.length > 0) {\n    var minFromDomain = domain[0];\n    var minFromTicks = niceTicks[0];\n    var maxFromDomain = domain[1];\n    var maxFromTicks = niceTicks[niceTicks.length - 1];\n    return [Math.min(minFromDomain, minFromTicks), Math.max(maxFromDomain, maxFromTicks)];\n  }\n  return domain;\n};\nexport var selectAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectAxisDomain, selectNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);\n\n/**\n * Returns the smallest gap, between two numbers in the data, as a ratio of the whole range (max - min).\n * Ignores domain provided by user and only considers domain from data.\n *\n * The result is a number between 0 and 1.\n */\nexport var selectSmallestDistanceBetweenValues = createSelector(selectAllAppliedValues, selectBaseAxis, (allDataSquished, axisSettings) => {\n  if (!axisSettings || axisSettings.type !== 'number') {\n    return undefined;\n  }\n  var smallestDistanceBetweenValues = Infinity;\n  var sortedValues = Array.from(onlyAllowNumbers(allDataSquished.map(d => d.value))).sort((a, b) => a - b);\n  if (sortedValues.length < 2) {\n    return Infinity;\n  }\n  var diff = sortedValues[sortedValues.length - 1] - sortedValues[0];\n  if (diff === 0) {\n    return Infinity;\n  }\n  // Only do n - 1 distance calculations because there's only n - 1 distances between n values.\n  for (var i = 0; i < sortedValues.length - 1; i++) {\n    var distance = sortedValues[i + 1] - sortedValues[i];\n    smallestDistanceBetweenValues = Math.min(smallestDistanceBetweenValues, distance);\n  }\n  return smallestDistanceBetweenValues / diff;\n});\nvar selectCalculatedPadding = createSelector(selectSmallestDistanceBetweenValues, selectChartLayout, selectBarCategoryGap, selectChartOffsetInternal, (_1, _2, _3, padding) => padding, (smallestDistanceInPercent, layout, barCategoryGap, offset, padding) => {\n  if (!isWellBehavedNumber(smallestDistanceInPercent)) {\n    return 0;\n  }\n  var rangeWidth = layout === 'vertical' ? offset.height : offset.width;\n  if (padding === 'gap') {\n    return smallestDistanceInPercent * rangeWidth / 2;\n  }\n  if (padding === 'no-gap') {\n    var gap = getPercentValue(barCategoryGap, smallestDistanceInPercent * rangeWidth);\n    var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n    return halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n  }\n  return 0;\n});\nexport var selectCalculatedXAxisPadding = (state, axisId) => {\n  var xAxisSettings = selectXAxisSettings(state, axisId);\n  if (xAxisSettings == null || typeof xAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'xAxis', axisId, xAxisSettings.padding);\n};\nexport var selectCalculatedYAxisPadding = (state, axisId) => {\n  var yAxisSettings = selectYAxisSettings(state, axisId);\n  if (yAxisSettings == null || typeof yAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'yAxis', axisId, yAxisSettings.padding);\n};\nvar selectXAxisPadding = createSelector(selectXAxisSettings, selectCalculatedXAxisPadding, (xAxisSettings, calculated) => {\n  var _padding$left, _padding$right;\n  if (xAxisSettings == null) {\n    return {\n      left: 0,\n      right: 0\n    };\n  }\n  var {\n    padding\n  } = xAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      left: calculated,\n      right: calculated\n    };\n  }\n  return {\n    left: ((_padding$left = padding.left) !== null && _padding$left !== void 0 ? _padding$left : 0) + calculated,\n    right: ((_padding$right = padding.right) !== null && _padding$right !== void 0 ? _padding$right : 0) + calculated\n  };\n});\nvar selectYAxisPadding = createSelector(selectYAxisSettings, selectCalculatedYAxisPadding, (yAxisSettings, calculated) => {\n  var _padding$top, _padding$bottom;\n  if (yAxisSettings == null) {\n    return {\n      top: 0,\n      bottom: 0\n    };\n  }\n  var {\n    padding\n  } = yAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      top: calculated,\n      bottom: calculated\n    };\n  }\n  return {\n    top: ((_padding$top = padding.top) !== null && _padding$top !== void 0 ? _padding$top : 0) + calculated,\n    bottom: ((_padding$bottom = padding.bottom) !== null && _padding$bottom !== void 0 ? _padding$bottom : 0) + calculated\n  };\n});\nexport var combineXAxisRange = createSelector([selectChartOffsetInternal, selectXAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, padding, brushDimensions, _ref4, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref4;\n  if (isPanorama) {\n    return [brushPadding.left, brushDimensions.width - brushPadding.right];\n  }\n  return [offset.left + padding.left, offset.left + offset.width - padding.right];\n});\nexport var combineYAxisRange = createSelector([selectChartOffsetInternal, selectChartLayout, selectYAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, layout, padding, brushDimensions, _ref5, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref5;\n  if (isPanorama) {\n    return [brushDimensions.height - brushPadding.bottom, brushPadding.top];\n  }\n  if (layout === 'horizontal') {\n    return [offset.top + offset.height - padding.bottom, offset.top + padding.top];\n  }\n  return [offset.top + padding.top, offset.top + offset.height - padding.bottom];\n});\nexport var selectAxisRange = (state, axisType, axisId, isPanorama) => {\n  var _selectZAxisSettings;\n  switch (axisType) {\n    case 'xAxis':\n      return combineXAxisRange(state, axisId, isPanorama);\n    case 'yAxis':\n      return combineYAxisRange(state, axisId, isPanorama);\n    case 'zAxis':\n      return (_selectZAxisSettings = selectZAxisSettings(state, axisId)) === null || _selectZAxisSettings === void 0 ? void 0 : _selectZAxisSettings.range;\n    case 'angleAxis':\n      return selectAngleAxisRange(state);\n    case 'radiusAxis':\n      return selectRadiusAxisRange(state, axisId);\n    default:\n      return undefined;\n  }\n};\nexport var selectAxisRangeWithReverse = createSelector([selectBaseAxis, selectAxisRange], combineAxisRangeWithReverse);\nexport var selectAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomainIncludingNiceTicks, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectErrorBarsSettings = createSelector([selectCartesianItemsSettings, selectAllErrorBarSettings, pickAxisType], combineRelevantErrorBarSettings);\nfunction compareIds(a, b) {\n  if (a.id < b.id) {\n    return -1;\n  }\n  if (a.id > b.id) {\n    return 1;\n  }\n  return 0;\n}\nvar pickAxisOrientation = (_state, orientation) => orientation;\nvar pickMirror = (_state, _orientation, mirror) => mirror;\nvar selectAllXAxesWithOffsetType = createSelector(selectAllXAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar selectAllYAxesWithOffsetType = createSelector(selectAllYAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar getXAxisSize = (offset, axisSettings) => {\n  return {\n    width: offset.width,\n    height: axisSettings.height\n  };\n};\nvar getYAxisSize = (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n};\nexport var selectXAxisSize = createSelector(selectChartOffsetInternal, selectXAxisSettings, getXAxisSize);\nvar combineXAxisPositionStartingPoint = (offset, orientation, chartHeight) => {\n  switch (orientation) {\n    case 'top':\n      return offset.top;\n    case 'bottom':\n      return chartHeight - offset.bottom;\n    default:\n      return 0;\n  }\n};\nvar combineYAxisPositionStartingPoint = (offset, orientation, chartWidth) => {\n  switch (orientation) {\n    case 'left':\n      return offset.left;\n    case 'right':\n      return chartWidth - offset.right;\n    default:\n      return 0;\n  }\n};\nexport var selectAllXAxesOffsetSteps = createSelector(selectChartHeight, selectChartOffsetInternal, selectAllXAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartHeight, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getXAxisSize(offset, axis);\n    if (position == null) {\n      position = combineXAxisPositionStartingPoint(offset, orientation, chartHeight);\n    }\n    var needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.height;\n    position += (needSpace ? -1 : 1) * axisSize.height;\n  });\n  return steps;\n});\nexport var selectAllYAxesOffsetSteps = createSelector(selectChartWidth, selectChartOffsetInternal, selectAllYAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartWidth, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getYAxisSize(offset, axis);\n    if (position == null) {\n      position = combineYAxisPositionStartingPoint(offset, orientation, chartWidth);\n    }\n    var needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.width;\n    position += (needSpace ? -1 : 1) * axisSize.width;\n  });\n  return steps;\n});\nexport var selectXAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectXAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllXAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: offset.left,\n      y: 0\n    };\n  }\n  return {\n    x: offset.left,\n    y: stepOfThisAxis\n  };\n};\nexport var selectYAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectYAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllYAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: 0,\n      y: offset.top\n    };\n  }\n  return {\n    x: stepOfThisAxis,\n    y: offset.top\n  };\n};\nexport var selectYAxisSize = createSelector(selectChartOffsetInternal, selectYAxisSettings, (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n});\nexport var selectCartesianAxisSize = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSize(state, axisId).width;\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSize(state, axisId).height;\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n};\nexport var combineDuplicateDomain = (chartLayout, appliedValues, axis, axisType) => {\n  if (axis == null) {\n    return undefined;\n  }\n  var {\n    allowDuplicatedCategory,\n    type,\n    dataKey\n  } = axis;\n  var isCategorical = isCategoricalAxis(chartLayout, axisType);\n  var allData = appliedValues.map(av => av.value);\n  if (dataKey && isCategorical && type === 'category' && allowDuplicatedCategory && hasDuplicate(allData)) {\n    return allData;\n  }\n  return undefined;\n};\nexport var selectDuplicateDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectBaseAxis, pickAxisType], combineDuplicateDomain);\nexport var combineCategoricalDomain = (layout, appliedValues, axis, axisType) => {\n  if (axis == null || axis.dataKey == null) {\n    return undefined;\n  }\n  var {\n    type,\n    scale\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && (type === 'number' || scale !== 'auto')) {\n    return appliedValues.map(d => d.value);\n  }\n  return undefined;\n};\nexport var selectCategoricalDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectAxisPropsNeededForCartesianGridTicksGenerator = createSelector([selectChartLayout, selectCartesianAxisSettings, selectRealScaleType, selectAxisScale, selectDuplicateDomain, selectCategoricalDomain, selectAxisRange, selectNiceTicks, pickAxisType], (layout, axis, realScaleType, scale, duplicateDomain, categoricalDomain, axisRange, niceTicks, axisType) => {\n  if (axis == null) {\n    return null;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  return {\n    angle: axis.angle,\n    interval: axis.interval,\n    minTickGap: axis.minTickGap,\n    orientation: axis.orientation,\n    tick: axis.tick,\n    tickCount: axis.tickCount,\n    tickFormatter: axis.tickFormatter,\n    ticks: axis.ticks,\n    type: axis.type,\n    unit: axis.unit,\n    axisType,\n    categoricalDomain,\n    duplicateDomain,\n    isCategorical,\n    niceTicks,\n    range: axisRange,\n    realScaleType,\n    scale\n  };\n});\nexport var combineAxisTicks = (layout, axis, realScaleType, scale, niceTicks, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    type,\n    ticks,\n    tickCount\n  } = axis;\n\n  // This is testing for `scaleBand` but for band axis the type is reported as `band` so this looks like a dead code with a workaround elsewhere?\n  var offsetForBand = realScaleType === 'scaleBand' && typeof scale.bandwidth === 'function' ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && axisRange != null && axisRange.length >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  var ticksOrNiceTicks = ticks || niceTicks;\n  if (ticksOrNiceTicks) {\n    var result = ticksOrNiceTicks.map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        index,\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfAxis = createSelector([selectChartLayout, selectAxisSettings, selectRealScaleType, selectAxisScale, selectNiceTicks, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var combineGraphicalItemTicks = (layout, axis, scale, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null || axisRange == null || axisRange[0] === axisRange[1]) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    tickCount\n  } = axis;\n  var offset = 0;\n  offset = axisType === 'angleAxis' && (axisRange === null || axisRange === void 0 ? void 0 : axisRange.length) >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfGraphicalItem = createSelector([selectChartLayout, selectAxisSettings, selectAxisScale, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineGraphicalItemTicks);\nexport var selectAxisWithScale = createSelector(selectBaseAxis, selectAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\nvar selectZAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomain, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectZAxisWithScale = createSelector((state, _axisType, axisId) => selectZAxisSettings(state, axisId), selectZAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\n\n/**\n * We are also going to need to implement polar chart directions if we want to support keyboard controls for those.\n */\n\nexport var selectChartDirection = createSelector([selectChartLayout, selectAllXAxes, selectAllYAxes], (layout, allXAxes, allYAxes) => {\n  switch (layout) {\n    case 'horizontal':\n      {\n        return allXAxes.some(axis => axis.reversed) ? 'right-to-left' : 'left-to-right';\n      }\n    case 'vertical':\n      {\n        return allYAxes.some(axis => axis.reversed) ? 'bottom-to-top' : 'top-to-bottom';\n      }\n    // TODO: make this better. For now, right arrow triggers \"forward\", left arrow \"back\"\n    // however, the tooltip moves an unintuitive direction because of how the indices are rendered\n    case 'centric':\n    case 'radial':\n      {\n        return 'left-to-right';\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAO,KAAKC,QAAQ,MAAM,yBAAyB;AACnD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,uBAAuB;AACxI,SAASC,0BAA0B,EAAEC,yCAAyC,QAAQ,iBAAiB;AACvG,SAASC,wBAAwB,EAAEC,4CAA4C,EAAEC,wBAAwB,QAAQ,oCAAoC;AACrJ,SAASC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,sBAAsB;AACvH,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,kBAAkB;AAC9E,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,qBAAqB,EAAEC,mBAAmB,QAAQ,kBAAkB;AAC7E,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,sBAAsB;AACnG,SAASC,eAAe,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACrH,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,IAAIC,oBAAoB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG;EACzBC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,IAAI;EACnBC,uBAAuB,EAAE,IAAI;EAC7BC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAEC,SAAS;EAClBC,MAAM,EAAED,SAAS;EACjBE,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,IAAI;EACVC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAET,SAAS;EACfU,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAElB,SAAS;EACxBmB,KAAK,EAAEnB,SAAS;EAChBoB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAErB;AACR,CAAC;AACD,OAAO,IAAIsB,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,KAAK,CAACH,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO/B,aAAa;EACtB;EACA,OAAO+B,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,aAAa,GAAG;EACzBjC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,IAAI;EACnBC,uBAAuB,EAAE,IAAI;EAC7BC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAEC,SAAS;EAClBC,MAAM,EAAER,oBAAoB;EAC5BU,IAAI,EAAE,IAAI;EACVC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAET,SAAS;EACfU,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE;IACPkB,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDhB,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAElB,SAAS;EACxBmB,KAAK,EAAEnB,SAAS;EAChBoB,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAErB,SAAS;EACf+B,KAAK,EAAE3C;AACT,CAAC;AACD,OAAO,IAAI4C,mBAAmB,GAAGA,CAACT,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACO,KAAK,CAACT,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOG,aAAa;EACtB;EACA,OAAOH,IAAI;AACb,CAAC;AACD,OAAO,IAAIS,aAAa,GAAG;EACzBjC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EACnBI,aAAa,EAAE,KAAK;EACpBS,QAAQ,EAAE,KAAK;EACfnB,iBAAiB,EAAE,KAAK;EACxBE,uBAAuB,EAAE,KAAK;EAC9BE,OAAO,EAAEC,SAAS;EAClBI,EAAE,EAAE,CAAC;EACLK,IAAI,EAAE,EAAE;EACR7D,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACfmE,KAAK,EAAE,MAAM;EACbK,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,IAAIc,mBAAmB,GAAGA,CAACZ,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACU,KAAK,CAACZ,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOS,aAAa;EACtB;EACA,OAAOT,IAAI;AACb,CAAC;AACD,OAAO,IAAIY,cAAc,GAAGA,CAACd,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EACvD,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOW,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,WAAW;MACd;QACE,OAAO3C,eAAe,CAAC0C,KAAK,EAAEC,MAAM,CAAC;MACvC;IACF,KAAK,YAAY;MACf;QACE,OAAOzC,gBAAgB,CAACwC,KAAK,EAAEC,MAAM,CAAC;MACxC;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;AACD,IAAIG,2BAA2B,GAAGA,CAAClB,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAC7D,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,kBAAkB,GAAGA,CAACnB,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAC3D,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,WAAW;MACd;QACE,OAAO3C,eAAe,CAAC0C,KAAK,EAAEC,MAAM,CAAC;MACvC;IACF,KAAK,YAAY;MACf;QACE,OAAOzC,gBAAgB,CAACwC,KAAK,EAAEC,MAAM,CAAC;MACxC;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIK,YAAY,GAAGpB,KAAK,IAAIA,KAAK,CAACqB,cAAc,CAACC,cAAc,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3B,IAAI,KAAK,KAAK,CAAC,IAAIG,KAAK,CAACqB,cAAc,CAACI,UAAU,CAACF,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3B,IAAI,KAAK,WAAW,CAAC;;AAEnL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,iBAAiBA,CAACX,QAAQ,EAAEd,MAAM,EAAE;EAClD,OAAOuB,IAAI,IAAI;IACb,QAAQT,QAAQ;MACd,KAAK,OAAO;QACV;QACA,OAAO,SAAS,IAAIS,IAAI,IAAIA,IAAI,CAACG,OAAO,KAAK1B,MAAM;MACrD,KAAK,OAAO;QACV,OAAO,SAAS,IAAIuB,IAAI,IAAIA,IAAI,CAACI,OAAO,KAAK3B,MAAM;MACrD,KAAK,OAAO;QACV,OAAO,SAAS,IAAIuB,IAAI,IAAIA,IAAI,CAACK,OAAO,KAAK5B,MAAM;MACrD,KAAK,WAAW;QACd,OAAO,aAAa,IAAIuB,IAAI,IAAIA,IAAI,CAACM,WAAW,KAAK7B,MAAM;MAC7D,KAAK,YAAY;QACf,OAAO,cAAc,IAAIuB,IAAI,IAAIA,IAAI,CAACO,YAAY,KAAK9B,MAAM;MAC/D;QACE,OAAO,KAAK;IAChB;EACF,CAAC;AACH;AACA,OAAO,IAAI+B,8BAA8B,GAAGhC,KAAK,IAAIA,KAAK,CAACqB,cAAc,CAACC,cAAc;AACxF,IAAIW,mBAAmB,GAAG7G,cAAc,CAAC,CAACsC,YAAY,EAAEC,UAAU,CAAC,EAAE+D,iBAAiB,CAAC;AACvF,OAAO,IAAIQ,6BAA6B,GAAGA,CAACb,cAAc,EAAEc,YAAY,EAAEC,aAAa,KAAKf,cAAc,CAAC1H,MAAM,CAACyI,aAAa,CAAC,CAACzI,MAAM,CAAC6H,IAAI,IAAI;EAC9I,IAAI,CAACW,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACrD,aAAa,MAAM,IAAI,EAAE;IACrG,OAAO,IAAI;EACb;EACA,OAAO,CAAC0C,IAAI,CAAC5C,IAAI;AACnB,CAAC,CAAC;AACF,OAAO,IAAIyD,4BAA4B,GAAGjH,cAAc,CAAC,CAAC4G,8BAA8B,EAAElB,cAAc,EAAEmB,mBAAmB,CAAC,EAAEC,6BAA6B,CAAC;AAC9J,OAAO,IAAII,mCAAmC,GAAGlH,cAAc,CAAC,CAACiH,4BAA4B,CAAC,EAAEf,cAAc,IAAI;EAChH,OAAOA,cAAc,CAAC3H,MAAM,CAAC6H,IAAI,IAAIA,IAAI,CAAC3B,IAAI,KAAK,MAAM,IAAI2B,IAAI,CAAC3B,IAAI,KAAK,KAAK,CAAC,CAAClG,MAAM,CAACsE,SAAS,CAAC;AACrG,CAAC,CAAC;AACF,OAAO,IAAIsE,8BAA8B,GAAGjB,cAAc,IAAIA,cAAc,CAAC3H,MAAM,CAAC6H,IAAI,IAAI,EAAE,SAAS,IAAIA,IAAI,CAAC,IAAIA,IAAI,CAACgB,OAAO,KAAK/D,SAAS,CAAC;AAC/I,IAAIgE,yCAAyC,GAAGrH,cAAc,CAAC,CAACiH,4BAA4B,CAAC,EAAEE,8BAA8B,CAAC;AAC9H,OAAO,IAAIG,yBAAyB,GAAGpB,cAAc,IAAIA,cAAc,CAACqB,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAACoB,IAAI,CAAC,CAACjJ,MAAM,CAACkJ,OAAO,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;;AAEtH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iCAAiC,GAAG3H,cAAc,CAAC,CAACiH,4BAA4B,CAAC,EAAEK,yBAAyB,CAAC;AACxH,OAAO,IAAIM,oBAAoB,GAAGA,CAACC,kBAAkB,EAAEC,IAAI,KAAK;EAC9D,IAAI;IACFC,SAAS,GAAG,EAAE;IACdC,cAAc;IACdC;EACF,CAAC,GAAGH,IAAI;EACR,IAAID,kBAAkB,CAAC/I,MAAM,GAAG,CAAC,EAAE;IACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAO+I,kBAAkB;EAC3B;EACA,OAAOE,SAAS,CAACG,KAAK,CAACF,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,mBAAmB,GAAGnI,cAAc,CAAC,CAAC2H,iCAAiC,EAAEjH,yCAAyC,CAAC,EAAEkH,oBAAoB,CAAC;AACrJ,OAAO,IAAIQ,oBAAoB,GAAGA,CAACZ,IAAI,EAAET,YAAY,EAAEsB,KAAK,KAAK;EAC/D,IAAI,CAACtB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC3D,OAAO,KAAK,IAAI,EAAE;IAC9F,OAAOoE,IAAI,CAACD,GAAG,CAACnB,IAAI,KAAK;MACvB/G,KAAK,EAAEkB,iBAAiB,CAAC6F,IAAI,EAAEW,YAAY,CAAC3D,OAAO;IACrD,CAAC,CAAC,CAAC;EACL;EACA,IAAIiF,KAAK,CAACvJ,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOuJ,KAAK,CAACd,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAAChD,OAAO,CAAC,CAACkF,OAAO,CAAClF,OAAO,IAAIoE,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;MAC3ElJ,KAAK,EAAEkB,iBAAiB,CAACgI,KAAK,EAAEnF,OAAO;IACzC,CAAC,CAAC,CAAC,CAAC;EACN;EACA,OAAOoE,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;IACxBlJ,KAAK,EAAEkJ;EACT,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAGxI,cAAc,CAAC,CAACmI,mBAAmB,EAAEzC,cAAc,EAAEuB,4BAA4B,CAAC,EAAEmB,oBAAoB,CAAC;AAC7I,OAAO,SAASK,6BAA6BA,CAAC9C,QAAQ,EAAE+C,QAAQ,EAAE;EAChE,QAAQ/C,QAAQ;IACd,KAAK,OAAO;MACV,OAAO+C,QAAQ,CAACC,SAAS,KAAK,GAAG;IACnC,KAAK,OAAO;MACV,OAAOD,QAAQ,CAACC,SAAS,KAAK,GAAG;IACnC;MACE,OAAO,KAAK;EAChB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACvJ,KAAK,EAAE;EAC1C,IAAI4B,QAAQ,CAAC5B,KAAK,CAAC,IAAIU,MAAM,CAAC8I,QAAQ,CAACxJ,KAAK,CAAC,EAAE;IAC7C,OAAO,CAACA,KAAK,EAAEA,KAAK,CAAC;EACvB;EACA,IAAIyJ,KAAK,CAACC,OAAO,CAAC1J,KAAK,CAAC,EAAE;IACxB,IAAI2J,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG7J,KAAK,CAAC;IACjC,IAAI8J,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAG/J,KAAK,CAAC;IACjC,IAAI,CAAC2B,KAAK,CAACgI,QAAQ,CAAC,IAAI,CAAChI,KAAK,CAACmI,QAAQ,CAAC,IAAIpJ,MAAM,CAAC8I,QAAQ,CAACG,QAAQ,CAAC,IAAIjJ,MAAM,CAAC8I,QAAQ,CAACM,QAAQ,CAAC,EAAE;MAClG,OAAO,CAACH,QAAQ,EAAEG,QAAQ,CAAC;IAC7B;EACF;EACA,OAAO9F,SAAS;AAClB;AACA,SAASgG,gBAAgBA,CAAC7B,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACjJ,MAAM,CAAC+K,CAAC,IAAIpI,UAAU,CAACoI,CAAC,CAAC,IAAIA,CAAC,YAAYC,IAAI,CAAC,CAAChC,GAAG,CAACxH,MAAM,CAAC,CAACxB,MAAM,CAACiL,CAAC,IAAIxI,KAAK,CAACwI,CAAC,CAAC,KAAK,KAAK,CAAC;AACzG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAAClB,KAAK,EAAEmB,YAAY,EAAEC,iBAAiB,EAAE;EAC9E,IAAI,CAACA,iBAAiB,IAAI,OAAOD,YAAY,KAAK,QAAQ,IAAI1I,KAAK,CAAC0I,YAAY,CAAC,EAAE;IACjF,OAAO,EAAE;EACX;EACA,IAAI,CAACC,iBAAiB,CAAC7K,MAAM,EAAE;IAC7B,OAAO,EAAE;EACX;EACA,OAAOuK,gBAAgB,CAACM,iBAAiB,CAACrB,OAAO,CAACsB,EAAE,IAAI;IACtD,IAAIC,UAAU,GAAGtJ,iBAAiB,CAACgI,KAAK,EAAEqB,EAAE,CAACxG,OAAO,CAAC;IACrD,IAAI0G,QAAQ,EAAEC,SAAS;IACvB,IAAIjB,KAAK,CAACC,OAAO,CAACc,UAAU,CAAC,EAAE;MAC7B,CAACC,QAAQ,EAAEC,SAAS,CAAC,GAAGF,UAAU;IACpC,CAAC,MAAM;MACLC,QAAQ,GAAGC,SAAS,GAAGF,UAAU;IACnC;IACA,IAAI,CAACxI,mBAAmB,CAACyI,QAAQ,CAAC,IAAI,CAACzI,mBAAmB,CAAC0I,SAAS,CAAC,EAAE;MACrE,OAAO1G,SAAS;IAClB;IACA,OAAO,CAACqG,YAAY,GAAGI,QAAQ,EAAEJ,YAAY,GAAGK,SAAS,CAAC;EAC5D,CAAC,CAAC,CAAC;AACL;AACA,OAAO,IAAIC,0BAA0B,GAAGhK,cAAc,CAAC,CAACkH,mCAAmC,EAAExG,yCAAyC,EAAEiC,iBAAiB,CAAC,EAAEC,2BAA2B,CAAC;AACxL,OAAO,IAAIqH,kBAAkB,GAAGA,CAACC,aAAa,EAAE7B,KAAK,EAAE8B,eAAe,KAAK;EACzE,IAAIC,kBAAkB,GAAG,CAAC,CAAC;EAC3B,IAAIC,UAAU,GAAGhC,KAAK,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEnE,IAAI,KAAK;IAC3C,IAAIA,IAAI,CAACgB,OAAO,IAAI,IAAI,EAAE;MACxB,OAAOmD,GAAG;IACZ;IACA,IAAIA,GAAG,CAACnE,IAAI,CAACgB,OAAO,CAAC,IAAI,IAAI,EAAE;MAC7BmD,GAAG,CAACnE,IAAI,CAACgB,OAAO,CAAC,GAAG,EAAE;IACxB;IACAmD,GAAG,CAACnE,IAAI,CAACgB,OAAO,CAAC,CAAC1I,IAAI,CAAC0H,IAAI,CAAC;IAC5B,OAAOmE,GAAG;EACZ,CAAC,EAAEH,kBAAkB,CAAC;EACtB,OAAOjM,MAAM,CAACqM,WAAW,CAACrM,MAAM,CAACsM,OAAO,CAACJ,UAAU,CAAC,CAAC9C,GAAG,CAACmD,KAAK,IAAI;IAChE,IAAI,CAACtD,OAAO,EAAEnB,cAAc,CAAC,GAAGyE,KAAK;IACrC,IAAIC,QAAQ,GAAG1E,cAAc,CAACsB,GAAG,CAAC7E,wBAAwB,CAAC;IAC3D,OAAO,CAAC0E,OAAO,EAAE;MACf;MACAwD,WAAW,EAAEtK,cAAc,CAAC4J,aAAa,EAAES,QAAQ,EAAER,eAAe,CAAC;MACrElE;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI4E,iBAAiB,GAAG7K,cAAc,CAAC,CAACgK,0BAA0B,EAAE9C,mCAAmC,EAAEjF,qBAAqB,CAAC,EAAEgI,kBAAkB,CAAC;AAC3J,OAAO,IAAIa,0BAA0B,GAAGA,CAACC,WAAW,EAAEC,KAAK,EAAErF,QAAQ,KAAK;EACxE,IAAI;IACFqC,cAAc;IACdC;EACF,CAAC,GAAG+C,KAAK;EACT,IAAIrF,QAAQ,KAAK,OAAO,EAAE;IACxB;IACA,OAAOtC,SAAS;EAClB;EACA,IAAI4H,mBAAmB,GAAG5K,sBAAsB,CAAC0K,WAAW,EAAE/C,cAAc,EAAEC,YAAY,CAAC;EAC3F,IAAIgD,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;IAC/F,OAAO5H,SAAS;EAClB;EACA,OAAO4H,mBAAmB;AAC5B,CAAC;AACD,OAAO,IAAIC,yBAAyB,GAAGlL,cAAc,CAAC,CAAC6K,iBAAiB,EAAEpK,0BAA0B,EAAE6B,YAAY,CAAC,EAAEwI,0BAA0B,CAAC;AAChJ,OAAO,IAAIK,iDAAiD,GAAGA,CAAC3D,IAAI,EAAET,YAAY,EAAEsB,KAAK,EAAE+C,SAAS,EAAEzF,QAAQ,KAAK;EACjH,IAAI0C,KAAK,CAACvJ,MAAM,GAAG,CAAC,EAAE;IACpB,OAAO0I,IAAI,CAACc,OAAO,CAACC,KAAK,IAAI;MAC3B,OAAOF,KAAK,CAACC,OAAO,CAAClC,IAAI,IAAI;QAC3B,IAAIiF,kBAAkB,EAAEC,qBAAqB;QAC7C,IAAI3B,iBAAiB,GAAG,CAAC0B,kBAAkB,GAAGD,SAAS,CAAChF,IAAI,CAAC3C,EAAE,CAAC,MAAM,IAAI,IAAI4H,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAC9M,MAAM,CAACmK,QAAQ,IAAID,6BAA6B,CAAC9C,QAAQ,EAAE+C,QAAQ,CAAC,CAAC;QAC/M,IAAI6C,cAAc,GAAGhL,iBAAiB,CAACgI,KAAK,EAAE,CAAC+C,qBAAqB,GAAGvE,YAAY,CAAC3D,OAAO,MAAM,IAAI,IAAIkI,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGlF,IAAI,CAAChD,OAAO,CAAC;QACjL,OAAO;UACL/D,KAAK,EAAEkM,cAAc;UACrBC,WAAW,EAAE/B,uBAAuB,CAAClB,KAAK,EAAEgD,cAAc,EAAE5B,iBAAiB;QAC/E,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAACpL,MAAM,CAACkJ,OAAO,CAAC;EACpB;EACA,IAAI,CAACV,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC3D,OAAO,KAAK,IAAI,EAAE;IAC9F,OAAOoE,IAAI,CAACD,GAAG,CAACnB,IAAI,KAAK;MACvB/G,KAAK,EAAEkB,iBAAiB,CAAC6F,IAAI,EAAEW,YAAY,CAAC3D,OAAO,CAAC;MACpDoI,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL;EACA,OAAOhE,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;IACxBlJ,KAAK,EAAEkJ,KAAK;IACZiD,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAIC,yBAAyB,GAAG7G,KAAK,IAAIA,KAAK,CAACwG,SAAS;AAC/D,IAAIM,+BAA+B,GAAGA,CAACC,sBAAsB,EAAEC,mBAAmB,EAAEjG,QAAQ,KAAK;EAC/F,OAAOgG,sBAAsB,CAACrD,OAAO,CAAClC,IAAI,IAAI;IAC5C,OAAOwF,mBAAmB,CAACxF,IAAI,CAAC3C,EAAE,CAAC;EACrC,CAAC,CAAC,CAAClF,MAAM,CAACkJ,OAAO,CAAC,CAAClJ,MAAM,CAACP,CAAC,IAAI;IAC7B,OAAOyK,6BAA6B,CAAC9C,QAAQ,EAAE3H,CAAC,CAAC;EACnD,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAI6N,oCAAoC,GAAG7L,cAAc,CAAC,CAACqH,yCAAyC,EAAEoE,yBAAyB,EAAEnJ,YAAY,CAAC,EAAEoJ,+BAA+B,CAAC;AACvL,OAAO,IAAII,mDAAmD,GAAG9L,cAAc,CAAC,CAACmI,mBAAmB,EAAEzC,cAAc,EAAE2B,yCAAyC,EAAEoE,yBAAyB,EAAEnJ,YAAY,CAAC,EAAE6I,iDAAiD,CAAC;AAC7P,SAASY,kCAAkCA,CAAC3F,IAAI,EAAE;EAChD,IAAI;IACF/G;EACF,CAAC,GAAG+G,IAAI;EACR,IAAIlF,UAAU,CAAC7B,KAAK,CAAC,IAAIA,KAAK,YAAYkK,IAAI,EAAE;IAC9C,OAAOlK,KAAK;EACd;EACA,OAAOgE,SAAS;AAClB;AACA,IAAI2I,sBAAsB,GAAGC,oBAAoB,IAAI;EACnD,IAAIC,eAAe,GAAGD;EACtB;EAAA,CACC3D,OAAO,CAAC6D,CAAC,IAAI,CAACA,CAAC,CAAC9M,KAAK,EAAE8M,CAAC,CAACX,WAAW,CAAC;EACtC;EAAA,CACC9D,IAAI,CAAC,CAAC,CAAC;EACR,IAAI0E,WAAW,GAAG/C,gBAAgB,CAAC6C,eAAe,CAAC;EACnD,IAAIE,WAAW,CAACtN,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOuE,SAAS;EAClB;EACA,OAAO,CAAC4F,IAAI,CAACC,GAAG,CAAC,GAAGkD,WAAW,CAAC,EAAEnD,IAAI,CAACG,GAAG,CAAC,GAAGgD,WAAW,CAAC,CAAC;AAC7D,CAAC;AACD,IAAIC,2BAA2B,GAAGA,CAACH,eAAe,EAAEnF,YAAY,EAAEuF,aAAa,KAAK;EAClF,IAAIC,iBAAiB,GAAGL,eAAe,CAAC3E,GAAG,CAACwE,kCAAkC,CAAC,CAACxN,MAAM,CAAC+K,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;EACtG,IAAIgD,aAAa,KAAKvF,YAAY,CAAC3D,OAAO,IAAI,IAAI,IAAI2D,YAAY,CAAC7D,uBAAuB,IAAInC,YAAY,CAACwL,iBAAiB,CAAC,CAAC,EAAE;IAC9H;AACJ;AACA;AACA;IACI,OAAOtM,KAAK,CAAC,CAAC,EAAEiM,eAAe,CAACpN,MAAM,CAAC;EACzC;EACA,IAAIiI,YAAY,CAAC7D,uBAAuB,EAAE;IACxC,OAAOqJ,iBAAiB;EAC1B;EACA,OAAOzD,KAAK,CAAC0D,IAAI,CAAC,IAAIC,GAAG,CAACF,iBAAiB,CAAC,CAAC;AAC/C,CAAC;AACD,OAAO,IAAIG,mBAAmB,GAAG3F,YAAY,IAAI;EAC/C,IAAI4F,oBAAoB;EACxB,IAAI5F,YAAY,IAAI,IAAI,IAAI,EAAE,QAAQ,IAAIA,YAAY,CAAC,EAAE;IACvD,OAAOjE,oBAAoB;EAC7B;EACA,IAAIiE,YAAY,CAACzD,MAAM,IAAI,IAAI,EAAE;IAC/B,OAAOyD,YAAY,CAACzD,MAAM;EAC5B;EACA,IAAIyD,YAAY,CAACvC,KAAK,IAAI,IAAI,EAAE;IAC9B,IAAIuC,YAAY,CAACtC,IAAI,KAAK,QAAQ,EAAE;MAClC,IAAImI,SAAS,GAAGvD,gBAAgB,CAACtC,YAAY,CAACvC,KAAK,CAAC;MACpD,OAAO,CAACyE,IAAI,CAACC,GAAG,CAAC,GAAG0D,SAAS,CAAC,EAAE3D,IAAI,CAACG,GAAG,CAAC,GAAGwD,SAAS,CAAC,CAAC;IACzD;IACA,IAAI7F,YAAY,CAACtC,IAAI,KAAK,UAAU,EAAE;MACpC,OAAOsC,YAAY,CAACvC,KAAK,CAAC+C,GAAG,CAACzH,MAAM,CAAC;IACvC;EACF;EACA,OAAO,CAAC6M,oBAAoB,GAAG5F,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACzD,MAAM,MAAM,IAAI,IAAIqJ,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG7J,oBAAoB;AAC3M,CAAC;AACD,OAAO,IAAI+J,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EAChD,KAAK,IAAIC,IAAI,GAAGjO,SAAS,CAACC,MAAM,EAAEiO,OAAO,GAAG,IAAIjE,KAAK,CAACgE,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAC1FD,OAAO,CAACC,IAAI,CAAC,GAAGnO,SAAS,CAACmO,IAAI,CAAC;EACjC;EACA,IAAIC,UAAU,GAAGF,OAAO,CAACxO,MAAM,CAACkJ,OAAO,CAAC;EACxC,IAAIwF,UAAU,CAACnO,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAOuE,SAAS;EAClB;EACA,IAAIuJ,SAAS,GAAGK,UAAU,CAACvF,IAAI,CAAC,CAAC;EACjC,IAAIwB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,GAAG0D,SAAS,CAAC;EAChC,IAAIxD,GAAG,GAAGH,IAAI,CAACG,GAAG,CAAC,GAAGwD,SAAS,CAAC;EAChC,OAAO,CAAC1D,GAAG,EAAEE,GAAG,CAAC;AACnB,CAAC;AACD,OAAO,IAAI8D,mBAAmB,GAAGtI,KAAK,IAAIA,KAAK,CAACuI,iBAAiB,CAACC,IAAI;AACtE,OAAO,IAAIC,uBAAuB,GAAGA,CAACC,QAAQ,EAAE3H,QAAQ,EAAEd,MAAM,KAAK;EACnE,OAAOyI,QAAQ,CAAC/O,MAAM,CAACgP,EAAE,IAAIA,EAAE,CAACC,UAAU,KAAK,cAAc,CAAC,CAACjP,MAAM,CAACgP,EAAE,IAAI;IAC1E,IAAI5H,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAO4H,EAAE,CAAChH,OAAO,KAAK1B,MAAM;IAC9B;IACA,OAAO0I,EAAE,CAAC/G,OAAO,KAAK3B,MAAM;EAC9B,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAI4I,yBAAyB,GAAGzN,cAAc,CAAC,CAACkN,mBAAmB,EAAE5K,YAAY,EAAEC,UAAU,CAAC,EAAE8K,uBAAuB,CAAC;AAC/H,OAAO,IAAIK,oBAAoB,GAAG9I,KAAK,IAAIA,KAAK,CAACuI,iBAAiB,CAACQ,KAAK;AACxE,OAAO,IAAIC,0BAA0B,GAAG5N,cAAc,CAAC,CAAC0N,oBAAoB,EAAEpL,YAAY,EAAEC,UAAU,CAAC,EAAE8K,uBAAuB,CAAC;AACjI,OAAO,IAAIQ,oBAAoB,GAAGjJ,KAAK,IAAIA,KAAK,CAACuI,iBAAiB,CAACW,KAAK;AACxE,OAAO,IAAIC,0BAA0B,GAAG/N,cAAc,CAAC,CAAC6N,oBAAoB,EAAEvL,YAAY,EAAEC,UAAU,CAAC,EAAE8K,uBAAuB,CAAC;AACjI,OAAO,IAAIW,iBAAiB,GAAGA,CAACZ,IAAI,EAAEzH,QAAQ,KAAK;EACjD,IAAIsI,SAAS,GAAG5E,gBAAgB,CAAC+D,IAAI,CAAC7F,GAAG,CAAC2G,GAAG,IAAIvI,QAAQ,KAAK,OAAO,GAAGuI,GAAG,CAACC,CAAC,GAAGD,GAAG,CAACE,CAAC,CAAC,CAAC;EACvF,IAAIH,SAAS,CAACnP,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOuE,SAAS;EAClB;EACA,OAAO,CAAC4F,IAAI,CAACC,GAAG,CAAC,GAAG+E,SAAS,CAAC,EAAEhF,IAAI,CAACG,GAAG,CAAC,GAAG6E,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAII,yBAAyB,GAAGrO,cAAc,CAACyN,yBAAyB,EAAEnL,YAAY,EAAE0L,iBAAiB,CAAC;AAC1G,OAAO,IAAIM,kBAAkB,GAAGA,CAACX,KAAK,EAAEhI,QAAQ,KAAK;EACnD,IAAIsI,SAAS,GAAG5E,gBAAgB,CAACsE,KAAK,CAACrF,OAAO,CAACiG,IAAI,IAAI,CAAC5I,QAAQ,KAAK,OAAO,GAAG4I,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,EAAE,EAAE9I,QAAQ,KAAK,OAAO,GAAG4I,IAAI,CAACG,EAAE,GAAGH,IAAI,CAACI,EAAE,CAAC,CAAC,CAAC;EAC7I,IAAIV,SAAS,CAACnP,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOuE,SAAS;EAClB;EACA,OAAO,CAAC4F,IAAI,CAACC,GAAG,CAAC,GAAG+E,SAAS,CAAC,EAAEhF,IAAI,CAACG,GAAG,CAAC,GAAG6E,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAIW,0BAA0B,GAAG5O,cAAc,CAAC,CAAC4N,0BAA0B,EAAEtL,YAAY,CAAC,EAAEgM,kBAAkB,CAAC;AAC/G,OAAO,IAAIO,kBAAkB,GAAGA,CAACf,KAAK,EAAEnI,QAAQ,KAAK;EACnD,IAAIsI,SAAS,GAAG5E,gBAAgB,CAACyE,KAAK,CAACvG,GAAG,CAACuH,IAAI,IAAInJ,QAAQ,KAAK,OAAO,GAAGmJ,IAAI,CAACX,CAAC,GAAGW,IAAI,CAACV,CAAC,CAAC,CAAC;EAC3F,IAAIH,SAAS,CAACnP,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOuE,SAAS;EAClB;EACA,OAAO,CAAC4F,IAAI,CAACC,GAAG,CAAC,GAAG+E,SAAS,CAAC,EAAEhF,IAAI,CAACG,GAAG,CAAC,GAAG6E,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAIc,0BAA0B,GAAG/O,cAAc,CAAC+N,0BAA0B,EAAEzL,YAAY,EAAEuM,kBAAkB,CAAC;AAC7G,IAAIG,6BAA6B,GAAGhP,cAAc,CAACqO,yBAAyB,EAAEU,0BAA0B,EAAEH,0BAA0B,EAAE,CAACK,UAAU,EAAEC,WAAW,EAAEC,WAAW,KAAK;EAC9K,OAAOtC,YAAY,CAACoC,UAAU,EAAEE,WAAW,EAAED,WAAW,CAAC;AAC3D,CAAC,CAAC;AACF,OAAO,IAAIE,sBAAsB,GAAGpP,cAAc,CAAC,CAAC0F,cAAc,CAAC,EAAEgH,mBAAmB,CAAC;AACzF,OAAO,IAAI2C,sBAAsB,GAAGA,CAACtI,YAAY,EAAEuI,gBAAgB,EAAErE,mBAAmB,EAAEsE,uBAAuB,EAAEC,uBAAuB,EAAEC,MAAM,EAAE9J,QAAQ,KAAK;EAC/J,IAAI+J,wBAAwB,GAAG9O,4CAA4C,CAAC0O,gBAAgB,EAAEvI,YAAY,CAAC/D,iBAAiB,CAAC;EAC7H,IAAI0M,wBAAwB,IAAI,IAAI,EAAE;IACpC;IACA,OAAOA,wBAAwB;EACjC;EACA,IAAIC,gCAAgC,GAAGF,MAAM,KAAK,UAAU,IAAI9J,QAAQ,KAAK,OAAO,IAAI8J,MAAM,KAAK,YAAY,IAAI9J,QAAQ,KAAK,OAAO;EACvI,IAAIiK,aAAa,GAAGD,gCAAgC,GAAG9C,YAAY,CAAC5B,mBAAmB,EAAEuE,uBAAuB,EAAExD,sBAAsB,CAACuD,uBAAuB,CAAC,CAAC,GAAG1C,YAAY,CAAC2C,uBAAuB,EAAExD,sBAAsB,CAACuD,uBAAuB,CAAC,CAAC;EAC3P,OAAO1O,wBAAwB,CAACyO,gBAAgB,EAAEM,aAAa,EAAE7I,YAAY,CAAC/D,iBAAiB,CAAC;AAClG,CAAC;AACD,OAAO,IAAI6M,qBAAqB,GAAG7P,cAAc,CAAC,CAAC0F,cAAc,EAAE0J,sBAAsB,EAAElE,yBAAyB,EAAEY,mDAAmD,EAAEkD,6BAA6B,EAAE7O,iBAAiB,EAAEmC,YAAY,CAAC,EAAE+M,sBAAsB,CAAC;;AAEnQ;AACA;AACA;AACA;AACA;AACA,IAAIS,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,OAAO,IAAIC,iBAAiB,GAAGA,CAAChJ,YAAY,EAAE0I,MAAM,EAAEvF,aAAa,EAAE8F,gBAAgB,EAAE7F,eAAe,EAAExE,QAAQ,EAAEsK,eAAe,KAAK;EACpI,IAAI,CAAClJ,YAAY,IAAI,IAAI,IAAImD,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACpL,MAAM,KAAK,CAAC,KAAKmR,eAAe,KAAK5M,SAAS,EAAE;IAClH,OAAOA,SAAS;EAClB;EACA,IAAI;IACFD,OAAO;IACPqB;EACF,CAAC,GAAGsC,YAAY;EAChB,IAAIuF,aAAa,GAAG9L,iBAAiB,CAACiP,MAAM,EAAE9J,QAAQ,CAAC;EACvD,IAAI2G,aAAa,IAAIlJ,OAAO,IAAI,IAAI,EAAE;IACpC,OAAOnD,KAAK,CAAC,CAAC,EAAEiK,aAAa,CAACpL,MAAM,CAAC;EACvC;EACA,IAAI2F,IAAI,KAAK,UAAU,EAAE;IACvB,OAAO4H,2BAA2B,CAAC2D,gBAAgB,EAAEjJ,YAAY,EAAEuF,aAAa,CAAC;EACnF;EACA,IAAInC,eAAe,KAAK,QAAQ,EAAE;IAChC,OAAO2F,YAAY;EACrB;EACA,OAAOG,eAAe;AACxB,CAAC;AACD,OAAO,IAAIC,gBAAgB,GAAGlQ,cAAc,CAAC,CAAC0F,cAAc,EAAEvF,iBAAiB,EAAEgI,mBAAmB,EAAEK,sBAAsB,EAAEvG,qBAAqB,EAAEK,YAAY,EAAEuN,qBAAqB,CAAC,EAAEE,iBAAiB,CAAC;AAC7M,OAAO,IAAII,oBAAoB,GAAGA,CAACC,UAAU,EAAEX,MAAM,EAAEY,MAAM,EAAEC,SAAS,EAAE3K,QAAQ,KAAK;EACrF,IAAIyK,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO/M,SAAS;EAClB;EACA,IAAI;IACFe,KAAK;IACLK;EACF,CAAC,GAAG2L,UAAU;EACd,IAAIhM,KAAK,KAAK,MAAM,EAAE;IACpB,IAAIqL,MAAM,KAAK,QAAQ,IAAI9J,QAAQ,KAAK,YAAY,EAAE;MACpD,OAAO,MAAM;IACf;IACA,IAAI8J,MAAM,KAAK,QAAQ,IAAI9J,QAAQ,KAAK,WAAW,EAAE;MACnD,OAAO,QAAQ;IACjB;IACA,IAAIlB,IAAI,KAAK,UAAU,IAAI6L,SAAS,KAAKA,SAAS,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAID,SAAS,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAID,SAAS,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAACF,MAAM,CAAC,EAAE;MAC1K,OAAO,OAAO;IAChB;IACA,IAAI5L,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO,MAAM;IACf;IACA,OAAO,QAAQ;EACjB;EACA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIN,IAAI,GAAG,OAAO,CAAC+B,MAAM,CAACzE,UAAU,CAACgD,KAAK,CAAC,CAAC;IAC5C,OAAON,IAAI,IAAI5D,QAAQ,GAAG4D,IAAI,GAAG,OAAO;EAC1C;EACA,OAAOT,SAAS;AAClB,CAAC;AACD,OAAO,IAAImN,mBAAmB,GAAGxQ,cAAc,CAAC,CAAC0F,cAAc,EAAEvF,iBAAiB,EAAE6F,YAAY,EAAEhE,eAAe,EAAEM,YAAY,CAAC,EAAE6N,oBAAoB,CAAC;AACvJ,SAASM,kBAAkBA,CAACC,aAAa,EAAE;EACzC,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOrN,SAAS;EAClB;EACA,IAAIqN,aAAa,IAAIxQ,QAAQ,EAAE;IAC7B;IACA,OAAOA,QAAQ,CAACwQ,aAAa,CAAC,CAAC,CAAC;EAClC;EACA,IAAI5M,IAAI,GAAG,OAAO,CAAC+B,MAAM,CAACzE,UAAU,CAACsP,aAAa,CAAC,CAAC;EACpD,IAAI5M,IAAI,IAAI5D,QAAQ,EAAE;IACpB;IACA,OAAOA,QAAQ,CAAC4D,IAAI,CAAC,CAAC,CAAC;EACzB;EACA,OAAOT,SAAS;AAClB;AACA,OAAO,SAASsN,oBAAoBA,CAAC7L,IAAI,EAAE4L,aAAa,EAAEE,UAAU,EAAEC,SAAS,EAAE;EAC/E,IAAID,UAAU,IAAI,IAAI,IAAIC,SAAS,IAAI,IAAI,EAAE;IAC3C,OAAOxN,SAAS;EAClB;EACA,IAAI,OAAOyB,IAAI,CAACV,KAAK,KAAK,UAAU,EAAE;IACpC;IACA,OAAOU,IAAI,CAACV,KAAK,CAAC0M,IAAI,CAAC,CAAC,CAACxN,MAAM,CAACsN,UAAU,CAAC,CAAC3Q,KAAK,CAAC4Q,SAAS,CAAC;EAC9D;EACA,IAAIE,eAAe,GAAGN,kBAAkB,CAACC,aAAa,CAAC;EACvD,IAAIK,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAO1N,SAAS;EAClB;EACA,IAAIe,KAAK,GAAG2M,eAAe,CAACzN,MAAM,CAACsN,UAAU,CAAC,CAAC3Q,KAAK,CAAC4Q,SAAS,CAAC;EAC/D;EACAzQ,kBAAkB,CAACgE,KAAK,CAAC;EACzB,OAAOA,KAAK;AACd;AACA,OAAO,IAAI4M,gBAAgB,GAAGA,CAACJ,UAAU,EAAE7J,YAAY,EAAE2J,aAAa,KAAK;EACzE,IAAIpB,gBAAgB,GAAG5C,mBAAmB,CAAC3F,YAAY,CAAC;EACxD,IAAI2J,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC1D,OAAOrN,SAAS;EAClB;EACA,IAAI0D,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACzC,SAAS,IAAIwE,KAAK,CAACC,OAAO,CAACuG,gBAAgB,CAAC,KAAKA,gBAAgB,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIA,gBAAgB,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI3O,wBAAwB,CAACiQ,UAAU,CAAC,EAAE;IACnM,OAAOtP,iBAAiB,CAACsP,UAAU,EAAE7J,YAAY,CAACzC,SAAS,EAAEyC,YAAY,CAAC9D,aAAa,CAAC;EAC1F;EACA,IAAI8D,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACzC,SAAS,IAAIyC,YAAY,CAACtC,IAAI,KAAK,QAAQ,IAAI9D,wBAAwB,CAACiQ,UAAU,CAAC,EAAE;IAC5H,OAAOrP,wBAAwB,CAACqP,UAAU,EAAE7J,YAAY,CAACzC,SAAS,EAAEyC,YAAY,CAAC9D,aAAa,CAAC;EACjG;EACA,OAAOI,SAAS;AAClB,CAAC;AACD,OAAO,IAAI4N,eAAe,GAAGjR,cAAc,CAAC,CAACkQ,gBAAgB,EAAEnK,kBAAkB,EAAEyK,mBAAmB,CAAC,EAAEQ,gBAAgB,CAAC;AAC1H,OAAO,IAAIE,8BAA8B,GAAGA,CAACnK,YAAY,EAAEzD,MAAM,EAAE6N,SAAS,EAAExL,QAAQ,KAAK;EACzF;EACA;AACF;AACA;AACA;AACA;AACA;EACEA,QAAQ,KAAK,WAAW,IAAI,CAACoB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACtC,IAAI,MAAM,QAAQ,IAAI9D,wBAAwB,CAAC2C,MAAM,CAAC,IAAIwF,KAAK,CAACC,OAAO,CAACoI,SAAS,CAAC,IAAIA,SAAS,CAACrS,MAAM,GAAG,CAAC,EAAE;IAChN,IAAIsS,aAAa,GAAG9N,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAI+N,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC/B,IAAIG,aAAa,GAAGhO,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAIiO,YAAY,GAAGJ,SAAS,CAACA,SAAS,CAACrS,MAAM,GAAG,CAAC,CAAC;IAClD,OAAO,CAACmK,IAAI,CAACC,GAAG,CAACkI,aAAa,EAAEC,YAAY,CAAC,EAAEpI,IAAI,CAACG,GAAG,CAACkI,aAAa,EAAEC,YAAY,CAAC,CAAC;EACvF;EACA,OAAOjO,MAAM;AACf,CAAC;AACD,OAAO,IAAIkO,kCAAkC,GAAGxR,cAAc,CAAC,CAAC0F,cAAc,EAAEwK,gBAAgB,EAAEe,eAAe,EAAE3O,YAAY,CAAC,EAAE4O,8BAA8B,CAAC;;AAEjK;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,mCAAmC,GAAGzR,cAAc,CAACwI,sBAAsB,EAAE9C,cAAc,EAAE,CAACwG,eAAe,EAAEnF,YAAY,KAAK;EACzI,IAAI,CAACA,YAAY,IAAIA,YAAY,CAACtC,IAAI,KAAK,QAAQ,EAAE;IACnD,OAAOpB,SAAS;EAClB;EACA,IAAIqO,6BAA6B,GAAGC,QAAQ;EAC5C,IAAIC,YAAY,GAAG9I,KAAK,CAAC0D,IAAI,CAACnD,gBAAgB,CAAC6C,eAAe,CAAC3E,GAAG,CAAC4E,CAAC,IAAIA,CAAC,CAAC9M,KAAK,CAAC,CAAC,CAAC,CAACwS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACxG,IAAIH,YAAY,CAAC9S,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAO6S,QAAQ;EACjB;EACA,IAAIK,IAAI,GAAGJ,YAAY,CAACA,YAAY,CAAC9S,MAAM,GAAG,CAAC,CAAC,GAAG8S,YAAY,CAAC,CAAC,CAAC;EAClE,IAAII,IAAI,KAAK,CAAC,EAAE;IACd,OAAOL,QAAQ;EACjB;EACA;EACA,KAAK,IAAInS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoS,YAAY,CAAC9S,MAAM,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;IAChD,IAAIyS,QAAQ,GAAGL,YAAY,CAACpS,CAAC,GAAG,CAAC,CAAC,GAAGoS,YAAY,CAACpS,CAAC,CAAC;IACpDkS,6BAA6B,GAAGzI,IAAI,CAACC,GAAG,CAACwI,6BAA6B,EAAEO,QAAQ,CAAC;EACnF;EACA,OAAOP,6BAA6B,GAAGM,IAAI;AAC7C,CAAC,CAAC;AACF,IAAIE,uBAAuB,GAAGlS,cAAc,CAACyR,mCAAmC,EAAEtR,iBAAiB,EAAE4B,oBAAoB,EAAEH,yBAAyB,EAAE,CAACuQ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAErO,OAAO,KAAKA,OAAO,EAAE,CAACsO,yBAAyB,EAAE7C,MAAM,EAAE8C,cAAc,EAAEC,MAAM,EAAExO,OAAO,KAAK;EAC9P,IAAI,CAAC3C,mBAAmB,CAACiR,yBAAyB,CAAC,EAAE;IACnD,OAAO,CAAC;EACV;EACA,IAAIG,UAAU,GAAGhD,MAAM,KAAK,UAAU,GAAG+C,MAAM,CAACjP,MAAM,GAAGiP,MAAM,CAACpN,KAAK;EACrE,IAAIpB,OAAO,KAAK,KAAK,EAAE;IACrB,OAAOsO,yBAAyB,GAAGG,UAAU,GAAG,CAAC;EACnD;EACA,IAAIzO,OAAO,KAAK,QAAQ,EAAE;IACxB,IAAI0O,GAAG,GAAG5R,eAAe,CAACyR,cAAc,EAAED,yBAAyB,GAAGG,UAAU,CAAC;IACjF,IAAIE,QAAQ,GAAGL,yBAAyB,GAAGG,UAAU,GAAG,CAAC;IACzD,OAAOE,QAAQ,GAAGD,GAAG,GAAG,CAACC,QAAQ,GAAGD,GAAG,IAAID,UAAU,GAAGC,GAAG;EAC7D;EACA,OAAO,CAAC;AACV,CAAC,CAAC;AACF,OAAO,IAAIE,4BAA4B,GAAGA,CAAChO,KAAK,EAAEC,MAAM,KAAK;EAC3D,IAAIgO,aAAa,GAAGlO,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;EACtD,IAAIgO,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,CAAC7O,OAAO,KAAK,QAAQ,EAAE;IACtE,OAAO,CAAC;EACV;EACA,OAAOkO,uBAAuB,CAACtN,KAAK,EAAE,OAAO,EAAEC,MAAM,EAAEgO,aAAa,CAAC7O,OAAO,CAAC;AAC/E,CAAC;AACD,OAAO,IAAI8O,4BAA4B,GAAGA,CAAClO,KAAK,EAAEC,MAAM,KAAK;EAC3D,IAAIkO,aAAa,GAAG1N,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;EACtD,IAAIkO,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,CAAC/O,OAAO,KAAK,QAAQ,EAAE;IACtE,OAAO,CAAC;EACV;EACA,OAAOkO,uBAAuB,CAACtN,KAAK,EAAE,OAAO,EAAEC,MAAM,EAAEkO,aAAa,CAAC/O,OAAO,CAAC;AAC/E,CAAC;AACD,IAAIgP,kBAAkB,GAAGhT,cAAc,CAAC2E,mBAAmB,EAAEiO,4BAA4B,EAAE,CAACC,aAAa,EAAEI,UAAU,KAAK;EACxH,IAAIC,aAAa,EAAEC,cAAc;EACjC,IAAIN,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO;MACL5O,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAI;IACFF;EACF,CAAC,GAAG6O,aAAa;EACjB,IAAI,OAAO7O,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLC,IAAI,EAAEgP,UAAU;MAChB/O,KAAK,EAAE+O;IACT,CAAC;EACH;EACA,OAAO;IACLhP,IAAI,EAAE,CAAC,CAACiP,aAAa,GAAGlP,OAAO,CAACC,IAAI,MAAM,IAAI,IAAIiP,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,IAAID,UAAU;IAC5G/O,KAAK,EAAE,CAAC,CAACiP,cAAc,GAAGnP,OAAO,CAACE,KAAK,MAAM,IAAI,IAAIiP,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,CAAC,IAAIF;EACzG,CAAC;AACH,CAAC,CAAC;AACF,IAAIG,kBAAkB,GAAGpT,cAAc,CAACqF,mBAAmB,EAAEyN,4BAA4B,EAAE,CAACC,aAAa,EAAEE,UAAU,KAAK;EACxH,IAAII,YAAY,EAAEC,eAAe;EACjC,IAAIP,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO;MACL7N,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAI;IACFnB;EACF,CAAC,GAAG+O,aAAa;EACjB,IAAI,OAAO/O,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLkB,GAAG,EAAE+N,UAAU;MACf9N,MAAM,EAAE8N;IACV,CAAC;EACH;EACA,OAAO;IACL/N,GAAG,EAAE,CAAC,CAACmO,YAAY,GAAGrP,OAAO,CAACkB,GAAG,MAAM,IAAI,IAAImO,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC,IAAIJ,UAAU;IACvG9N,MAAM,EAAE,CAAC,CAACmO,eAAe,GAAGtP,OAAO,CAACmB,MAAM,MAAM,IAAI,IAAImO,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,IAAIL;EAC9G,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIM,iBAAiB,GAAGvT,cAAc,CAAC,CAAC4B,yBAAyB,EAAEoR,kBAAkB,EAAEnR,qBAAqB,EAAEC,mBAAmB,EAAE,CAAC0R,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAKA,UAAU,CAAC,EAAE,CAAClB,MAAM,EAAExO,OAAO,EAAE2P,eAAe,EAAEC,KAAK,EAAEF,UAAU,KAAK;EAC/O,IAAI;IACF1P,OAAO,EAAE6P;EACX,CAAC,GAAGD,KAAK;EACT,IAAIF,UAAU,EAAE;IACd,OAAO,CAACG,YAAY,CAAC5P,IAAI,EAAE0P,eAAe,CAACvO,KAAK,GAAGyO,YAAY,CAAC3P,KAAK,CAAC;EACxE;EACA,OAAO,CAACsO,MAAM,CAACvO,IAAI,GAAGD,OAAO,CAACC,IAAI,EAAEuO,MAAM,CAACvO,IAAI,GAAGuO,MAAM,CAACpN,KAAK,GAAGpB,OAAO,CAACE,KAAK,CAAC;AACjF,CAAC,CAAC;AACF,OAAO,IAAI4P,iBAAiB,GAAG9T,cAAc,CAAC,CAAC4B,yBAAyB,EAAEzB,iBAAiB,EAAEiT,kBAAkB,EAAEvR,qBAAqB,EAAEC,mBAAmB,EAAE,CAAC0R,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAKA,UAAU,CAAC,EAAE,CAAClB,MAAM,EAAE/C,MAAM,EAAEzL,OAAO,EAAE2P,eAAe,EAAEI,KAAK,EAAEL,UAAU,KAAK;EAC1Q,IAAI;IACF1P,OAAO,EAAE6P;EACX,CAAC,GAAGE,KAAK;EACT,IAAIL,UAAU,EAAE;IACd,OAAO,CAACC,eAAe,CAACpQ,MAAM,GAAGsQ,YAAY,CAAC1O,MAAM,EAAE0O,YAAY,CAAC3O,GAAG,CAAC;EACzE;EACA,IAAIuK,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO,CAAC+C,MAAM,CAACtN,GAAG,GAAGsN,MAAM,CAACjP,MAAM,GAAGS,OAAO,CAACmB,MAAM,EAAEqN,MAAM,CAACtN,GAAG,GAAGlB,OAAO,CAACkB,GAAG,CAAC;EAChF;EACA,OAAO,CAACsN,MAAM,CAACtN,GAAG,GAAGlB,OAAO,CAACkB,GAAG,EAAEsN,MAAM,CAACtN,GAAG,GAAGsN,MAAM,CAACjP,MAAM,GAAGS,OAAO,CAACmB,MAAM,CAAC;AAChF,CAAC,CAAC;AACF,OAAO,IAAI6O,eAAe,GAAGA,CAACpP,KAAK,EAAEe,QAAQ,EAAEd,MAAM,EAAE6O,UAAU,KAAK;EACpE,IAAIO,oBAAoB;EACxB,QAAQtO,QAAQ;IACd,KAAK,OAAO;MACV,OAAO4N,iBAAiB,CAAC3O,KAAK,EAAEC,MAAM,EAAE6O,UAAU,CAAC;IACrD,KAAK,OAAO;MACV,OAAOI,iBAAiB,CAAClP,KAAK,EAAEC,MAAM,EAAE6O,UAAU,CAAC;IACrD,KAAK,OAAO;MACV,OAAO,CAACO,oBAAoB,GAAGzO,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC,MAAM,IAAI,IAAIoP,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAChU,KAAK;IACtJ,KAAK,WAAW;MACd,OAAOkC,oBAAoB,CAACyC,KAAK,CAAC;IACpC,KAAK,YAAY;MACf,OAAOvC,qBAAqB,CAACuC,KAAK,EAAEC,MAAM,CAAC;IAC7C;MACE,OAAOxB,SAAS;EACpB;AACF,CAAC;AACD,OAAO,IAAI6Q,0BAA0B,GAAGlU,cAAc,CAAC,CAAC0F,cAAc,EAAEsO,eAAe,CAAC,EAAExR,2BAA2B,CAAC;AACtH,OAAO,IAAI2R,eAAe,GAAGnU,cAAc,CAAC,CAAC0F,cAAc,EAAE8K,mBAAmB,EAAEgB,kCAAkC,EAAE0C,0BAA0B,CAAC,EAAEvD,oBAAoB,CAAC;AACxK,OAAO,IAAIyD,uBAAuB,GAAGpU,cAAc,CAAC,CAACiH,4BAA4B,EAAEwE,yBAAyB,EAAEnJ,YAAY,CAAC,EAAEoJ,+BAA+B,CAAC;AAC7J,SAAS2I,UAAUA,CAACvC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAID,CAAC,CAACrO,EAAE,GAAGsO,CAAC,CAACtO,EAAE,EAAE;IACf,OAAO,CAAC,CAAC;EACX;EACA,IAAIqO,CAAC,CAACrO,EAAE,GAAGsO,CAAC,CAACtO,EAAE,EAAE;IACf,OAAO,CAAC;EACV;EACA,OAAO,CAAC;AACV;AACA,IAAI6Q,mBAAmB,GAAGA,CAACd,MAAM,EAAEzP,WAAW,KAAKA,WAAW;AAC9D,IAAIwQ,UAAU,GAAGA,CAACf,MAAM,EAAEgB,YAAY,EAAE3Q,MAAM,KAAKA,MAAM;AACzD,IAAI4Q,4BAA4B,GAAGzU,cAAc,CAAC0B,cAAc,EAAE4S,mBAAmB,EAAEC,UAAU,EAAE,CAACG,OAAO,EAAE3Q,WAAW,EAAEF,MAAM,KAAK6Q,OAAO,CAACnW,MAAM,CAACuG,IAAI,IAAIA,IAAI,CAACf,WAAW,KAAKA,WAAW,CAAC,CAACxF,MAAM,CAACuG,IAAI,IAAIA,IAAI,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACgO,IAAI,CAACwC,UAAU,CAAC,CAAC;AACtP,IAAIM,4BAA4B,GAAG3U,cAAc,CAAC2B,cAAc,EAAE2S,mBAAmB,EAAEC,UAAU,EAAE,CAACG,OAAO,EAAE3Q,WAAW,EAAEF,MAAM,KAAK6Q,OAAO,CAACnW,MAAM,CAACuG,IAAI,IAAIA,IAAI,CAACf,WAAW,KAAKA,WAAW,CAAC,CAACxF,MAAM,CAACuG,IAAI,IAAIA,IAAI,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACgO,IAAI,CAACwC,UAAU,CAAC,CAAC;AACtP,IAAIO,YAAY,GAAGA,CAACpC,MAAM,EAAEzL,YAAY,KAAK;EAC3C,OAAO;IACL3B,KAAK,EAAEoN,MAAM,CAACpN,KAAK;IACnB7B,MAAM,EAAEwD,YAAY,CAACxD;EACvB,CAAC;AACH,CAAC;AACD,IAAIsR,YAAY,GAAGA,CAACrC,MAAM,EAAEzL,YAAY,KAAK;EAC3C,IAAI3B,KAAK,GAAG,OAAO2B,YAAY,CAAC3B,KAAK,KAAK,QAAQ,GAAG2B,YAAY,CAAC3B,KAAK,GAAG3C,oBAAoB;EAC9F,OAAO;IACL2C,KAAK;IACL7B,MAAM,EAAEiP,MAAM,CAACjP;EACjB,CAAC;AACH,CAAC;AACD,OAAO,IAAIuR,eAAe,GAAG9U,cAAc,CAAC4B,yBAAyB,EAAE+C,mBAAmB,EAAEiQ,YAAY,CAAC;AACzG,IAAIG,iCAAiC,GAAGA,CAACvC,MAAM,EAAEzO,WAAW,EAAEiR,WAAW,KAAK;EAC5E,QAAQjR,WAAW;IACjB,KAAK,KAAK;MACR,OAAOyO,MAAM,CAACtN,GAAG;IACnB,KAAK,QAAQ;MACX,OAAO8P,WAAW,GAAGxC,MAAM,CAACrN,MAAM;IACpC;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,IAAI8P,iCAAiC,GAAGA,CAACzC,MAAM,EAAEzO,WAAW,EAAEmR,UAAU,KAAK;EAC3E,QAAQnR,WAAW;IACjB,KAAK,MAAM;MACT,OAAOyO,MAAM,CAACvO,IAAI;IACpB,KAAK,OAAO;MACV,OAAOiR,UAAU,GAAG1C,MAAM,CAACtO,KAAK;IAClC;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,OAAO,IAAIiR,yBAAyB,GAAGnV,cAAc,CAACwB,iBAAiB,EAAEI,yBAAyB,EAAE6S,4BAA4B,EAAEH,mBAAmB,EAAEC,UAAU,EAAE,CAACS,WAAW,EAAExC,MAAM,EAAE4C,yBAAyB,EAAErR,WAAW,EAAEF,MAAM,KAAK;EAC1O,IAAIwR,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,QAAQ;EACZF,yBAAyB,CAACrW,OAAO,CAAC+F,IAAI,IAAI;IACxC,IAAIyQ,QAAQ,GAAGX,YAAY,CAACpC,MAAM,EAAE1N,IAAI,CAAC;IACzC,IAAIwQ,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAGP,iCAAiC,CAACvC,MAAM,EAAEzO,WAAW,EAAEiR,WAAW,CAAC;IAChF;IACA,IAAIQ,SAAS,GAAGzR,WAAW,KAAK,KAAK,IAAI,CAACF,MAAM,IAAIE,WAAW,KAAK,QAAQ,IAAIF,MAAM;IACtFwR,KAAK,CAACvQ,IAAI,CAACrB,EAAE,CAAC,GAAG6R,QAAQ,GAAGvV,MAAM,CAACyV,SAAS,CAAC,GAAGD,QAAQ,CAAChS,MAAM;IAC/D+R,QAAQ,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAID,QAAQ,CAAChS,MAAM;EACpD,CAAC,CAAC;EACF,OAAO8R,KAAK;AACd,CAAC,CAAC;AACF,OAAO,IAAII,yBAAyB,GAAGzV,cAAc,CAACyB,gBAAgB,EAAEG,yBAAyB,EAAE+S,4BAA4B,EAAEL,mBAAmB,EAAEC,UAAU,EAAE,CAACW,UAAU,EAAE1C,MAAM,EAAE4C,yBAAyB,EAAErR,WAAW,EAAEF,MAAM,KAAK;EACxO,IAAIwR,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,QAAQ;EACZF,yBAAyB,CAACrW,OAAO,CAAC+F,IAAI,IAAI;IACxC,IAAIyQ,QAAQ,GAAGV,YAAY,CAACrC,MAAM,EAAE1N,IAAI,CAAC;IACzC,IAAIwQ,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAGL,iCAAiC,CAACzC,MAAM,EAAEzO,WAAW,EAAEmR,UAAU,CAAC;IAC/E;IACA,IAAIM,SAAS,GAAGzR,WAAW,KAAK,MAAM,IAAI,CAACF,MAAM,IAAIE,WAAW,KAAK,OAAO,IAAIF,MAAM;IACtFwR,KAAK,CAACvQ,IAAI,CAACrB,EAAE,CAAC,GAAG6R,QAAQ,GAAGvV,MAAM,CAACyV,SAAS,CAAC,GAAGD,QAAQ,CAACnQ,KAAK;IAC9DkQ,QAAQ,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAID,QAAQ,CAACnQ,KAAK;EACnD,CAAC,CAAC;EACF,OAAOiQ,KAAK;AACd,CAAC,CAAC;AACF,OAAO,IAAIK,mBAAmB,GAAGA,CAAC9Q,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAI2N,MAAM,GAAG5Q,yBAAyB,CAACgD,KAAK,CAAC;EAC7C,IAAImC,YAAY,GAAGpC,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;EACrD,IAAIkC,YAAY,IAAI,IAAI,EAAE;IACxB,OAAO1D,SAAS;EAClB;EACA,IAAIsS,QAAQ,GAAGR,yBAAyB,CAACvQ,KAAK,EAAEmC,YAAY,CAAChD,WAAW,EAAEgD,YAAY,CAAClD,MAAM,CAAC;EAC9F,IAAI+R,cAAc,GAAGD,QAAQ,CAAC9Q,MAAM,CAAC;EACrC,IAAI+Q,cAAc,IAAI,IAAI,EAAE;IAC1B,OAAO;MACLzH,CAAC,EAAEqE,MAAM,CAACvO,IAAI;MACdmK,CAAC,EAAE;IACL,CAAC;EACH;EACA,OAAO;IACLD,CAAC,EAAEqE,MAAM,CAACvO,IAAI;IACdmK,CAAC,EAAEwH;EACL,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGA,CAACjR,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAI2N,MAAM,GAAG5Q,yBAAyB,CAACgD,KAAK,CAAC;EAC7C,IAAImC,YAAY,GAAG1B,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;EACrD,IAAIkC,YAAY,IAAI,IAAI,EAAE;IACxB,OAAO1D,SAAS;EAClB;EACA,IAAIsS,QAAQ,GAAGF,yBAAyB,CAAC7Q,KAAK,EAAEmC,YAAY,CAAChD,WAAW,EAAEgD,YAAY,CAAClD,MAAM,CAAC;EAC9F,IAAI+R,cAAc,GAAGD,QAAQ,CAAC9Q,MAAM,CAAC;EACrC,IAAI+Q,cAAc,IAAI,IAAI,EAAE;IAC1B,OAAO;MACLzH,CAAC,EAAE,CAAC;MACJC,CAAC,EAAEoE,MAAM,CAACtN;IACZ,CAAC;EACH;EACA,OAAO;IACLiJ,CAAC,EAAEyH,cAAc;IACjBxH,CAAC,EAAEoE,MAAM,CAACtN;EACZ,CAAC;AACH,CAAC;AACD,OAAO,IAAI4Q,eAAe,GAAG9V,cAAc,CAAC4B,yBAAyB,EAAEyD,mBAAmB,EAAE,CAACmN,MAAM,EAAEzL,YAAY,KAAK;EACpH,IAAI3B,KAAK,GAAG,OAAO2B,YAAY,CAAC3B,KAAK,KAAK,QAAQ,GAAG2B,YAAY,CAAC3B,KAAK,GAAG3C,oBAAoB;EAC9F,OAAO;IACL2C,KAAK;IACL7B,MAAM,EAAEiP,MAAM,CAACjP;EACjB,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIwS,uBAAuB,GAAGA,CAACnR,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAChE,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOmP,eAAe,CAAClQ,KAAK,EAAEC,MAAM,CAAC,CAACO,KAAK;MAC7C;IACF,KAAK,OAAO;MACV;QACE,OAAO0Q,eAAe,CAAClR,KAAK,EAAEC,MAAM,CAAC,CAACtB,MAAM;MAC9C;IACF;MACE;QACE,OAAOF,SAAS;MAClB;EACJ;AACF,CAAC;AACD,OAAO,IAAI2S,sBAAsB,GAAGA,CAACC,WAAW,EAAEC,aAAa,EAAEpR,IAAI,EAAEa,QAAQ,KAAK;EAClF,IAAIb,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOzB,SAAS;EAClB;EACA,IAAI;IACFH,uBAAuB;IACvBuB,IAAI;IACJrB;EACF,CAAC,GAAG0B,IAAI;EACR,IAAIwH,aAAa,GAAG9L,iBAAiB,CAACyV,WAAW,EAAEtQ,QAAQ,CAAC;EAC5D,IAAIwQ,OAAO,GAAGD,aAAa,CAAC3O,GAAG,CAAC6O,EAAE,IAAIA,EAAE,CAAC/W,KAAK,CAAC;EAC/C,IAAI+D,OAAO,IAAIkJ,aAAa,IAAI7H,IAAI,KAAK,UAAU,IAAIvB,uBAAuB,IAAInC,YAAY,CAACoV,OAAO,CAAC,EAAE;IACvG,OAAOA,OAAO;EAChB;EACA,OAAO9S,SAAS;AAClB,CAAC;AACD,OAAO,IAAIgT,qBAAqB,GAAGrW,cAAc,CAAC,CAACG,iBAAiB,EAAEqI,sBAAsB,EAAE9C,cAAc,EAAEpD,YAAY,CAAC,EAAE0T,sBAAsB,CAAC;AACpJ,OAAO,IAAIM,wBAAwB,GAAGA,CAAC7G,MAAM,EAAEyG,aAAa,EAAEpR,IAAI,EAAEa,QAAQ,KAAK;EAC/E,IAAIb,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC1B,OAAO,IAAI,IAAI,EAAE;IACxC,OAAOC,SAAS;EAClB;EACA,IAAI;IACFoB,IAAI;IACJL;EACF,CAAC,GAAGU,IAAI;EACR,IAAIwH,aAAa,GAAG9L,iBAAiB,CAACiP,MAAM,EAAE9J,QAAQ,CAAC;EACvD,IAAI2G,aAAa,KAAK7H,IAAI,KAAK,QAAQ,IAAIL,KAAK,KAAK,MAAM,CAAC,EAAE;IAC5D,OAAO8R,aAAa,CAAC3O,GAAG,CAAC4E,CAAC,IAAIA,CAAC,CAAC9M,KAAK,CAAC;EACxC;EACA,OAAOgE,SAAS;AAClB,CAAC;AACD,OAAO,IAAIkT,uBAAuB,GAAGvW,cAAc,CAAC,CAACG,iBAAiB,EAAEqI,sBAAsB,EAAEzC,kBAAkB,EAAEzD,YAAY,CAAC,EAAEgU,wBAAwB,CAAC;AAC5J,OAAO,IAAIE,mDAAmD,GAAGxW,cAAc,CAAC,CAACG,iBAAiB,EAAE2F,2BAA2B,EAAE0K,mBAAmB,EAAE2D,eAAe,EAAEkC,qBAAqB,EAAEE,uBAAuB,EAAEvC,eAAe,EAAE/C,eAAe,EAAE3O,YAAY,CAAC,EAAE,CAACmN,MAAM,EAAE3K,IAAI,EAAE4L,aAAa,EAAEtM,KAAK,EAAEqS,eAAe,EAAElK,iBAAiB,EAAEsE,SAAS,EAAEM,SAAS,EAAExL,QAAQ,KAAK;EAClX,IAAIb,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAIwH,aAAa,GAAG9L,iBAAiB,CAACiP,MAAM,EAAE9J,QAAQ,CAAC;EACvD,OAAO;IACLxC,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;IACjBQ,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ;IACvBC,UAAU,EAAEkB,IAAI,CAAClB,UAAU;IAC3BG,WAAW,EAAEe,IAAI,CAACf,WAAW;IAC7BM,IAAI,EAAES,IAAI,CAACT,IAAI;IACfC,SAAS,EAAEQ,IAAI,CAACR,SAAS;IACzBC,aAAa,EAAEO,IAAI,CAACP,aAAa;IACjCC,KAAK,EAAEM,IAAI,CAACN,KAAK;IACjBC,IAAI,EAAEK,IAAI,CAACL,IAAI;IACfC,IAAI,EAAEI,IAAI,CAACJ,IAAI;IACfiB,QAAQ;IACR4G,iBAAiB;IACjBkK,eAAe;IACfnK,aAAa;IACb6E,SAAS;IACTlR,KAAK,EAAE4Q,SAAS;IAChBH,aAAa;IACbtM;EACF,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIsS,gBAAgB,GAAGA,CAACjH,MAAM,EAAE3K,IAAI,EAAE4L,aAAa,EAAEtM,KAAK,EAAE+M,SAAS,EAAEN,SAAS,EAAE4F,eAAe,EAAElK,iBAAiB,EAAE5G,QAAQ,KAAK;EACxI,IAAIb,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,IAAIiJ,aAAa,GAAG9L,iBAAiB,CAACiP,MAAM,EAAE9J,QAAQ,CAAC;EACvD,IAAI;IACFlB,IAAI;IACJD,KAAK;IACLF;EACF,CAAC,GAAGQ,IAAI;;EAER;EACA,IAAI6R,aAAa,GAAGjG,aAAa,KAAK,WAAW,IAAI,OAAOtM,KAAK,CAACwS,SAAS,KAAK,UAAU,GAAGxS,KAAK,CAACwS,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACtH,IAAIpE,MAAM,GAAG/N,IAAI,KAAK,UAAU,IAAIL,KAAK,CAACwS,SAAS,GAAGxS,KAAK,CAACwS,SAAS,CAAC,CAAC,GAAGD,aAAa,GAAG,CAAC;EAC3FnE,MAAM,GAAG7M,QAAQ,KAAK,WAAW,IAAIkL,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC/R,MAAM,IAAI,CAAC,GAAGqC,QAAQ,CAAC0P,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG2B,MAAM,GAAGA,MAAM;;EAE7I;EACA,IAAIqE,gBAAgB,GAAGrS,KAAK,IAAI2M,SAAS;EACzC,IAAI0F,gBAAgB,EAAE;IACpB,IAAIC,MAAM,GAAGD,gBAAgB,CAACtP,GAAG,CAAC,CAACgB,KAAK,EAAEwO,KAAK,KAAK;MAClD,IAAIC,YAAY,GAAGP,eAAe,GAAGA,eAAe,CAAClG,OAAO,CAAChI,KAAK,CAAC,GAAGA,KAAK;MAC3E,OAAO;QACLwO,KAAK;QACL;QACA;QACAE,UAAU,EAAE7S,KAAK,CAAC4S,YAAY,CAAC,GAAGxE,MAAM;QACxCnT,KAAK,EAAEkJ,KAAK;QACZiK;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAOsE,MAAM,CAACvY,MAAM,CAAC2Y,GAAG,IAAI,CAAClW,KAAK,CAACkW,GAAG,CAACD,UAAU,CAAC,CAAC;EACrD;;EAEA;EACA,IAAI3K,aAAa,IAAIC,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAAChF,GAAG,CAAC,CAACgB,KAAK,EAAEwO,KAAK,MAAM;MAC9CE,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;MACjCnT,KAAK,EAAEkJ,KAAK;MACZwO,KAAK;MACLvE;IACF,CAAC,CAAC,CAAC;EACL;EACA,IAAIpO,KAAK,CAACI,KAAK,EAAE;IACf,OAAOJ,KAAK,CAACI,KAAK,CAACF,SAAS;IAC5B;IAAA,CACCiD,GAAG,CAACgB,KAAK,KAAK;MACb0O,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;MACjCnT,KAAK,EAAEkJ,KAAK;MACZiK;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAOpO,KAAK,CAACd,MAAM,CAAC,CAAC,CAACiE,GAAG,CAAC,CAACgB,KAAK,EAAEwO,KAAK,MAAM;IAC3CE,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;IACjCnT,KAAK,EAAEoX,eAAe,GAAGA,eAAe,CAAClO,KAAK,CAAC,GAAGA,KAAK;IACvDwO,KAAK;IACLvE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAI2E,iBAAiB,GAAGnX,cAAc,CAAC,CAACG,iBAAiB,EAAE4F,kBAAkB,EAAEyK,mBAAmB,EAAE2D,eAAe,EAAElD,eAAe,EAAE+C,eAAe,EAAEqC,qBAAqB,EAAEE,uBAAuB,EAAEjU,YAAY,CAAC,EAAEoU,gBAAgB,CAAC;AAC9O,OAAO,IAAIU,yBAAyB,GAAGA,CAAC3H,MAAM,EAAE3K,IAAI,EAAEV,KAAK,EAAEyM,SAAS,EAAE4F,eAAe,EAAElK,iBAAiB,EAAE5G,QAAQ,KAAK;EACvH,IAAIb,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,IAAIyM,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;IACvF,OAAOxN,SAAS;EAClB;EACA,IAAIiJ,aAAa,GAAG9L,iBAAiB,CAACiP,MAAM,EAAE9J,QAAQ,CAAC;EACvD,IAAI;IACFrB;EACF,CAAC,GAAGQ,IAAI;EACR,IAAI0N,MAAM,GAAG,CAAC;EACdA,MAAM,GAAG7M,QAAQ,KAAK,WAAW,IAAI,CAACkL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/R,MAAM,KAAK,CAAC,GAAGqC,QAAQ,CAAC0P,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG2B,MAAM,GAAGA,MAAM;;EAEhL;EACA,IAAIlG,aAAa,IAAIC,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAAChF,GAAG,CAAC,CAACgB,KAAK,EAAEwO,KAAK,MAAM;MAC9CE,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;MACjCnT,KAAK,EAAEkJ,KAAK;MACZwO,KAAK;MACLvE;IACF,CAAC,CAAC,CAAC;EACL;EACA,IAAIpO,KAAK,CAACI,KAAK,EAAE;IACf,OAAOJ,KAAK,CAACI,KAAK,CAACF,SAAS;IAC5B;IAAA,CACCiD,GAAG,CAACgB,KAAK,KAAK;MACb0O,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;MACjCnT,KAAK,EAAEkJ,KAAK;MACZiK;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAOpO,KAAK,CAACd,MAAM,CAAC,CAAC,CAACiE,GAAG,CAAC,CAACgB,KAAK,EAAEwO,KAAK,MAAM;IAC3CE,UAAU,EAAE7S,KAAK,CAACmE,KAAK,CAAC,GAAGiK,MAAM;IACjCnT,KAAK,EAAEoX,eAAe,GAAGA,eAAe,CAAClO,KAAK,CAAC,GAAGA,KAAK;IACvDwO,KAAK;IACLvE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAI6E,0BAA0B,GAAGrX,cAAc,CAAC,CAACG,iBAAiB,EAAE4F,kBAAkB,EAAEoO,eAAe,EAAEH,eAAe,EAAEqC,qBAAqB,EAAEE,uBAAuB,EAAEjU,YAAY,CAAC,EAAE8U,yBAAyB,CAAC;AAC1N,OAAO,IAAIE,mBAAmB,GAAGtX,cAAc,CAAC0F,cAAc,EAAEyO,eAAe,EAAE,CAACrP,IAAI,EAAEV,KAAK,KAAK;EAChG,IAAIU,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,OAAOzE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAChDV;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAImT,gBAAgB,GAAGvX,cAAc,CAAC,CAAC0F,cAAc,EAAE8K,mBAAmB,EAAEN,gBAAgB,EAAEgE,0BAA0B,CAAC,EAAEvD,oBAAoB,CAAC;AAChJ,OAAO,IAAI6G,oBAAoB,GAAGxX,cAAc,CAAC,CAAC4E,KAAK,EAAE6S,SAAS,EAAE5S,MAAM,KAAKW,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC,EAAE0S,gBAAgB,EAAE,CAACzS,IAAI,EAAEV,KAAK,KAAK;EACpJ,IAAIU,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,OAAOzE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAChDV;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,OAAO,IAAIsT,oBAAoB,GAAG1X,cAAc,CAAC,CAACG,iBAAiB,EAAEuB,cAAc,EAAEC,cAAc,CAAC,EAAE,CAAC8N,MAAM,EAAEkI,QAAQ,EAAEC,QAAQ,KAAK;EACpI,QAAQnI,MAAM;IACZ,KAAK,YAAY;MACf;QACE,OAAOkI,QAAQ,CAACxR,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACX,QAAQ,CAAC,GAAG,eAAe,GAAG,eAAe;MACjF;IACF,KAAK,UAAU;MACb;QACE,OAAOyT,QAAQ,CAACzR,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACX,QAAQ,CAAC,GAAG,eAAe,GAAG,eAAe;MACjF;IACF;IACA;IACA,KAAK,SAAS;IACd,KAAK,QAAQ;MACX;QACE,OAAO,eAAe;MACxB;IACF;MACE;QACE,OAAOd,SAAS;MAClB;EACJ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
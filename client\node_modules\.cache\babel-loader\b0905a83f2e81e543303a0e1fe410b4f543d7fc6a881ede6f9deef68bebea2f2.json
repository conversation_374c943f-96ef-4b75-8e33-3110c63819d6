{"ast": null, "code": "function random(minimum, maximum) {\n  if (maximum == null) {\n    maximum = minimum;\n    minimum = 0;\n  }\n  if (minimum >= maximum) {\n    throw new Error('Invalid input: The maximum value must be greater than the minimum value.');\n  }\n  return Math.random() * (maximum - minimum) + minimum;\n}\nexport { random };", "map": {"version": 3, "names": ["random", "minimum", "maximum", "Error", "Math"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/math/random.mjs"], "sourcesContent": ["function random(minimum, maximum) {\n    if (maximum == null) {\n        maximum = minimum;\n        minimum = 0;\n    }\n    if (minimum >= maximum) {\n        throw new Error('Invalid input: The maximum value must be greater than the minimum value.');\n    }\n    return Math.random() * (maximum - minimum) + minimum;\n}\n\nexport { random };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC9B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACjBA,OAAO,GAAGD,OAAO;IACjBA,OAAO,GAAG,CAAC;EACf;EACA,IAAIA,OAAO,IAAIC,OAAO,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;EAC/F;EACA,OAAOC,IAAI,CAACJ,MAAM,CAAC,CAAC,IAAIE,OAAO,GAAGD,OAAO,CAAC,GAAGA,OAAO;AACxD;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
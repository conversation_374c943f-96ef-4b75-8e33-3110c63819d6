{"ast": null, "code": "var _ref;\nimport * as React from 'react';\nimport { uniqueId } from './DataUtils';\n\n/**\n * Fallback for React.useId() for versions prior to React 18.\n * Generates a unique ID using a simple counter and a prefix.\n *\n * @returns A unique ID that remains consistent across renders.\n */\nexport var useIdFallback = () => {\n  var [id] = React.useState(() => uniqueId('uid-'));\n  return id;\n};\n\n/*\n * This weird syntax is used to avoid a build-time error in React 17 and earlier when building with Webpack.\n * See https://github.com/webpack/webpack/issues/14814\n */\nexport var useId = (_ref = React['useId'.toString()]) !== null && _ref !== void 0 ? _ref : useIdFallback;", "map": {"version": 3, "names": ["_ref", "React", "uniqueId", "useIdFallback", "id", "useState", "useId", "toString"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/useId.js"], "sourcesContent": ["var _ref;\nimport * as React from 'react';\nimport { uniqueId } from './DataUtils';\n\n/**\n * Fallback for React.useId() for versions prior to React 18.\n * Generates a unique ID using a simple counter and a prefix.\n *\n * @returns A unique ID that remains consistent across renders.\n */\nexport var useIdFallback = () => {\n  var [id] = React.useState(() => uniqueId('uid-'));\n  return id;\n};\n\n/*\n * This weird syntax is used to avoid a build-time error in React 17 and earlier when building with Webpack.\n * See https://github.com/webpack/webpack/issues/14814\n */\nexport var useId = (_ref = React['useId'.toString()]) !== null && _ref !== void 0 ? _ref : useIdFallback;"], "mappings": "AAAA,IAAIA,IAAI;AACR,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAGA,CAAA,KAAM;EAC/B,IAAI,CAACC,EAAE,CAAC,GAAGH,KAAK,CAACI,QAAQ,CAAC,MAAMH,QAAQ,CAAC,MAAM,CAAC,CAAC;EACjD,OAAOE,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIE,KAAK,GAAG,CAACN,IAAI,GAAGC,KAAK,CAAC,OAAO,CAACM,QAAQ,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIP,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
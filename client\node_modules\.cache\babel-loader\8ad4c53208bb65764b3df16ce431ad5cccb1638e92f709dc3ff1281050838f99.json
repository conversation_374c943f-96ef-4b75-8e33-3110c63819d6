{"ast": null, "code": "function isArrayBuffer(value) {\n  return value instanceof ArrayBuffer;\n}\nexport { isArrayBuffer };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isArrayBuffer.mjs"], "sourcesContent": ["function isArrayBuffer(value) {\n    return value instanceof ArrayBuffer;\n}\n\nexport { isArrayBuffer };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,YAAYC,WAAW;AACvC;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
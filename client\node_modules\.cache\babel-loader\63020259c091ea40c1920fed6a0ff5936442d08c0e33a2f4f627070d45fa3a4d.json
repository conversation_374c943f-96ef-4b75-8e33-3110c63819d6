{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem, replaceCartesianGraphicalItem } from './graphicalItemsSlice';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  var prevPropsRef = useRef(null);\n  useEffect(() => {\n    if (prevPropsRef.current === null) {\n      dispatch(addCartesianGraphicalItem(props));\n    } else if (prevPropsRef.current !== props) {\n      dispatch(replaceCartesianGraphicalItem({\n        prev: prevPropsRef.current,\n        next: props\n      }));\n    }\n    prevPropsRef.current = props;\n  }, [dispatch, props]);\n  useEffect(() => {\n    return () => {\n      if (prevPropsRef.current) {\n        dispatch(removeCartesianGraphicalItem(prevPropsRef.current));\n        /*\n         * Here we have to reset the ref to null because in StrictMode, the effect will run twice,\n         * but it will keep the same ref value from the first render.\n         *\n         * In browser, <PERSON><PERSON> will clear the ref after the first effect cleanup,\n         * so that wouldn't be an issue.\n         *\n         * In StrictMode, however, the ref is kept,\n         * and in the hook above the code checks for `prevPropsRef.current === null`\n         * which would be false so it would not dispatch the `addCartesianGraphicalItem` action again.\n         *\n         * https://github.com/recharts/recharts/issues/6022\n         */\n        prevPropsRef.current = null;\n      }\n    };\n  }, [dispatch]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useAppDispatch", "addCartesianGraphicalItem", "addPolarGraphicalItem", "removeCartesianGraphicalItem", "removePolarGraphicalItem", "replaceCartesianGraphicalItem", "SetCartesianGraphicalItem", "props", "dispatch", "prevPropsRef", "current", "prev", "next", "SetPolarGraphicalItem"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/SetGraphicalItem.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addCartesianGraphicalItem, addPolarGraphicalItem, removeCartesianGraphicalItem, removePolarGraphicalItem, replaceCartesianGraphicalItem } from './graphicalItemsSlice';\nexport function SetCartesianGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  var prevPropsRef = useRef(null);\n  useEffect(() => {\n    if (prevPropsRef.current === null) {\n      dispatch(addCartesianGraphicalItem(props));\n    } else if (prevPropsRef.current !== props) {\n      dispatch(replaceCartesianGraphicalItem({\n        prev: prevPropsRef.current,\n        next: props\n      }));\n    }\n    prevPropsRef.current = props;\n  }, [dispatch, props]);\n  useEffect(() => {\n    return () => {\n      if (prevPropsRef.current) {\n        dispatch(removeCartesianGraphicalItem(prevPropsRef.current));\n        /*\n         * Here we have to reset the ref to null because in StrictMode, the effect will run twice,\n         * but it will keep the same ref value from the first render.\n         *\n         * In browser, <PERSON><PERSON> will clear the ref after the first effect cleanup,\n         * so that wouldn't be an issue.\n         *\n         * In StrictMode, however, the ref is kept,\n         * and in the hook above the code checks for `prevPropsRef.current === null`\n         * which would be false so it would not dispatch the `addCartesianGraphicalItem` action again.\n         *\n         * https://github.com/recharts/recharts/issues/6022\n         */\n        prevPropsRef.current = null;\n      }\n    };\n  }, [dispatch]);\n  return null;\n}\nexport function SetPolarGraphicalItem(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addPolarGraphicalItem(props));\n    return () => {\n      dispatch(removePolarGraphicalItem(props));\n    };\n  }, [dispatch, props]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,yBAAyB,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,wBAAwB,EAAEC,6BAA6B,QAAQ,uBAAuB;AAC/K,OAAO,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,IAAIC,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/B,IAAIS,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd,IAAIW,YAAY,CAACC,OAAO,KAAK,IAAI,EAAE;MACjCF,QAAQ,CAACP,yBAAyB,CAACM,KAAK,CAAC,CAAC;IAC5C,CAAC,MAAM,IAAIE,YAAY,CAACC,OAAO,KAAKH,KAAK,EAAE;MACzCC,QAAQ,CAACH,6BAA6B,CAAC;QACrCM,IAAI,EAAEF,YAAY,CAACC,OAAO;QAC1BE,IAAI,EAAEL;MACR,CAAC,CAAC,CAAC;IACL;IACAE,YAAY,CAACC,OAAO,GAAGH,KAAK;EAC9B,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrBT,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIW,YAAY,CAACC,OAAO,EAAE;QACxBF,QAAQ,CAACL,4BAA4B,CAACM,YAAY,CAACC,OAAO,CAAC,CAAC;QAC5D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACQD,YAAY,CAACC,OAAO,GAAG,IAAI;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EACd,OAAO,IAAI;AACb;AACA,OAAO,SAASK,qBAAqBA,CAACN,KAAK,EAAE;EAC3C,IAAIC,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/BF,SAAS,CAAC,MAAM;IACdU,QAAQ,CAACN,qBAAqB,CAACK,KAAK,CAAC,CAAC;IACtC,OAAO,MAAM;MACXC,QAAQ,CAACJ,wBAAwB,CAACG,KAAK,CAAC,CAAC;IAC3C,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAED,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
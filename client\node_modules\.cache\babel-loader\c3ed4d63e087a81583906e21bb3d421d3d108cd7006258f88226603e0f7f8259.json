{"ast": null, "code": "import { getStackSeriesIdentifier } from '../../../util/stacks/getStackSeriesIdentifier';\nimport { getValueByDataKey } from '../../../util/ChartUtils';\n\n/**\n * In a stacked chart, each graphical item has its own data. That data could be either:\n * - defined on the chart root, in which case the item gets a unique dataKey\n * - or defined on the item itself, in which case multiple items can share the same dataKey\n *\n * That means we cannot use the dataKey as a unique identifier for the item.\n *\n * This type represents a single data point in a stacked chart, where each key is a series identifier\n * and the value is the numeric value for that series using the numerical axis dataKey.\n */\n\nexport function combineDisplayedStackedData(stackedGraphicalItems, _ref, tooltipAxisSettings) {\n  var {\n    chartData = []\n  } = _ref;\n  var tooltipDataKey = tooltipAxisSettings === null || tooltipAxisSettings === void 0 ? void 0 : tooltipAxisSettings.dataKey;\n\n  // A map of tooltip data keys to the stacked data points\n  var knownItemsByDataKey = new Map();\n  stackedGraphicalItems.forEach(item => {\n    var _item$data;\n    // If there is no data on the individual item then we use the root chart data\n    var resolvedData = (_item$data = item.data) !== null && _item$data !== void 0 ? _item$data : chartData;\n    if (resolvedData == null || resolvedData.length === 0) {\n      // if that didn't work then we skip this item\n      return;\n    }\n    var stackIdentifier = getStackSeriesIdentifier(item);\n    resolvedData.forEach((entry, index) => {\n      var tooltipValue = tooltipDataKey == null ? index : String(getValueByDataKey(entry, tooltipDataKey, null));\n      var numericValue = getValueByDataKey(entry, item.dataKey, 0);\n      var curr;\n      if (knownItemsByDataKey.has(tooltipValue)) {\n        curr = knownItemsByDataKey.get(tooltipValue);\n      } else {\n        curr = {};\n      }\n      Object.assign(curr, {\n        [stackIdentifier]: numericValue\n      });\n      knownItemsByDataKey.set(tooltipValue, curr);\n    });\n  });\n  return Array.from(knownItemsByDataKey.values());\n}", "map": {"version": 3, "names": ["getStackSeriesIdentifier", "getValueByDataKey", "combineDisplayedStackedData", "stackedGraphicalItems", "_ref", "tooltipAxisSettings", "chartData", "tooltipDataKey", "dataKey", "knownItemsByDataKey", "Map", "for<PERSON>ach", "item", "_item$data", "resolvedData", "data", "length", "stackIdentifier", "entry", "index", "tooltipValue", "String", "numericValue", "curr", "has", "get", "Object", "assign", "set", "Array", "from", "values"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/combiners/combineDisplayedStackedData.js"], "sourcesContent": ["import { getStackSeriesIdentifier } from '../../../util/stacks/getStackSeriesIdentifier';\nimport { getValueByDataKey } from '../../../util/ChartUtils';\n\n/**\n * In a stacked chart, each graphical item has its own data. That data could be either:\n * - defined on the chart root, in which case the item gets a unique dataKey\n * - or defined on the item itself, in which case multiple items can share the same dataKey\n *\n * That means we cannot use the dataKey as a unique identifier for the item.\n *\n * This type represents a single data point in a stacked chart, where each key is a series identifier\n * and the value is the numeric value for that series using the numerical axis dataKey.\n */\n\nexport function combineDisplayedStackedData(stackedGraphicalItems, _ref, tooltipAxisSettings) {\n  var {\n    chartData = []\n  } = _ref;\n  var tooltipDataKey = tooltipAxisSettings === null || tooltipAxisSettings === void 0 ? void 0 : tooltipAxisSettings.dataKey;\n\n  // A map of tooltip data keys to the stacked data points\n  var knownItemsByDataKey = new Map();\n  stackedGraphicalItems.forEach(item => {\n    var _item$data;\n    // If there is no data on the individual item then we use the root chart data\n    var resolvedData = (_item$data = item.data) !== null && _item$data !== void 0 ? _item$data : chartData;\n    if (resolvedData == null || resolvedData.length === 0) {\n      // if that didn't work then we skip this item\n      return;\n    }\n    var stackIdentifier = getStackSeriesIdentifier(item);\n    resolvedData.forEach((entry, index) => {\n      var tooltipValue = tooltipDataKey == null ? index : String(getValueByDataKey(entry, tooltipDataKey, null));\n      var numericValue = getValueByDataKey(entry, item.dataKey, 0);\n      var curr;\n      if (knownItemsByDataKey.has(tooltipValue)) {\n        curr = knownItemsByDataKey.get(tooltipValue);\n      } else {\n        curr = {};\n      }\n      Object.assign(curr, {\n        [stackIdentifier]: numericValue\n      });\n      knownItemsByDataKey.set(tooltipValue, curr);\n    });\n  });\n  return Array.from(knownItemsByDataKey.values());\n}"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,iBAAiB,QAAQ,0BAA0B;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,2BAA2BA,CAACC,qBAAqB,EAAEC,IAAI,EAAEC,mBAAmB,EAAE;EAC5F,IAAI;IACFC,SAAS,GAAG;EACd,CAAC,GAAGF,IAAI;EACR,IAAIG,cAAc,GAAGF,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACG,OAAO;;EAE1H;EACA,IAAIC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnCP,qBAAqB,CAACQ,OAAO,CAACC,IAAI,IAAI;IACpC,IAAIC,UAAU;IACd;IACA,IAAIC,YAAY,GAAG,CAACD,UAAU,GAAGD,IAAI,CAACG,IAAI,MAAM,IAAI,IAAIF,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGP,SAAS;IACtG,IAAIQ,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;MACrD;MACA;IACF;IACA,IAAIC,eAAe,GAAGjB,wBAAwB,CAACY,IAAI,CAAC;IACpDE,YAAY,CAACH,OAAO,CAAC,CAACO,KAAK,EAAEC,KAAK,KAAK;MACrC,IAAIC,YAAY,GAAGb,cAAc,IAAI,IAAI,GAAGY,KAAK,GAAGE,MAAM,CAACpB,iBAAiB,CAACiB,KAAK,EAAEX,cAAc,EAAE,IAAI,CAAC,CAAC;MAC1G,IAAIe,YAAY,GAAGrB,iBAAiB,CAACiB,KAAK,EAAEN,IAAI,CAACJ,OAAO,EAAE,CAAC,CAAC;MAC5D,IAAIe,IAAI;MACR,IAAId,mBAAmB,CAACe,GAAG,CAACJ,YAAY,CAAC,EAAE;QACzCG,IAAI,GAAGd,mBAAmB,CAACgB,GAAG,CAACL,YAAY,CAAC;MAC9C,CAAC,MAAM;QACLG,IAAI,GAAG,CAAC,CAAC;MACX;MACAG,MAAM,CAACC,MAAM,CAACJ,IAAI,EAAE;QAClB,CAACN,eAAe,GAAGK;MACrB,CAAC,CAAC;MACFb,mBAAmB,CAACmB,GAAG,CAACR,YAAY,EAAEG,IAAI,CAAC;IAC7C,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOM,KAAK,CAACC,IAAI,CAACrB,mBAAmB,CAACsB,MAAM,CAAC,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
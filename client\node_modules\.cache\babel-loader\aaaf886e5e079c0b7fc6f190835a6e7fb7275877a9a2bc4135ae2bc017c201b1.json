{"ast": null, "code": "import * as React from 'react';\nimport { useAppSelector } from '../state/hooks';\nimport { implicitXAxis, implicitYAxis, selectXAxisSettings, selectYAxisSettings } from '../state/selectors/axisSelectors';\nimport { usePlotArea } from '../hooks';\nexport function useNeedsClip(xAxisId, yAxisId) {\n  var _xAxis$allowDataOverf, _yAxis$allowDataOverf;\n  var xAxis = useAppSelector(state => selectXAxisSettings(state, xAxisId));\n  var yAxis = useAppSelector(state => selectYAxisSettings(state, yAxisId));\n  var needClipX = (_xAxis$allowDataOverf = xAxis === null || xAxis === void 0 ? void 0 : xAxis.allowDataOverflow) !== null && _xAxis$allowDataOverf !== void 0 ? _xAxis$allowDataOverf : implicitXAxis.allowDataOverflow;\n  var needClipY = (_yAxis$allowDataOverf = yAxis === null || yAxis === void 0 ? void 0 : yAxis.allowDataOverflow) !== null && _yAxis$allowDataOverf !== void 0 ? _yAxis$allowDataOverf : implicitYAxis.allowDataOverflow;\n  var needClip = needClipX || needClipY;\n  return {\n    needClip,\n    needClipX,\n    needClipY\n  };\n}\nexport function GraphicalItemClipPath(_ref) {\n  var {\n    xAxisId,\n    yAxisId,\n    clipPathId\n  } = _ref;\n  var plotArea = usePlotArea();\n  var {\n    needClipX,\n    needClipY,\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  if (!needClip) {\n    return null;\n  }\n  var {\n    x,\n    y,\n    width,\n    height\n  } = plotArea;\n  return /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clipPath-\".concat(clipPathId)\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: needClipX ? x : x - width / 2,\n    y: needClipY ? y : y - height / 2,\n    width: needClipX ? width : width * 2,\n    height: needClipY ? height : height * 2\n  }));\n}", "map": {"version": 3, "names": ["React", "useAppSelector", "implicitXAxis", "implicitYAxis", "selectXAxisSettings", "selectYAxisSettings", "usePlotArea", "useNeedsClip", "xAxisId", "yAxisId", "_xAxis$allowDataOverf", "_yAxis$allowDataOverf", "xAxis", "state", "yAxis", "needClipX", "allowDataOverflow", "needClipY", "needClip", "GraphicalItemClipPath", "_ref", "clipPathId", "<PERSON><PERSON><PERSON>", "x", "y", "width", "height", "createElement", "id", "concat"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/cartesian/GraphicalItemClipPath.js"], "sourcesContent": ["import * as React from 'react';\nimport { useAppSelector } from '../state/hooks';\nimport { implicitXAxis, implicitYAxis, selectXAxisSettings, selectYAxisSettings } from '../state/selectors/axisSelectors';\nimport { usePlotArea } from '../hooks';\nexport function useNeedsClip(xAxisId, yAxisId) {\n  var _xAxis$allowDataOverf, _yAxis$allowDataOverf;\n  var xAxis = useAppSelector(state => selectXAxisSettings(state, xAxisId));\n  var yAxis = useAppSelector(state => selectYAxisSettings(state, yAxisId));\n  var needClipX = (_xAxis$allowDataOverf = xAxis === null || xAxis === void 0 ? void 0 : xAxis.allowDataOverflow) !== null && _xAxis$allowDataOverf !== void 0 ? _xAxis$allowDataOverf : implicitXAxis.allowDataOverflow;\n  var needClipY = (_yAxis$allowDataOverf = yAxis === null || yAxis === void 0 ? void 0 : yAxis.allowDataOverflow) !== null && _yAxis$allowDataOverf !== void 0 ? _yAxis$allowDataOverf : implicitYAxis.allowDataOverflow;\n  var needClip = needClipX || needClipY;\n  return {\n    needClip,\n    needClipX,\n    needClipY\n  };\n}\nexport function GraphicalItemClipPath(_ref) {\n  var {\n    xAxisId,\n    yAxisId,\n    clipPathId\n  } = _ref;\n  var plotArea = usePlotArea();\n  var {\n    needClipX,\n    needClipY,\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  if (!needClip) {\n    return null;\n  }\n  var {\n    x,\n    y,\n    width,\n    height\n  } = plotArea;\n  return /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clipPath-\".concat(clipPathId)\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: needClipX ? x : x - width / 2,\n    y: needClipY ? y : y - height / 2,\n    width: needClipX ? width : width * 2,\n    height: needClipY ? height : height * 2\n  }));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,kCAAkC;AACzH,SAASC,WAAW,QAAQ,UAAU;AACtC,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC7C,IAAIC,qBAAqB,EAAEC,qBAAqB;EAChD,IAAIC,KAAK,GAAGX,cAAc,CAACY,KAAK,IAAIT,mBAAmB,CAACS,KAAK,EAAEL,OAAO,CAAC,CAAC;EACxE,IAAIM,KAAK,GAAGb,cAAc,CAACY,KAAK,IAAIR,mBAAmB,CAACQ,KAAK,EAAEJ,OAAO,CAAC,CAAC;EACxE,IAAIM,SAAS,GAAG,CAACL,qBAAqB,GAAGE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,iBAAiB,MAAM,IAAI,IAAIN,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGR,aAAa,CAACc,iBAAiB;EACtN,IAAIC,SAAS,GAAG,CAACN,qBAAqB,GAAGG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,iBAAiB,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGR,aAAa,CAACa,iBAAiB;EACtN,IAAIE,QAAQ,GAAGH,SAAS,IAAIE,SAAS;EACrC,OAAO;IACLC,QAAQ;IACRH,SAAS;IACTE;EACF,CAAC;AACH;AACA,OAAO,SAASE,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,IAAI;IACFZ,OAAO;IACPC,OAAO;IACPY;EACF,CAAC,GAAGD,IAAI;EACR,IAAIE,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC5B,IAAI;IACFS,SAAS;IACTE,SAAS;IACTC;EACF,CAAC,GAAGX,YAAY,CAACC,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAI,CAACS,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAI;IACFK,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC;EACF,CAAC,GAAGJ,QAAQ;EACZ,OAAO,aAAatB,KAAK,CAAC2B,aAAa,CAAC,UAAU,EAAE;IAClDC,EAAE,EAAE,WAAW,CAACC,MAAM,CAACR,UAAU;EACnC,CAAC,EAAE,aAAarB,KAAK,CAAC2B,aAAa,CAAC,MAAM,EAAE;IAC1CJ,CAAC,EAAER,SAAS,GAAGQ,CAAC,GAAGA,CAAC,GAAGE,KAAK,GAAG,CAAC;IAChCD,CAAC,EAAEP,SAAS,GAAGO,CAAC,GAAGA,CAAC,GAAGE,MAAM,GAAG,CAAC;IACjCD,KAAK,EAAEV,SAAS,GAAGU,KAAK,GAAGA,KAAK,GAAG,CAAC;IACpCC,MAAM,EAAET,SAAS,GAAGS,MAAM,GAAGA,MAAM,GAAG;EACxC,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
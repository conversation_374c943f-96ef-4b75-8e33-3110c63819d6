{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectEventEmitter, selectSyncId, selectSyncMethod } from '../state/selectors/rootPropsSelectors';\nimport { BRUSH_SYNC_EVENT, eventCenter, TOOLTIP_SYNC_EVENT } from '../util/Events';\nimport { createEventEmitter } from '../state/optionsSlice';\nimport { setSyncInteraction } from '../state/tooltipSlice';\nimport { selectTooltipDataKey } from '../state/selectors/selectors';\nimport { selectTooltipAxisTicks } from '../state/selectors/tooltipSelectors';\nimport { selectSynchronisedTooltipState } from './syncSelectors';\nimport { useChartLayout, useViewBox } from '../context/chartLayoutContext';\nimport { setDataStartEndIndexes } from '../state/chartDataSlice';\nvar noop = () => {};\nfunction useTooltipSyncEventsListener() {\n  var mySyncId = useAppSelector(selectSyncId);\n  var myEventEmitter = useAppSelector(selectEventEmitter);\n  var dispatch = useAppDispatch();\n  var syncMethod = useAppSelector(selectSyncMethod);\n  var tooltipTicks = useAppSelector(selectTooltipAxisTicks);\n  var layout = useChartLayout();\n  var viewBox = useViewBox();\n  var className = useAppSelector(state => state.rootProps.className);\n  useEffect(() => {\n    if (mySyncId == null) {\n      // This chart is not synchronised with any other chart so we don't need to listen for any events.\n      return noop;\n    }\n    var listener = (incomingSyncId, action, emitter) => {\n      if (myEventEmitter === emitter) {\n        // We don't want to dispatch actions that we sent ourselves.\n        return;\n      }\n      if (mySyncId !== incomingSyncId) {\n        // This event is not for this chart\n        return;\n      }\n      if (syncMethod === 'index') {\n        dispatch(action);\n        // This is the default behaviour, we don't need to do anything else.\n        return;\n      }\n      if (tooltipTicks == null) {\n        // for the other two sync methods, we need the ticks to be available\n        return;\n      }\n      var activeTick;\n      if (typeof syncMethod === 'function') {\n        /*\n         * This is what the data shape in 2.x CategoricalChartState used to look like.\n         * In 3.x we store things differently but let's try to keep the old shape for compatibility.\n         */\n        var syncMethodParam = {\n          activeTooltipIndex: action.payload.index == null ? undefined : Number(action.payload.index),\n          isTooltipActive: action.payload.active,\n          activeIndex: action.payload.index == null ? undefined : Number(action.payload.index),\n          activeLabel: action.payload.label,\n          activeDataKey: action.payload.dataKey,\n          activeCoordinate: action.payload.coordinate\n        };\n        // Call a callback function. If there is an application specific algorithm\n        var activeTooltipIndex = syncMethod(tooltipTicks, syncMethodParam);\n        activeTick = tooltipTicks[activeTooltipIndex];\n      } else if (syncMethod === 'value') {\n        // labels are always strings, tick.value might be a string or a number, depending on axis type\n        activeTick = tooltipTicks.find(tick => String(tick.value) === action.payload.label);\n      }\n      var {\n        coordinate\n      } = action.payload;\n      if (activeTick == null || action.payload.active === false || coordinate == null || viewBox == null) {\n        dispatch(setSyncInteraction({\n          active: false,\n          coordinate: undefined,\n          dataKey: undefined,\n          index: null,\n          label: undefined\n        }));\n        return;\n      }\n      var {\n        x,\n        y\n      } = coordinate;\n      var validateChartX = Math.min(x, viewBox.x + viewBox.width);\n      var validateChartY = Math.min(y, viewBox.y + viewBox.height);\n      var activeCoordinate = {\n        x: layout === 'horizontal' ? activeTick.coordinate : validateChartX,\n        y: layout === 'horizontal' ? validateChartY : activeTick.coordinate\n      };\n      var syncAction = setSyncInteraction({\n        active: action.payload.active,\n        coordinate: activeCoordinate,\n        dataKey: action.payload.dataKey,\n        index: String(activeTick.index),\n        label: action.payload.label\n      });\n      dispatch(syncAction);\n    };\n    eventCenter.on(TOOLTIP_SYNC_EVENT, listener);\n    return () => {\n      eventCenter.off(TOOLTIP_SYNC_EVENT, listener);\n    };\n  }, [className, dispatch, myEventEmitter, mySyncId, syncMethod, tooltipTicks, layout, viewBox]);\n}\nfunction useBrushSyncEventsListener() {\n  var mySyncId = useAppSelector(selectSyncId);\n  var myEventEmitter = useAppSelector(selectEventEmitter);\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    if (mySyncId == null) {\n      // This chart is not synchronised with any other chart so we don't need to listen for any events.\n      return noop;\n    }\n    var listener = (incomingSyncId, action, emitter) => {\n      if (myEventEmitter === emitter) {\n        // We don't want to dispatch actions that we sent ourselves.\n        return;\n      }\n      if (mySyncId === incomingSyncId) {\n        dispatch(setDataStartEndIndexes(action));\n      }\n    };\n    eventCenter.on(BRUSH_SYNC_EVENT, listener);\n    return () => {\n      eventCenter.off(BRUSH_SYNC_EVENT, listener);\n    };\n  }, [dispatch, myEventEmitter, mySyncId]);\n}\n\n/**\n * Will receive synchronisation events from other charts.\n *\n * Reads syncMethod from state and decides how to synchronise the tooltip based on that.\n *\n * @returns void\n */\nexport function useSynchronisedEventsFromOtherCharts() {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(createEventEmitter());\n  }, [dispatch]);\n  useTooltipSyncEventsListener();\n  useBrushSyncEventsListener();\n}\n\n/**\n * Will send events to other charts.\n * If syncId is undefined, no events will be sent.\n *\n * This ignores the syncMethod, because that is set and computed on the receiving end.\n *\n * @param tooltipEventType from Tooltip\n * @param trigger from Tooltip\n * @param activeCoordinate from state\n * @param activeLabel from state\n * @param activeIndex from state\n * @param isTooltipActive from state\n * @returns void\n */\nexport function useTooltipChartSynchronisation(tooltipEventType, trigger, activeCoordinate, activeLabel, activeIndex, isTooltipActive) {\n  var activeDataKey = useAppSelector(state => selectTooltipDataKey(state, tooltipEventType, trigger));\n  var eventEmitterSymbol = useAppSelector(selectEventEmitter);\n  var syncId = useAppSelector(selectSyncId);\n  var syncMethod = useAppSelector(selectSyncMethod);\n  var tooltipState = useAppSelector(selectSynchronisedTooltipState);\n  var isReceivingSynchronisation = tooltipState === null || tooltipState === void 0 ? void 0 : tooltipState.active;\n  useEffect(() => {\n    if (isReceivingSynchronisation) {\n      /*\n       * This chart currently has active tooltip, synchronised from another chart.\n       * Let's not send any outgoing synchronisation events while that's happening\n       * to avoid infinite loops.\n       */\n      return;\n    }\n    if (syncId == null) {\n      /*\n       * syncId is not set, means that this chart is not synchronised with any other chart,\n       * means we don't need to send synchronisation events\n       */\n      return;\n    }\n    if (eventEmitterSymbol == null) {\n      /*\n       * When using Recharts internal hooks and selectors outside charts context,\n       * these properties will be undefined. Let's return silently instead of throwing an error.\n       */\n      return;\n    }\n    var syncAction = setSyncInteraction({\n      active: isTooltipActive,\n      coordinate: activeCoordinate,\n      dataKey: activeDataKey,\n      index: activeIndex,\n      label: typeof activeLabel === 'number' ? String(activeLabel) : activeLabel\n    });\n    eventCenter.emit(TOOLTIP_SYNC_EVENT, syncId, syncAction, eventEmitterSymbol);\n  }, [isReceivingSynchronisation, activeCoordinate, activeDataKey, activeIndex, activeLabel, eventEmitterSymbol, syncId, syncMethod, isTooltipActive]);\n}\nexport function useBrushChartSynchronisation() {\n  var syncId = useAppSelector(selectSyncId);\n  var eventEmitterSymbol = useAppSelector(selectEventEmitter);\n  var brushStartIndex = useAppSelector(state => state.chartData.dataStartIndex);\n  var brushEndIndex = useAppSelector(state => state.chartData.dataEndIndex);\n  useEffect(() => {\n    if (syncId == null || brushStartIndex == null || brushEndIndex == null || eventEmitterSymbol == null) {\n      return;\n    }\n    var syncAction = {\n      startIndex: brushStartIndex,\n      endIndex: brushEndIndex\n    };\n    eventCenter.emit(BRUSH_SYNC_EVENT, syncId, syncAction, eventEmitterSymbol);\n  }, [brushEndIndex, brushStartIndex, eventEmitterSymbol, syncId]);\n}", "map": {"version": 3, "names": ["useEffect", "useAppDispatch", "useAppSelector", "selectEventEmitter", "selectSyncId", "selectSyncMethod", "BRUSH_SYNC_EVENT", "eventCenter", "TOOLTIP_SYNC_EVENT", "createEventEmitter", "setSyncInteraction", "selectTooltipDataKey", "selectTooltipAxisTicks", "selectSynchronisedTooltipState", "useChartLayout", "useViewBox", "setDataStartEndIndexes", "noop", "useTooltipSyncEventsListener", "mySyncId", "myEventEmitter", "dispatch", "syncMethod", "tooltipTicks", "layout", "viewBox", "className", "state", "rootProps", "listener", "incomingSyncId", "action", "emitter", "activeTick", "syncMethodParam", "activeTooltipIndex", "payload", "index", "undefined", "Number", "isTooltipActive", "active", "activeIndex", "activeLabel", "label", "activeDataKey", "dataKey", "activeCoordinate", "coordinate", "find", "tick", "String", "value", "x", "y", "validateChartX", "Math", "min", "width", "validateChartY", "height", "syncAction", "on", "off", "useBrushSyncEventsListener", "useSynchronisedEventsFromOtherCharts", "useTooltipChartSynchronisation", "tooltipEventType", "trigger", "eventEmitterSymbol", "syncId", "tooltipState", "isReceivingSynchronisation", "emit", "useBrushChartSynchronisation", "brushStartIndex", "chartData", "dataStartIndex", "brushEndIndex", "dataEndIndex", "startIndex", "endIndex"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/synchronisation/useChartSynchronisation.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectEventEmitter, selectSyncId, selectSyncMethod } from '../state/selectors/rootPropsSelectors';\nimport { BRUSH_SYNC_EVENT, eventCenter, TOOLTIP_SYNC_EVENT } from '../util/Events';\nimport { createEventEmitter } from '../state/optionsSlice';\nimport { setSyncInteraction } from '../state/tooltipSlice';\nimport { selectTooltipDataKey } from '../state/selectors/selectors';\nimport { selectTooltipAxisTicks } from '../state/selectors/tooltipSelectors';\nimport { selectSynchronisedTooltipState } from './syncSelectors';\nimport { useChartLayout, useViewBox } from '../context/chartLayoutContext';\nimport { setDataStartEndIndexes } from '../state/chartDataSlice';\nvar noop = () => {};\nfunction useTooltipSyncEventsListener() {\n  var mySyncId = useAppSelector(selectSyncId);\n  var myEventEmitter = useAppSelector(selectEventEmitter);\n  var dispatch = useAppDispatch();\n  var syncMethod = useAppSelector(selectSyncMethod);\n  var tooltipTicks = useAppSelector(selectTooltipAxisTicks);\n  var layout = useChartLayout();\n  var viewBox = useViewBox();\n  var className = useAppSelector(state => state.rootProps.className);\n  useEffect(() => {\n    if (mySyncId == null) {\n      // This chart is not synchronised with any other chart so we don't need to listen for any events.\n      return noop;\n    }\n    var listener = (incomingSyncId, action, emitter) => {\n      if (myEventEmitter === emitter) {\n        // We don't want to dispatch actions that we sent ourselves.\n        return;\n      }\n      if (mySyncId !== incomingSyncId) {\n        // This event is not for this chart\n        return;\n      }\n      if (syncMethod === 'index') {\n        dispatch(action);\n        // This is the default behaviour, we don't need to do anything else.\n        return;\n      }\n      if (tooltipTicks == null) {\n        // for the other two sync methods, we need the ticks to be available\n        return;\n      }\n      var activeTick;\n      if (typeof syncMethod === 'function') {\n        /*\n         * This is what the data shape in 2.x CategoricalChartState used to look like.\n         * In 3.x we store things differently but let's try to keep the old shape for compatibility.\n         */\n        var syncMethodParam = {\n          activeTooltipIndex: action.payload.index == null ? undefined : Number(action.payload.index),\n          isTooltipActive: action.payload.active,\n          activeIndex: action.payload.index == null ? undefined : Number(action.payload.index),\n          activeLabel: action.payload.label,\n          activeDataKey: action.payload.dataKey,\n          activeCoordinate: action.payload.coordinate\n        };\n        // Call a callback function. If there is an application specific algorithm\n        var activeTooltipIndex = syncMethod(tooltipTicks, syncMethodParam);\n        activeTick = tooltipTicks[activeTooltipIndex];\n      } else if (syncMethod === 'value') {\n        // labels are always strings, tick.value might be a string or a number, depending on axis type\n        activeTick = tooltipTicks.find(tick => String(tick.value) === action.payload.label);\n      }\n      var {\n        coordinate\n      } = action.payload;\n      if (activeTick == null || action.payload.active === false || coordinate == null || viewBox == null) {\n        dispatch(setSyncInteraction({\n          active: false,\n          coordinate: undefined,\n          dataKey: undefined,\n          index: null,\n          label: undefined\n        }));\n        return;\n      }\n      var {\n        x,\n        y\n      } = coordinate;\n      var validateChartX = Math.min(x, viewBox.x + viewBox.width);\n      var validateChartY = Math.min(y, viewBox.y + viewBox.height);\n      var activeCoordinate = {\n        x: layout === 'horizontal' ? activeTick.coordinate : validateChartX,\n        y: layout === 'horizontal' ? validateChartY : activeTick.coordinate\n      };\n      var syncAction = setSyncInteraction({\n        active: action.payload.active,\n        coordinate: activeCoordinate,\n        dataKey: action.payload.dataKey,\n        index: String(activeTick.index),\n        label: action.payload.label\n      });\n      dispatch(syncAction);\n    };\n    eventCenter.on(TOOLTIP_SYNC_EVENT, listener);\n    return () => {\n      eventCenter.off(TOOLTIP_SYNC_EVENT, listener);\n    };\n  }, [className, dispatch, myEventEmitter, mySyncId, syncMethod, tooltipTicks, layout, viewBox]);\n}\nfunction useBrushSyncEventsListener() {\n  var mySyncId = useAppSelector(selectSyncId);\n  var myEventEmitter = useAppSelector(selectEventEmitter);\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    if (mySyncId == null) {\n      // This chart is not synchronised with any other chart so we don't need to listen for any events.\n      return noop;\n    }\n    var listener = (incomingSyncId, action, emitter) => {\n      if (myEventEmitter === emitter) {\n        // We don't want to dispatch actions that we sent ourselves.\n        return;\n      }\n      if (mySyncId === incomingSyncId) {\n        dispatch(setDataStartEndIndexes(action));\n      }\n    };\n    eventCenter.on(BRUSH_SYNC_EVENT, listener);\n    return () => {\n      eventCenter.off(BRUSH_SYNC_EVENT, listener);\n    };\n  }, [dispatch, myEventEmitter, mySyncId]);\n}\n\n/**\n * Will receive synchronisation events from other charts.\n *\n * Reads syncMethod from state and decides how to synchronise the tooltip based on that.\n *\n * @returns void\n */\nexport function useSynchronisedEventsFromOtherCharts() {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(createEventEmitter());\n  }, [dispatch]);\n  useTooltipSyncEventsListener();\n  useBrushSyncEventsListener();\n}\n\n/**\n * Will send events to other charts.\n * If syncId is undefined, no events will be sent.\n *\n * This ignores the syncMethod, because that is set and computed on the receiving end.\n *\n * @param tooltipEventType from Tooltip\n * @param trigger from Tooltip\n * @param activeCoordinate from state\n * @param activeLabel from state\n * @param activeIndex from state\n * @param isTooltipActive from state\n * @returns void\n */\nexport function useTooltipChartSynchronisation(tooltipEventType, trigger, activeCoordinate, activeLabel, activeIndex, isTooltipActive) {\n  var activeDataKey = useAppSelector(state => selectTooltipDataKey(state, tooltipEventType, trigger));\n  var eventEmitterSymbol = useAppSelector(selectEventEmitter);\n  var syncId = useAppSelector(selectSyncId);\n  var syncMethod = useAppSelector(selectSyncMethod);\n  var tooltipState = useAppSelector(selectSynchronisedTooltipState);\n  var isReceivingSynchronisation = tooltipState === null || tooltipState === void 0 ? void 0 : tooltipState.active;\n  useEffect(() => {\n    if (isReceivingSynchronisation) {\n      /*\n       * This chart currently has active tooltip, synchronised from another chart.\n       * Let's not send any outgoing synchronisation events while that's happening\n       * to avoid infinite loops.\n       */\n      return;\n    }\n    if (syncId == null) {\n      /*\n       * syncId is not set, means that this chart is not synchronised with any other chart,\n       * means we don't need to send synchronisation events\n       */\n      return;\n    }\n    if (eventEmitterSymbol == null) {\n      /*\n       * When using Recharts internal hooks and selectors outside charts context,\n       * these properties will be undefined. Let's return silently instead of throwing an error.\n       */\n      return;\n    }\n    var syncAction = setSyncInteraction({\n      active: isTooltipActive,\n      coordinate: activeCoordinate,\n      dataKey: activeDataKey,\n      index: activeIndex,\n      label: typeof activeLabel === 'number' ? String(activeLabel) : activeLabel\n    });\n    eventCenter.emit(TOOLTIP_SYNC_EVENT, syncId, syncAction, eventEmitterSymbol);\n  }, [isReceivingSynchronisation, activeCoordinate, activeDataKey, activeIndex, activeLabel, eventEmitterSymbol, syncId, syncMethod, isTooltipActive]);\n}\nexport function useBrushChartSynchronisation() {\n  var syncId = useAppSelector(selectSyncId);\n  var eventEmitterSymbol = useAppSelector(selectEventEmitter);\n  var brushStartIndex = useAppSelector(state => state.chartData.dataStartIndex);\n  var brushEndIndex = useAppSelector(state => state.chartData.dataEndIndex);\n  useEffect(() => {\n    if (syncId == null || brushStartIndex == null || brushEndIndex == null || eventEmitterSymbol == null) {\n      return;\n    }\n    var syncAction = {\n      startIndex: brushStartIndex,\n      endIndex: brushEndIndex\n    };\n    eventCenter.emit(BRUSH_SYNC_EVENT, syncId, syncAction, eventEmitterSymbol);\n  }, [brushEndIndex, brushStartIndex, eventEmitterSymbol, syncId]);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,uCAAuC;AAC1G,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClF,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,8BAA8B,QAAQ,iBAAiB;AAChE,SAASC,cAAc,EAAEC,UAAU,QAAQ,+BAA+B;AAC1E,SAASC,sBAAsB,QAAQ,yBAAyB;AAChE,IAAIC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnB,SAASC,4BAA4BA,CAAA,EAAG;EACtC,IAAIC,QAAQ,GAAGjB,cAAc,CAACE,YAAY,CAAC;EAC3C,IAAIgB,cAAc,GAAGlB,cAAc,CAACC,kBAAkB,CAAC;EACvD,IAAIkB,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EAC/B,IAAIqB,UAAU,GAAGpB,cAAc,CAACG,gBAAgB,CAAC;EACjD,IAAIkB,YAAY,GAAGrB,cAAc,CAACU,sBAAsB,CAAC;EACzD,IAAIY,MAAM,GAAGV,cAAc,CAAC,CAAC;EAC7B,IAAIW,OAAO,GAAGV,UAAU,CAAC,CAAC;EAC1B,IAAIW,SAAS,GAAGxB,cAAc,CAACyB,KAAK,IAAIA,KAAK,CAACC,SAAS,CAACF,SAAS,CAAC;EAClE1B,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,IAAI,IAAI,EAAE;MACpB;MACA,OAAOF,IAAI;IACb;IACA,IAAIY,QAAQ,GAAGA,CAACC,cAAc,EAAEC,MAAM,EAAEC,OAAO,KAAK;MAClD,IAAIZ,cAAc,KAAKY,OAAO,EAAE;QAC9B;QACA;MACF;MACA,IAAIb,QAAQ,KAAKW,cAAc,EAAE;QAC/B;QACA;MACF;MACA,IAAIR,UAAU,KAAK,OAAO,EAAE;QAC1BD,QAAQ,CAACU,MAAM,CAAC;QAChB;QACA;MACF;MACA,IAAIR,YAAY,IAAI,IAAI,EAAE;QACxB;QACA;MACF;MACA,IAAIU,UAAU;MACd,IAAI,OAAOX,UAAU,KAAK,UAAU,EAAE;QACpC;AACR;AACA;AACA;QACQ,IAAIY,eAAe,GAAG;UACpBC,kBAAkB,EAAEJ,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI,IAAI,GAAGC,SAAS,GAAGC,MAAM,CAACR,MAAM,CAACK,OAAO,CAACC,KAAK,CAAC;UAC3FG,eAAe,EAAET,MAAM,CAACK,OAAO,CAACK,MAAM;UACtCC,WAAW,EAAEX,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI,IAAI,GAAGC,SAAS,GAAGC,MAAM,CAACR,MAAM,CAACK,OAAO,CAACC,KAAK,CAAC;UACpFM,WAAW,EAAEZ,MAAM,CAACK,OAAO,CAACQ,KAAK;UACjCC,aAAa,EAAEd,MAAM,CAACK,OAAO,CAACU,OAAO;UACrCC,gBAAgB,EAAEhB,MAAM,CAACK,OAAO,CAACY;QACnC,CAAC;QACD;QACA,IAAIb,kBAAkB,GAAGb,UAAU,CAACC,YAAY,EAAEW,eAAe,CAAC;QAClED,UAAU,GAAGV,YAAY,CAACY,kBAAkB,CAAC;MAC/C,CAAC,MAAM,IAAIb,UAAU,KAAK,OAAO,EAAE;QACjC;QACAW,UAAU,GAAGV,YAAY,CAAC0B,IAAI,CAACC,IAAI,IAAIC,MAAM,CAACD,IAAI,CAACE,KAAK,CAAC,KAAKrB,MAAM,CAACK,OAAO,CAACQ,KAAK,CAAC;MACrF;MACA,IAAI;QACFI;MACF,CAAC,GAAGjB,MAAM,CAACK,OAAO;MAClB,IAAIH,UAAU,IAAI,IAAI,IAAIF,MAAM,CAACK,OAAO,CAACK,MAAM,KAAK,KAAK,IAAIO,UAAU,IAAI,IAAI,IAAIvB,OAAO,IAAI,IAAI,EAAE;QAClGJ,QAAQ,CAACX,kBAAkB,CAAC;UAC1B+B,MAAM,EAAE,KAAK;UACbO,UAAU,EAAEV,SAAS;UACrBQ,OAAO,EAAER,SAAS;UAClBD,KAAK,EAAE,IAAI;UACXO,KAAK,EAAEN;QACT,CAAC,CAAC,CAAC;QACH;MACF;MACA,IAAI;QACFe,CAAC;QACDC;MACF,CAAC,GAAGN,UAAU;MACd,IAAIO,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAE5B,OAAO,CAAC4B,CAAC,GAAG5B,OAAO,CAACiC,KAAK,CAAC;MAC3D,IAAIC,cAAc,GAAGH,IAAI,CAACC,GAAG,CAACH,CAAC,EAAE7B,OAAO,CAAC6B,CAAC,GAAG7B,OAAO,CAACmC,MAAM,CAAC;MAC5D,IAAIb,gBAAgB,GAAG;QACrBM,CAAC,EAAE7B,MAAM,KAAK,YAAY,GAAGS,UAAU,CAACe,UAAU,GAAGO,cAAc;QACnED,CAAC,EAAE9B,MAAM,KAAK,YAAY,GAAGmC,cAAc,GAAG1B,UAAU,CAACe;MAC3D,CAAC;MACD,IAAIa,UAAU,GAAGnD,kBAAkB,CAAC;QAClC+B,MAAM,EAAEV,MAAM,CAACK,OAAO,CAACK,MAAM;QAC7BO,UAAU,EAAED,gBAAgB;QAC5BD,OAAO,EAAEf,MAAM,CAACK,OAAO,CAACU,OAAO;QAC/BT,KAAK,EAAEc,MAAM,CAAClB,UAAU,CAACI,KAAK,CAAC;QAC/BO,KAAK,EAAEb,MAAM,CAACK,OAAO,CAACQ;MACxB,CAAC,CAAC;MACFvB,QAAQ,CAACwC,UAAU,CAAC;IACtB,CAAC;IACDtD,WAAW,CAACuD,EAAE,CAACtD,kBAAkB,EAAEqB,QAAQ,CAAC;IAC5C,OAAO,MAAM;MACXtB,WAAW,CAACwD,GAAG,CAACvD,kBAAkB,EAAEqB,QAAQ,CAAC;IAC/C,CAAC;EACH,CAAC,EAAE,CAACH,SAAS,EAAEL,QAAQ,EAAED,cAAc,EAAED,QAAQ,EAAEG,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,CAAC,CAAC;AAChG;AACA,SAASuC,0BAA0BA,CAAA,EAAG;EACpC,IAAI7C,QAAQ,GAAGjB,cAAc,CAACE,YAAY,CAAC;EAC3C,IAAIgB,cAAc,GAAGlB,cAAc,CAACC,kBAAkB,CAAC;EACvD,IAAIkB,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,IAAI,IAAI,EAAE;MACpB;MACA,OAAOF,IAAI;IACb;IACA,IAAIY,QAAQ,GAAGA,CAACC,cAAc,EAAEC,MAAM,EAAEC,OAAO,KAAK;MAClD,IAAIZ,cAAc,KAAKY,OAAO,EAAE;QAC9B;QACA;MACF;MACA,IAAIb,QAAQ,KAAKW,cAAc,EAAE;QAC/BT,QAAQ,CAACL,sBAAsB,CAACe,MAAM,CAAC,CAAC;MAC1C;IACF,CAAC;IACDxB,WAAW,CAACuD,EAAE,CAACxD,gBAAgB,EAAEuB,QAAQ,CAAC;IAC1C,OAAO,MAAM;MACXtB,WAAW,CAACwD,GAAG,CAACzD,gBAAgB,EAAEuB,QAAQ,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACR,QAAQ,EAAED,cAAc,EAAED,QAAQ,CAAC,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8C,oCAAoCA,CAAA,EAAG;EACrD,IAAI5C,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACdqB,QAAQ,CAACZ,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC,EAAE,CAACY,QAAQ,CAAC,CAAC;EACdH,4BAA4B,CAAC,CAAC;EAC9B8C,0BAA0B,CAAC,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,8BAA8BA,CAACC,gBAAgB,EAAEC,OAAO,EAAErB,gBAAgB,EAAEJ,WAAW,EAAED,WAAW,EAAEF,eAAe,EAAE;EACrI,IAAIK,aAAa,GAAG3C,cAAc,CAACyB,KAAK,IAAIhB,oBAAoB,CAACgB,KAAK,EAAEwC,gBAAgB,EAAEC,OAAO,CAAC,CAAC;EACnG,IAAIC,kBAAkB,GAAGnE,cAAc,CAACC,kBAAkB,CAAC;EAC3D,IAAImE,MAAM,GAAGpE,cAAc,CAACE,YAAY,CAAC;EACzC,IAAIkB,UAAU,GAAGpB,cAAc,CAACG,gBAAgB,CAAC;EACjD,IAAIkE,YAAY,GAAGrE,cAAc,CAACW,8BAA8B,CAAC;EACjE,IAAI2D,0BAA0B,GAAGD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9B,MAAM;EAChHzC,SAAS,CAAC,MAAM;IACd,IAAIwE,0BAA0B,EAAE;MAC9B;AACN;AACA;AACA;AACA;MACM;IACF;IACA,IAAIF,MAAM,IAAI,IAAI,EAAE;MAClB;AACN;AACA;AACA;MACM;IACF;IACA,IAAID,kBAAkB,IAAI,IAAI,EAAE;MAC9B;AACN;AACA;AACA;MACM;IACF;IACA,IAAIR,UAAU,GAAGnD,kBAAkB,CAAC;MAClC+B,MAAM,EAAED,eAAe;MACvBQ,UAAU,EAAED,gBAAgB;MAC5BD,OAAO,EAAED,aAAa;MACtBR,KAAK,EAAEK,WAAW;MAClBE,KAAK,EAAE,OAAOD,WAAW,KAAK,QAAQ,GAAGQ,MAAM,CAACR,WAAW,CAAC,GAAGA;IACjE,CAAC,CAAC;IACFpC,WAAW,CAACkE,IAAI,CAACjE,kBAAkB,EAAE8D,MAAM,EAAET,UAAU,EAAEQ,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACG,0BAA0B,EAAEzB,gBAAgB,EAAEF,aAAa,EAAEH,WAAW,EAAEC,WAAW,EAAE0B,kBAAkB,EAAEC,MAAM,EAAEhD,UAAU,EAAEkB,eAAe,CAAC,CAAC;AACtJ;AACA,OAAO,SAASkC,4BAA4BA,CAAA,EAAG;EAC7C,IAAIJ,MAAM,GAAGpE,cAAc,CAACE,YAAY,CAAC;EACzC,IAAIiE,kBAAkB,GAAGnE,cAAc,CAACC,kBAAkB,CAAC;EAC3D,IAAIwE,eAAe,GAAGzE,cAAc,CAACyB,KAAK,IAAIA,KAAK,CAACiD,SAAS,CAACC,cAAc,CAAC;EAC7E,IAAIC,aAAa,GAAG5E,cAAc,CAACyB,KAAK,IAAIA,KAAK,CAACiD,SAAS,CAACG,YAAY,CAAC;EACzE/E,SAAS,CAAC,MAAM;IACd,IAAIsE,MAAM,IAAI,IAAI,IAAIK,eAAe,IAAI,IAAI,IAAIG,aAAa,IAAI,IAAI,IAAIT,kBAAkB,IAAI,IAAI,EAAE;MACpG;IACF;IACA,IAAIR,UAAU,GAAG;MACfmB,UAAU,EAAEL,eAAe;MAC3BM,QAAQ,EAAEH;IACZ,CAAC;IACDvE,WAAW,CAACkE,IAAI,CAACnE,gBAAgB,EAAEgE,MAAM,EAAET,UAAU,EAAEQ,kBAAkB,CAAC;EAC5E,CAAC,EAAE,CAACS,aAAa,EAAEH,eAAe,EAAEN,kBAAkB,EAAEC,MAAM,CAAC,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
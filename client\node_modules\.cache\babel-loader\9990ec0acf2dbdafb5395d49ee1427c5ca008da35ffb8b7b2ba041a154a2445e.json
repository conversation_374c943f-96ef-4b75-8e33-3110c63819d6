{"ast": null, "code": "import { useAppSelector } from '../hooks';\nexport var selectDefaultTooltipEventType = state => state.options.defaultTooltipEventType;\nexport var selectValidateTooltipEventTypes = state => state.options.validateTooltipEventTypes;\nexport function combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes) {\n  if (shared == null) {\n    return defaultTooltipEventType;\n  }\n  var eventType = shared ? 'axis' : 'item';\n  if (validateTooltipEventTypes == null) {\n    return defaultTooltipEventType;\n  }\n  return validateTooltipEventTypes.includes(eventType) ? eventType : defaultTooltipEventType;\n}\nexport function selectTooltipEventType(state, shared) {\n  var defaultTooltipEventType = selectDefaultTooltipEventType(state);\n  var validateTooltipEventTypes = selectValidateTooltipEventTypes(state);\n  return combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes);\n}\nexport function useTooltipEventType(shared) {\n  return useAppSelector(state => selectTooltipEventType(state, shared));\n}", "map": {"version": 3, "names": ["useAppSelector", "selectDefaultTooltipEventType", "state", "options", "defaultTooltipEventType", "selectValidateTooltipEventTypes", "validateTooltipEventTypes", "combineTooltipEventType", "shared", "eventType", "includes", "selectTooltipEventType", "useTooltipEventType"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/selectTooltipEventType.js"], "sourcesContent": ["import { useAppSelector } from '../hooks';\nexport var selectDefaultTooltipEventType = state => state.options.defaultTooltipEventType;\nexport var selectValidateTooltipEventTypes = state => state.options.validateTooltipEventTypes;\nexport function combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes) {\n  if (shared == null) {\n    return defaultTooltipEventType;\n  }\n  var eventType = shared ? 'axis' : 'item';\n  if (validateTooltipEventTypes == null) {\n    return defaultTooltipEventType;\n  }\n  return validateTooltipEventTypes.includes(eventType) ? eventType : defaultTooltipEventType;\n}\nexport function selectTooltipEventType(state, shared) {\n  var defaultTooltipEventType = selectDefaultTooltipEventType(state);\n  var validateTooltipEventTypes = selectValidateTooltipEventTypes(state);\n  return combineTooltipEventType(shared, defaultTooltipEventType, validateTooltipEventTypes);\n}\nexport function useTooltipEventType(shared) {\n  return useAppSelector(state => selectTooltipEventType(state, shared));\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,OAAO,IAAIC,6BAA6B,GAAGC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACC,uBAAuB;AACzF,OAAO,IAAIC,+BAA+B,GAAGH,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACG,yBAAyB;AAC7F,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAEJ,uBAAuB,EAAEE,yBAAyB,EAAE;EAClG,IAAIE,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOJ,uBAAuB;EAChC;EACA,IAAIK,SAAS,GAAGD,MAAM,GAAG,MAAM,GAAG,MAAM;EACxC,IAAIF,yBAAyB,IAAI,IAAI,EAAE;IACrC,OAAOF,uBAAuB;EAChC;EACA,OAAOE,yBAAyB,CAACI,QAAQ,CAACD,SAAS,CAAC,GAAGA,SAAS,GAAGL,uBAAuB;AAC5F;AACA,OAAO,SAASO,sBAAsBA,CAACT,KAAK,EAAEM,MAAM,EAAE;EACpD,IAAIJ,uBAAuB,GAAGH,6BAA6B,CAACC,KAAK,CAAC;EAClE,IAAII,yBAAyB,GAAGD,+BAA+B,CAACH,KAAK,CAAC;EACtE,OAAOK,uBAAuB,CAACC,MAAM,EAAEJ,uBAAuB,EAAEE,yBAAyB,CAAC;AAC5F;AACA,OAAO,SAASM,mBAAmBA,CAACJ,MAAM,EAAE;EAC1C,OAAOR,cAAc,CAACE,KAAK,IAAIS,sBAAsB,CAACT,KAAK,EAAEM,MAAM,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
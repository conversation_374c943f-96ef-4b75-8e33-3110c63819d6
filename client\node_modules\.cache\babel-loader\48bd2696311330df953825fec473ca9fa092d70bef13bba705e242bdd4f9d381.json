{"ast": null, "code": "import { isWellBehavedNumber } from '../../../util/isWellBehavedNumber';\nexport var combineActiveTooltipIndex = (tooltipInteraction, chartData) => {\n  var desiredIndex = tooltipInteraction === null || tooltipInteraction === void 0 ? void 0 : tooltipInteraction.index;\n  if (desiredIndex == null) {\n    return null;\n  }\n  var indexAsNumber = Number(desiredIndex);\n  if (!isWellBehavedNumber(indexAsNumber)) {\n    // this is for charts like Sankey and Treemap that do not support numerical indexes. We need a proper solution for this before we can start supporting keyboard events on these charts.\n    return desiredIndex;\n  }\n\n  /*\n   * Zero is a trivial limit for single-dimensional charts like Line and Area,\n   * but this also needs a support for multidimensional charts like Sankey and Treemap! TODO\n   */\n  var lowerLimit = 0;\n  var upperLimit = +Infinity;\n  if (chartData.length > 0) {\n    upperLimit = chartData.length - 1;\n  }\n\n  // now let's clamp the desiredIndex between the limits\n  return String(Math.max(lowerLimit, Math.min(indexAsNumber, upperLimit)));\n};", "map": {"version": 3, "names": ["isWellBehavedNumber", "combineActiveTooltipIndex", "tooltipInteraction", "chartData", "desiredIndex", "index", "indexAsNumber", "Number", "lowerLimit", "upperLimit", "Infinity", "length", "String", "Math", "max", "min"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/combiners/combineActiveTooltipIndex.js"], "sourcesContent": ["import { isWellBehavedNumber } from '../../../util/isWellBehavedNumber';\nexport var combineActiveTooltipIndex = (tooltipInteraction, chartData) => {\n  var desiredIndex = tooltipInteraction === null || tooltipInteraction === void 0 ? void 0 : tooltipInteraction.index;\n  if (desiredIndex == null) {\n    return null;\n  }\n  var indexAsNumber = Number(desiredIndex);\n  if (!isWellBehavedNumber(indexAsNumber)) {\n    // this is for charts like Sankey and Treemap that do not support numerical indexes. We need a proper solution for this before we can start supporting keyboard events on these charts.\n    return desiredIndex;\n  }\n\n  /*\n   * Zero is a trivial limit for single-dimensional charts like Line and Area,\n   * but this also needs a support for multidimensional charts like Sankey and Treemap! TODO\n   */\n  var lowerLimit = 0;\n  var upperLimit = +Infinity;\n  if (chartData.length > 0) {\n    upperLimit = chartData.length - 1;\n  }\n\n  // now let's clamp the desiredIndex between the limits\n  return String(Math.max(lowerLimit, Math.min(indexAsNumber, upperLimit)));\n};"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAmC;AACvE,OAAO,IAAIC,yBAAyB,GAAGA,CAACC,kBAAkB,EAAEC,SAAS,KAAK;EACxE,IAAIC,YAAY,GAAGF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACG,KAAK;EACnH,IAAID,YAAY,IAAI,IAAI,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAIE,aAAa,GAAGC,MAAM,CAACH,YAAY,CAAC;EACxC,IAAI,CAACJ,mBAAmB,CAACM,aAAa,CAAC,EAAE;IACvC;IACA,OAAOF,YAAY;EACrB;;EAEA;AACF;AACA;AACA;EACE,IAAII,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAG,CAACC,QAAQ;EAC1B,IAAIP,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAE;IACxBF,UAAU,GAAGN,SAAS,CAACQ,MAAM,GAAG,CAAC;EACnC;;EAEA;EACA,OAAOC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACN,UAAU,EAAEK,IAAI,CAACE,GAAG,CAACT,aAAa,EAAEG,UAAU,CAAC,CAAC,CAAC;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
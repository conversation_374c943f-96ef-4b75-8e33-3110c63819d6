{"ast": null, "code": "// `victory-vendor/d3-shape` (ESM)\n// See upstream license: https://github.com/d3/d3-shape/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-shape`\nexport * from \"d3-shape\";", "map": {"version": 3, "names": [], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/victory-vendor/es/d3-shape.js"], "sourcesContent": ["\n// `victory-vendor/d3-shape` (ESM)\n// See upstream license: https://github.com/d3/d3-shape/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-shape`\nexport * from \"d3-shape\";\n"], "mappings": "AACA;AACA;AACA;AACA;AACA,cAAc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
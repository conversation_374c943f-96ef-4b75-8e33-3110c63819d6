{"ast": null, "code": "function differenceWith(firstArr, secondArr, areItemsEqual) {\n  return firstArr.filter(firstItem => {\n    return secondArr.every(secondItem => {\n      return !areItemsEqual(firstItem, secondItem);\n    });\n  });\n}\nexport { differenceWith };", "map": {"version": 3, "names": ["differenceWith", "firstArr", "secondArr", "areItemsEqual", "filter", "firstItem", "every", "secondItem"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/differenceWith.mjs"], "sourcesContent": ["function differenceWith(firstArr, secondArr, areItemsEqual) {\n    return firstArr.filter(firstItem => {\n        return secondArr.every(secondItem => {\n            return !areItemsEqual(firstItem, secondItem);\n        });\n    });\n}\n\nexport { differenceWith };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACxD,OAAOF,QAAQ,CAACG,MAAM,CAACC,SAAS,IAAI;IAChC,OAAOH,SAAS,CAACI,KAAK,CAACC,UAAU,IAAI;MACjC,OAAO,CAACJ,aAAa,CAACE,SAAS,EAAEE,UAAU,CAAC;IAChD,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAASP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
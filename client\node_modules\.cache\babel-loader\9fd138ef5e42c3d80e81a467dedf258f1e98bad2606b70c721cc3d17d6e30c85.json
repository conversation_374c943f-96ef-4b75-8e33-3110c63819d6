{"ast": null, "code": "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n  constructor(reason, description, context) {\n    super(reason);\n    this.description = description;\n    this.context = context;\n    this.type = \"TransportError\";\n  }\n}\nexport class Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} opts - options\n   * @protected\n   */\n  constructor(opts) {\n    super();\n    this.writable = false;\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n    this.query = opts.query;\n    this.socket = opts.socket;\n    this.supportsBinary = !opts.forceBase64;\n  }\n  /**\n   * Emits an error.\n   *\n   * @param {String} reason\n   * @param description\n   * @param context - the error context\n   * @return {Transport} for chaining\n   * @protected\n   */\n  onError(reason, description, context) {\n    super.emitReserved(\"error\", new TransportError(reason, description, context));\n    return this;\n  }\n  /**\n   * Opens the transport.\n   */\n  open() {\n    this.readyState = \"opening\";\n    this.doOpen();\n    return this;\n  }\n  /**\n   * Closes the transport.\n   */\n  close() {\n    if (this.readyState === \"opening\" || this.readyState === \"open\") {\n      this.doClose();\n      this.onClose();\n    }\n    return this;\n  }\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   */\n  send(packets) {\n    if (this.readyState === \"open\") {\n      this.write(packets);\n    } else {\n      // this might happen if the transport was silently closed in the beforeunload event handler\n    }\n  }\n  /**\n   * Called upon open\n   *\n   * @protected\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    super.emitReserved(\"open\");\n  }\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @protected\n   */\n  onData(data) {\n    const packet = decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n  /**\n   * Called with a decoded packet.\n   *\n   * @protected\n   */\n  onPacket(packet) {\n    super.emitReserved(\"packet\", packet);\n  }\n  /**\n   * Called upon close.\n   *\n   * @protected\n   */\n  onClose(details) {\n    this.readyState = \"closed\";\n    super.emitReserved(\"close\", details);\n  }\n  /**\n   * Pauses the transport, in order not to lose packets during an upgrade.\n   *\n   * @param onPause\n   */\n  pause(onPause) {}\n  createUri(schema, query = {}) {\n    return schema + \"://\" + this._hostname() + this._port() + this.opts.path + this._query(query);\n  }\n  _hostname() {\n    const hostname = this.opts.hostname;\n    return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n  }\n  _port() {\n    if (this.opts.port && (this.opts.secure && Number(this.opts.port !== 443) || !this.opts.secure && Number(this.opts.port) !== 80)) {\n      return \":\" + this.opts.port;\n    } else {\n      return \"\";\n    }\n  }\n  _query(query) {\n    const encodedQuery = encode(query);\n    return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n  }\n}", "map": {"version": 3, "names": ["decodePacket", "Emitter", "installTimerFunctions", "encode", "TransportError", "Error", "constructor", "reason", "description", "context", "type", "Transport", "opts", "writable", "query", "socket", "supportsBinary", "forceBase64", "onError", "emit<PERSON><PERSON><PERSON><PERSON>", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "data", "packet", "binaryType", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "length"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/engine.io-client/build/esm/transport.js"], "sourcesContent": ["import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAO,MAAMC,cAAc,SAASC,KAAK,CAAC;EACtCC,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACtC,KAAK,CAACF,MAAM,CAAC;IACb,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAG,gBAAgB;EAChC;AACJ;AACA,OAAO,MAAMC,SAAS,SAASV,OAAO,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;EACIK,WAAWA,CAACM,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrBX,qBAAqB,CAAC,IAAI,EAAEU,IAAI,CAAC;IACjC,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,KAAK,GAAGF,IAAI,CAACE,KAAK;IACvB,IAAI,CAACC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACzB,IAAI,CAACC,cAAc,GAAG,CAACJ,IAAI,CAACK,WAAW;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACX,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAClC,KAAK,CAACU,YAAY,CAAC,OAAO,EAAE,IAAIf,cAAc,CAACG,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC,CAAC;IAC7E,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIW,IAAIA,CAAA,EAAG;IACH,IAAI,CAACC,UAAU,GAAG,SAAS;IAC3B,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACF,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,MAAM,EAAE;MAC7D,IAAI,CAACG,OAAO,CAAC,CAAC;MACd,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACC,OAAO,EAAE;IACV,IAAI,IAAI,CAACN,UAAU,KAAK,MAAM,EAAE;MAC5B,IAAI,CAACO,KAAK,CAACD,OAAO,CAAC;IACvB,CAAC,MACI;MACD;IAAA;EAER;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACR,UAAU,GAAG,MAAM;IACxB,IAAI,CAACR,QAAQ,GAAG,IAAI;IACpB,KAAK,CAACM,YAAY,CAAC,MAAM,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,MAAMA,CAACC,IAAI,EAAE;IACT,MAAMC,MAAM,GAAGhC,YAAY,CAAC+B,IAAI,EAAE,IAAI,CAAChB,MAAM,CAACkB,UAAU,CAAC;IACzD,IAAI,CAACC,QAAQ,CAACF,MAAM,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIE,QAAQA,CAACF,MAAM,EAAE;IACb,KAAK,CAACb,YAAY,CAAC,QAAQ,EAAEa,MAAM,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIP,OAAOA,CAACU,OAAO,EAAE;IACb,IAAI,CAACd,UAAU,GAAG,QAAQ;IAC1B,KAAK,CAACF,YAAY,CAAC,OAAO,EAAEgB,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIC,KAAKA,CAACC,OAAO,EAAE,CAAE;EACjBC,SAASA,CAACC,MAAM,EAAEzB,KAAK,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAQyB,MAAM,GACV,KAAK,GACL,IAAI,CAACC,SAAS,CAAC,CAAC,GAChB,IAAI,CAACC,KAAK,CAAC,CAAC,GACZ,IAAI,CAAC7B,IAAI,CAAC8B,IAAI,GACd,IAAI,CAACC,MAAM,CAAC7B,KAAK,CAAC;EAC1B;EACA0B,SAASA,CAAA,EAAG;IACR,MAAMI,QAAQ,GAAG,IAAI,CAAChC,IAAI,CAACgC,QAAQ;IACnC,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGD,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;EACzE;EACAH,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC7B,IAAI,CAACkC,IAAI,KACZ,IAAI,CAAClC,IAAI,CAACmC,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACpC,IAAI,CAACkC,IAAI,KAAK,GAAG,CAAC,IAC/C,CAAC,IAAI,CAAClC,IAAI,CAACmC,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACpC,IAAI,CAACkC,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;MAC3D,OAAO,GAAG,GAAG,IAAI,CAAClC,IAAI,CAACkC,IAAI;IAC/B,CAAC,MACI;MACD,OAAO,EAAE;IACb;EACJ;EACAH,MAAMA,CAAC7B,KAAK,EAAE;IACV,MAAMmC,YAAY,GAAG9C,MAAM,CAACW,KAAK,CAAC;IAClC,OAAOmC,YAAY,CAACC,MAAM,GAAG,GAAG,GAAGD,YAAY,GAAG,EAAE;EACxD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
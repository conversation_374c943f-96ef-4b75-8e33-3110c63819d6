{"ast": null, "code": "import { flow } from './flow.mjs';\nfunction flowRight(...funcs) {\n  return flow(...funcs.reverse());\n}\nexport { flowRight };", "map": {"version": 3, "names": ["flow", "flowRight", "funcs", "reverse"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/flowRight.mjs"], "sourcesContent": ["import { flow } from './flow.mjs';\n\nfunction flowRight(...funcs) {\n    return flow(...funcs.reverse());\n}\n\nexport { flowRight };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAEjC,SAASC,SAASA,CAAC,GAAGC,KAAK,EAAE;EACzB,OAAOF,IAAI,CAAC,GAAGE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;AACnC;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
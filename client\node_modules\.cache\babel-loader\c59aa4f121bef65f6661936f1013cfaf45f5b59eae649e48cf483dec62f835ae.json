{"ast": null, "code": "function unzip(zipped) {\n  let maxLen = 0;\n  for (let i = 0; i < zipped.length; i++) {\n    if (zipped[i].length > maxLen) {\n      maxLen = zipped[i].length;\n    }\n  }\n  const result = new Array(maxLen);\n  for (let i = 0; i < maxLen; i++) {\n    result[i] = new Array(zipped.length);\n    for (let j = 0; j < zipped.length; j++) {\n      result[i][j] = zipped[j][i];\n    }\n  }\n  return result;\n}\nexport { unzip };", "map": {"version": 3, "names": ["unzip", "zipped", "maxLen", "i", "length", "result", "Array", "j"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/unzip.mjs"], "sourcesContent": ["function unzip(zipped) {\n    let maxLen = 0;\n    for (let i = 0; i < zipped.length; i++) {\n        if (zipped[i].length > maxLen) {\n            maxLen = zipped[i].length;\n        }\n    }\n    const result = new Array(maxLen);\n    for (let i = 0; i < maxLen; i++) {\n        result[i] = new Array(zipped.length);\n        for (let j = 0; j < zipped.length; j++) {\n            result[i][j] = zipped[j][i];\n        }\n    }\n    return result;\n}\n\nexport { unzip };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,MAAM,EAAE;EACnB,IAAIC,MAAM,GAAG,CAAC;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACC,MAAM,GAAGF,MAAM,EAAE;MAC3BA,MAAM,GAAGD,MAAM,CAACE,CAAC,CAAC,CAACC,MAAM;IAC7B;EACJ;EACA,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACJ,MAAM,CAAC;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC7BE,MAAM,CAACF,CAAC,CAAC,GAAG,IAAIG,KAAK,CAACL,MAAM,CAACG,MAAM,CAAC;IACpC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;MACpCF,MAAM,CAACF,CAAC,CAAC,CAACI,CAAC,CAAC,GAAGN,MAAM,CAACM,CAAC,CAAC,CAACJ,CAAC,CAAC;IAC/B;EACJ;EACA,OAAOE,MAAM;AACjB;AAEA,SAASL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\vibe coding\\\\screentime_management_app\\\\client\\\\src\\\\pages\\\\Profile.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { User, Shield, Target, Save, Award } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _user$privacy$allowFr, _user$achievements;\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [profileData, setProfileData] = useState({\n    displayName: (user === null || user === void 0 ? void 0 : user.profile.displayName) || '',\n    bio: (user === null || user === void 0 ? void 0 : user.profile.bio) || '',\n    timezone: (user === null || user === void 0 ? void 0 : user.profile.timezone) || 'UTC'\n  });\n  const [privacyData, setPrivacyData] = useState({\n    shareScreenTime: (user === null || user === void 0 ? void 0 : user.privacy.shareScreenTime) || false,\n    showInLeaderboard: (user === null || user === void 0 ? void 0 : user.privacy.showInLeaderboard) || false,\n    allowFriendRequests: (_user$privacy$allowFr = user === null || user === void 0 ? void 0 : user.privacy.allowFriendRequests) !== null && _user$privacy$allowFr !== void 0 ? _user$privacy$allowFr : true\n  });\n  const [goalsData, setGoalsData] = useState({\n    dailyLimit: user !== null && user !== void 0 && user.goals.dailyLimit ? Math.round(user.goals.dailyLimit / 60) : 480,\n    // Convert to minutes\n    weeklyLimit: user !== null && user !== void 0 && user.goals.weeklyLimit ? Math.round(user.goals.weeklyLimit / 60) : 3360,\n    categories: (user === null || user === void 0 ? void 0 : user.goals.categories.map(cat => ({\n      name: cat.name,\n      limit: Math.round(cat.limit / 60)\n    }))) || []\n  });\n  const handleProfileSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      await axios.put('/users/profile', profileData);\n      updateUser({\n        profile: {\n          ...user.profile,\n          ...profileData\n        }\n      });\n      setMessage('Profile updated successfully!');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setMessage(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to update profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePrivacySubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      await axios.put('/users/privacy', privacyData);\n      updateUser({\n        privacy: {\n          ...user.privacy,\n          ...privacyData\n        }\n      });\n      setMessage('Privacy settings updated successfully!');\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setMessage(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to update privacy settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGoalsSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      await axios.put('/users/goals', goalsData);\n      updateUser({\n        goals: {\n          ...user.goals,\n          dailyLimit: goalsData.dailyLimit * 60,\n          weeklyLimit: goalsData.weeklyLimit * 60,\n          categories: goalsData.categories.map(cat => ({\n            name: cat.name,\n            limit: cat.limit * 60\n          }))\n        }\n      });\n      setMessage('Goals updated successfully!');\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setMessage(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to update goals');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const addCategory = () => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: [...prev.categories, {\n        name: '',\n        limit: 60\n      }]\n    }));\n  };\n  const removeCategory = index => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: prev.categories.filter((_, i) => i !== index)\n    }));\n  };\n  const updateCategory = (index, field, value) => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: prev.categories.map((cat, i) => i === index ? {\n        ...cat,\n        [field]: value\n      } : cat)\n    }));\n  };\n  const tabs = [{\n    id: 'profile',\n    name: 'Profile',\n    icon: User\n  }, {\n    id: 'privacy',\n    name: 'Privacy',\n    icon: Shield\n  }, {\n    id: 'goals',\n    name: 'Goals',\n    icon: Target\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"Profile Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage your account and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-xl font-medium\",\n            children: user === null || user === void 0 ? void 0 : user.profile.displayName.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: user === null || user === void 0 ? void 0 : user.profile.displayName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [\"@\", user === null || user === void 0 ? void 0 : user.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"Member since \", new Date((user === null || user === void 0 ? void 0 : user.createdAt) || '').toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-auto flex items-center space-x-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: [/*#__PURE__*/_jsxDEV(Award, {\n                className: \"h-4 w-4 text-yellow-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: (user === null || user === void 0 ? void 0 : (_user$achievements = user.achievements) === null || _user$achievements === void 0 ? void 0 : _user$achievements.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Achievements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex space-x-1 bg-gray-100 rounded-lg p-1\",\n      children: tabs.map(tab => {\n        const Icon = tab.icon;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.id),\n          className: `flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${activeTab === tab.id ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: tab.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-4 rounded-md ${message.includes('successfully') ? 'bg-success-50 text-success-800 border border-success-200' : 'bg-danger-50 text-danger-800 border border-danger-200'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleProfileSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Display Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: profileData.displayName,\n            onChange: e => setProfileData(prev => ({\n              ...prev,\n              displayName: e.target.value\n            })),\n            className: \"input\",\n            placeholder: \"How others will see you\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Bio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: profileData.bio,\n            onChange: e => setProfileData(prev => ({\n              ...prev,\n              bio: e.target.value\n            })),\n            className: \"input\",\n            rows: 3,\n            placeholder: \"Tell others about yourself...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Timezone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: profileData.timezone,\n            onChange: e => setProfileData(prev => ({\n              ...prev,\n              timezone: e.target.value\n            })),\n            className: \"input\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"UTC\",\n              children: \"UTC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"America/New_York\",\n              children: \"Eastern Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"America/Chicago\",\n              children: \"Central Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"America/Denver\",\n              children: \"Mountain Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"America/Los_Angeles\",\n              children: \"Pacific Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Europe/London\",\n              children: \"London\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Europe/Paris\",\n              children: \"Paris\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Asia/Tokyo\",\n              children: \"Tokyo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"btn-primary flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: loading ? 'Saving...' : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), activeTab === 'privacy' && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handlePrivacySubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Privacy Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Share Screen Time Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Allow your screen time data to be visible to friends and in leaderboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: privacyData.shareScreenTime,\n                onChange: e => setPrivacyData(prev => ({\n                  ...prev,\n                  shareScreenTime: e.target.checked\n                })),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Show in Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Appear in global leaderboards and competitions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: privacyData.showInLeaderboard,\n                onChange: e => setPrivacyData(prev => ({\n                  ...prev,\n                  showInLeaderboard: e.target.checked\n                })),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Allow Friend Requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Let other users send you friend requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"relative inline-flex items-center cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: privacyData.allowFriendRequests,\n                onChange: e => setPrivacyData(prev => ({\n                  ...prev,\n                  allowFriendRequests: e.target.checked\n                })),\n                className: \"sr-only peer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"btn-primary flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: loading ? 'Saving...' : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), activeTab === 'goals' && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleGoalsSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Screen Time Goals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Daily Limit (minutes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: goalsData.dailyLimit,\n              onChange: e => setGoalsData(prev => ({\n                ...prev,\n                dailyLimit: parseInt(e.target.value) || 0\n              })),\n              className: \"input\",\n              min: \"0\",\n              placeholder: \"480\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Weekly Limit (minutes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: goalsData.weeklyLimit,\n              onChange: e => setGoalsData(prev => ({\n                ...prev,\n                weeklyLimit: parseInt(e.target.value) || 0\n              })),\n              className: \"input\",\n              min: \"0\",\n              placeholder: \"3360\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Category Limits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addCategory,\n              className: \"btn-secondary text-sm\",\n              children: \"Add Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: goalsData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: category.name,\n                onChange: e => updateCategory(index, 'name', e.target.value),\n                className: \"input flex-1\",\n                placeholder: \"Category name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: category.limit,\n                onChange: e => updateCategory(index, 'limit', parseInt(e.target.value) || 0),\n                className: \"input w-32\",\n                min: \"0\",\n                placeholder: \"Minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => removeCategory(index),\n                className: \"btn-danger text-sm\",\n                children: \"Remove\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"btn-primary flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: loading ? 'Saving...' : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"RT/2C1gYCwV4sxgaYBWjplJFD00=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "axios", "User", "Shield", "Target", "Save", "Award", "jsxDEV", "_jsxDEV", "Profile", "_s", "_user$privacy$allowFr", "_user$achievements", "user", "updateUser", "activeTab", "setActiveTab", "loading", "setLoading", "message", "setMessage", "profileData", "setProfileData", "displayName", "profile", "bio", "timezone", "privacyData", "setPrivacyData", "shareScreenTime", "privacy", "showInLeaderboard", "allowFriendRequests", "goalsData", "setGoalsData", "dailyLimit", "goals", "Math", "round", "weeklyLimit", "categories", "map", "cat", "name", "limit", "handleProfileSubmit", "e", "preventDefault", "put", "err", "_err$response", "_err$response$data", "response", "data", "handlePrivacySubmit", "_err$response2", "_err$response2$data", "handleGoalsSubmit", "_err$response3", "_err$response3$data", "addCategory", "prev", "removeCategory", "index", "filter", "_", "i", "updateCategory", "field", "value", "tabs", "id", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "username", "Date", "createdAt", "toLocaleDateString", "achievements", "length", "tab", "Icon", "onClick", "includes", "onSubmit", "type", "onChange", "target", "placeholder", "rows", "disabled", "checked", "parseInt", "min", "category", "_c", "$RefreshReg$"], "sources": ["C:/vibe coding/screentime_management_app/client/src/pages/Profile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { User, Settings, Shield, Target, Save, Award } from 'lucide-react';\n\nconst Profile: React.FC = () => {\n  const { user, updateUser } = useAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'privacy' | 'goals'>('profile');\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const [profileData, setProfileData] = useState({\n    displayName: user?.profile.displayName || '',\n    bio: user?.profile.bio || '',\n    timezone: user?.profile.timezone || 'UTC'\n  });\n\n  const [privacyData, setPrivacyData] = useState({\n    shareScreenTime: user?.privacy.shareScreenTime || false,\n    showInLeaderboard: user?.privacy.showInLeaderboard || false,\n    allowFriendRequests: user?.privacy.allowFriendRequests ?? true\n  });\n\n  const [goalsData, setGoalsData] = useState({\n    dailyLimit: user?.goals.dailyLimit ? Math.round(user.goals.dailyLimit / 60) : 480, // Convert to minutes\n    weeklyLimit: user?.goals.weeklyLimit ? Math.round(user.goals.weeklyLimit / 60) : 3360,\n    categories: user?.goals.categories.map(cat => ({\n      name: cat.name,\n      limit: Math.round(cat.limit / 60)\n    })) || []\n  });\n\n  const handleProfileSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await axios.put('/users/profile', profileData);\n      updateUser({ profile: { ...user!.profile, ...profileData } });\n      setMessage('Profile updated successfully!');\n    } catch (err: any) {\n      setMessage(err.response?.data?.message || 'Failed to update profile');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePrivacySubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await axios.put('/users/privacy', privacyData);\n      updateUser({ privacy: { ...user!.privacy, ...privacyData } });\n      setMessage('Privacy settings updated successfully!');\n    } catch (err: any) {\n      setMessage(err.response?.data?.message || 'Failed to update privacy settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGoalsSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      await axios.put('/users/goals', goalsData);\n      updateUser({ \n        goals: { \n          ...user!.goals, \n          dailyLimit: goalsData.dailyLimit * 60,\n          weeklyLimit: goalsData.weeklyLimit * 60,\n          categories: goalsData.categories.map(cat => ({\n            name: cat.name,\n            limit: cat.limit * 60\n          }))\n        } \n      });\n      setMessage('Goals updated successfully!');\n    } catch (err: any) {\n      setMessage(err.response?.data?.message || 'Failed to update goals');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const addCategory = () => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: [...prev.categories, { name: '', limit: 60 }]\n    }));\n  };\n\n  const removeCategory = (index: number) => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: prev.categories.filter((_, i) => i !== index)\n    }));\n  };\n\n  const updateCategory = (index: number, field: 'name' | 'limit', value: string | number) => {\n    setGoalsData(prev => ({\n      ...prev,\n      categories: prev.categories.map((cat, i) => \n        i === index ? { ...cat, [field]: value } : cat\n      )\n    }));\n  };\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: User },\n    { id: 'privacy', name: 'Privacy', icon: Shield },\n    { id: 'goals', name: 'Goals', icon: Target }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Profile Settings</h1>\n        <p className=\"text-gray-600\">Manage your account and preferences</p>\n      </div>\n\n      {/* User Info Card */}\n      <div className=\"card\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center\">\n            <span className=\"text-white text-xl font-medium\">\n              {user?.profile.displayName.charAt(0).toUpperCase()}\n            </span>\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900\">{user?.profile.displayName}</h2>\n            <p className=\"text-gray-600\">@{user?.username}</p>\n            <p className=\"text-sm text-gray-500\">Member since {new Date(user?.createdAt || '').toLocaleDateString()}</p>\n          </div>\n          <div className=\"ml-auto flex items-center space-x-4\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center space-x-1\">\n                <Award className=\"h-4 w-4 text-yellow-500\" />\n                <span className=\"text-lg font-semibold text-gray-900\">{user?.achievements?.length || 0}</span>\n              </div>\n              <p className=\"text-xs text-gray-500\">Achievements</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 bg-gray-100 rounded-lg p-1\">\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id as any)}\n              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                activeTab === tab.id\n                  ? 'bg-white text-gray-900 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <Icon className=\"h-4 w-4\" />\n              <span>{tab.name}</span>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Message */}\n      {message && (\n        <div className={`p-4 rounded-md ${\n          message.includes('successfully') \n            ? 'bg-success-50 text-success-800 border border-success-200' \n            : 'bg-danger-50 text-danger-800 border border-danger-200'\n        }`}>\n          {message}\n        </div>\n      )}\n\n      {/* Tab Content */}\n      <div className=\"card\">\n        {activeTab === 'profile' && (\n          <form onSubmit={handleProfileSubmit} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Profile Information</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Display Name\n              </label>\n              <input\n                type=\"text\"\n                value={profileData.displayName}\n                onChange={(e) => setProfileData(prev => ({ ...prev, displayName: e.target.value }))}\n                className=\"input\"\n                placeholder=\"How others will see you\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Bio\n              </label>\n              <textarea\n                value={profileData.bio}\n                onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}\n                className=\"input\"\n                rows={3}\n                placeholder=\"Tell others about yourself...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Timezone\n              </label>\n              <select\n                value={profileData.timezone}\n                onChange={(e) => setProfileData(prev => ({ ...prev, timezone: e.target.value }))}\n                className=\"input\"\n              >\n                <option value=\"UTC\">UTC</option>\n                <option value=\"America/New_York\">Eastern Time</option>\n                <option value=\"America/Chicago\">Central Time</option>\n                <option value=\"America/Denver\">Mountain Time</option>\n                <option value=\"America/Los_Angeles\">Pacific Time</option>\n                <option value=\"Europe/London\">London</option>\n                <option value=\"Europe/Paris\">Paris</option>\n                <option value=\"Asia/Tokyo\">Tokyo</option>\n              </select>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary flex items-center space-x-2\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>{loading ? 'Saving...' : 'Save Changes'}</span>\n            </button>\n          </form>\n        )}\n\n        {activeTab === 'privacy' && (\n          <form onSubmit={handlePrivacySubmit} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Privacy Settings</h3>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Share Screen Time Data</h4>\n                  <p className=\"text-sm text-gray-500\">Allow your screen time data to be visible to friends and in leaderboards</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={privacyData.shareScreenTime}\n                    onChange={(e) => setPrivacyData(prev => ({ ...prev, shareScreenTime: e.target.checked }))}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Show in Leaderboard</h4>\n                  <p className=\"text-sm text-gray-500\">Appear in global leaderboards and competitions</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={privacyData.showInLeaderboard}\n                    onChange={(e) => setPrivacyData(prev => ({ ...prev, showInLeaderboard: e.target.checked }))}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Allow Friend Requests</h4>\n                  <p className=\"text-sm text-gray-500\">Let other users send you friend requests</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={privacyData.allowFriendRequests}\n                    onChange={(e) => setPrivacyData(prev => ({ ...prev, allowFriendRequests: e.target.checked }))}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600\"></div>\n                </label>\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary flex items-center space-x-2\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>{loading ? 'Saving...' : 'Save Changes'}</span>\n            </button>\n          </form>\n        )}\n\n        {activeTab === 'goals' && (\n          <form onSubmit={handleGoalsSubmit} className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Screen Time Goals</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Daily Limit (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  value={goalsData.dailyLimit}\n                  onChange={(e) => setGoalsData(prev => ({ ...prev, dailyLimit: parseInt(e.target.value) || 0 }))}\n                  className=\"input\"\n                  min=\"0\"\n                  placeholder=\"480\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Weekly Limit (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  value={goalsData.weeklyLimit}\n                  onChange={(e) => setGoalsData(prev => ({ ...prev, weeklyLimit: parseInt(e.target.value) || 0 }))}\n                  className=\"input\"\n                  min=\"0\"\n                  placeholder=\"3360\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-medium text-gray-900\">Category Limits</h4>\n                <button\n                  type=\"button\"\n                  onClick={addCategory}\n                  className=\"btn-secondary text-sm\"\n                >\n                  Add Category\n                </button>\n              </div>\n\n              <div className=\"space-y-3\">\n                {goalsData.categories.map((category, index) => (\n                  <div key={index} className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"text\"\n                      value={category.name}\n                      onChange={(e) => updateCategory(index, 'name', e.target.value)}\n                      className=\"input flex-1\"\n                      placeholder=\"Category name\"\n                    />\n                    <input\n                      type=\"number\"\n                      value={category.limit}\n                      onChange={(e) => updateCategory(index, 'limit', parseInt(e.target.value) || 0)}\n                      className=\"input w-32\"\n                      min=\"0\"\n                      placeholder=\"Minutes\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => removeCategory(index)}\n                      className=\"btn-danger text-sm\"\n                    >\n                      Remove\n                    </button>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary flex items-center space-x-2\"\n            >\n              <Save className=\"h-4 w-4\" />\n              <span>{loading ? 'Saving...' : 'Save Changes'}</span>\n            </button>\n          </form>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAYC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA;EAC9B,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGd,OAAO,CAAC,CAAC;EACtC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAkC,SAAS,CAAC;EACtF,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC;IAC7CwB,WAAW,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACD,WAAW,KAAI,EAAE;IAC5CE,GAAG,EAAE,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACC,GAAG,KAAI,EAAE;IAC5BC,QAAQ,EAAE,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACE,QAAQ,KAAI;EACtC,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC;IAC7C8B,eAAe,EAAE,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,OAAO,CAACD,eAAe,KAAI,KAAK;IACvDE,iBAAiB,EAAE,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,OAAO,CAACC,iBAAiB,KAAI,KAAK;IAC3DC,mBAAmB,GAAArB,qBAAA,GAAEE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,OAAO,CAACE,mBAAmB,cAAArB,qBAAA,cAAAA,qBAAA,GAAI;EAC5D,CAAC,CAAC;EAEF,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC;IACzCoC,UAAU,EAAEtB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuB,KAAK,CAACD,UAAU,GAAGE,IAAI,CAACC,KAAK,CAACzB,IAAI,CAACuB,KAAK,CAACD,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG;IAAE;IACnFI,WAAW,EAAE1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuB,KAAK,CAACG,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACzB,IAAI,CAACuB,KAAK,CAACG,WAAW,GAAG,EAAE,CAAC,GAAG,IAAI;IACrFC,UAAU,EAAE,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,KAAK,CAACI,UAAU,CAACC,GAAG,CAACC,GAAG,KAAK;MAC7CC,IAAI,EAAED,GAAG,CAACC,IAAI;MACdC,KAAK,EAAEP,IAAI,CAACC,KAAK,CAACI,GAAG,CAACE,KAAK,GAAG,EAAE;IAClC,CAAC,CAAC,CAAC,KAAI;EACT,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMnB,KAAK,CAAC+C,GAAG,CAAC,gBAAgB,EAAE3B,WAAW,CAAC;MAC9CP,UAAU,CAAC;QAAEU,OAAO,EAAE;UAAE,GAAGX,IAAI,CAAEW,OAAO;UAAE,GAAGH;QAAY;MAAE,CAAC,CAAC;MAC7DD,UAAU,CAAC,+BAA+B,CAAC;IAC7C,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjB/B,UAAU,CAAC,EAAA8B,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBhC,OAAO,KAAI,0BAA0B,CAAC;IACvE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAOR,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMnB,KAAK,CAAC+C,GAAG,CAAC,gBAAgB,EAAErB,WAAW,CAAC;MAC9Cb,UAAU,CAAC;QAAEgB,OAAO,EAAE;UAAE,GAAGjB,IAAI,CAAEiB,OAAO;UAAE,GAAGH;QAAY;MAAE,CAAC,CAAC;MAC7DP,UAAU,CAAC,wCAAwC,CAAC;IACtD,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACjBpC,UAAU,CAAC,EAAAmC,cAAA,GAAAN,GAAG,CAACG,QAAQ,cAAAG,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcF,IAAI,cAAAG,mBAAA,uBAAlBA,mBAAA,CAAoBrC,OAAO,KAAI,mCAAmC,CAAC;IAChF,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,iBAAiB,GAAG,MAAOX,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMnB,KAAK,CAAC+C,GAAG,CAAC,cAAc,EAAEf,SAAS,CAAC;MAC1CnB,UAAU,CAAC;QACTsB,KAAK,EAAE;UACL,GAAGvB,IAAI,CAAEuB,KAAK;UACdD,UAAU,EAAEF,SAAS,CAACE,UAAU,GAAG,EAAE;UACrCI,WAAW,EAAEN,SAAS,CAACM,WAAW,GAAG,EAAE;UACvCC,UAAU,EAAEP,SAAS,CAACO,UAAU,CAACC,GAAG,CAACC,GAAG,KAAK;YAC3CC,IAAI,EAAED,GAAG,CAACC,IAAI;YACdC,KAAK,EAAEF,GAAG,CAACE,KAAK,GAAG;UACrB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACFxB,UAAU,CAAC,6BAA6B,CAAC;IAC3C,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MAAA,IAAAS,cAAA,EAAAC,mBAAA;MACjBvC,UAAU,CAAC,EAAAsC,cAAA,GAAAT,GAAG,CAACG,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBxC,OAAO,KAAI,wBAAwB,CAAC;IACrE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB1B,YAAY,CAAC2B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPrB,UAAU,EAAE,CAAC,GAAGqB,IAAI,CAACrB,UAAU,EAAE;QAAEG,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAG,CAAC;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkB,cAAc,GAAIC,KAAa,IAAK;IACxC7B,YAAY,CAAC2B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPrB,UAAU,EAAEqB,IAAI,CAACrB,UAAU,CAACwB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,cAAc,GAAGA,CAACJ,KAAa,EAAEK,KAAuB,EAAEC,KAAsB,KAAK;IACzFnC,YAAY,CAAC2B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPrB,UAAU,EAAEqB,IAAI,CAACrB,UAAU,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEwB,CAAC,KACrCA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGrB,GAAG;QAAE,CAAC0B,KAAK,GAAGC;MAAM,CAAC,GAAG3B,GAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAE5B,IAAI,EAAE,SAAS;IAAE6B,IAAI,EAAEtE;EAAK,CAAC,EAC9C;IAAEqE,EAAE,EAAE,SAAS;IAAE5B,IAAI,EAAE,SAAS;IAAE6B,IAAI,EAAErE;EAAO,CAAC,EAChD;IAAEoE,EAAE,EAAE,OAAO;IAAE5B,IAAI,EAAE,OAAO;IAAE6B,IAAI,EAAEpE;EAAO,CAAC,CAC7C;EAED,oBACEI,OAAA;IAAKiE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlE,OAAA;MAAAkE,QAAA,gBACElE,OAAA;QAAIiE,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEtE,OAAA;QAAGiE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBlE,OAAA;QAAKiE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClE,OAAA;UAAKiE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrFlE,OAAA;YAAMiE,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAC7C7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACD,WAAW,CAACwD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAIiE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAE7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,OAAO,CAACD;UAAW;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpFtE,OAAA;YAAGiE,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,GAAC,EAAC7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,QAAQ;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDtE,OAAA;YAAGiE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,eAAa,EAAC,IAAIQ,IAAI,CAAC,CAAArE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,SAAS,KAAI,EAAE,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC,eACNtE,OAAA;UAAKiE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDlE,OAAA;YAAKiE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlE,OAAA;cAAKiE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClE,OAAA,CAACF,KAAK;gBAACmE,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CtE,OAAA;gBAAMiE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAE,CAAA7D,IAAI,aAAJA,IAAI,wBAAAD,kBAAA,GAAJC,IAAI,CAAEwE,YAAY,cAAAzE,kBAAA,uBAAlBA,kBAAA,CAAoB0E,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNtE,OAAA;cAAGiE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,EACvDJ,IAAI,CAAC7B,GAAG,CAAE8C,GAAG,IAAK;QACjB,MAAMC,IAAI,GAAGD,GAAG,CAACf,IAAI;QACrB,oBACEhE,OAAA;UAEEiF,OAAO,EAAEA,CAAA,KAAMzE,YAAY,CAACuE,GAAG,CAAChB,EAAS,CAAE;UAC3CE,SAAS,EAAE,0FACT1D,SAAS,KAAKwE,GAAG,CAAChB,EAAE,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAG,QAAA,gBAEHlE,OAAA,CAACgF,IAAI;YAACf,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BtE,OAAA;YAAAkE,QAAA,EAAOa,GAAG,CAAC5C;UAAI;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATlBS,GAAG,CAAChB,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUL,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL3D,OAAO,iBACNX,OAAA;MAAKiE,SAAS,EAAE,kBACdtD,OAAO,CAACuE,QAAQ,CAAC,cAAc,CAAC,GAC5B,0DAA0D,GAC1D,uDAAuD,EAC1D;MAAAhB,QAAA,EACAvD;IAAO;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGDtE,OAAA;MAAKiE,SAAS,EAAC,MAAM;MAAAC,QAAA,GAClB3D,SAAS,KAAK,SAAS,iBACtBP,OAAA;QAAMmF,QAAQ,EAAE9C,mBAAoB;QAAC4B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxDlE,OAAA;UAAIiE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE5EtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAOiE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtE,OAAA;YACEoF,IAAI,EAAC,MAAM;YACXvB,KAAK,EAAEhD,WAAW,CAACE,WAAY;YAC/BsE,QAAQ,EAAG/C,CAAC,IAAKxB,cAAc,CAACuC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEtC,WAAW,EAAEuB,CAAC,CAACgD,MAAM,CAACzB;YAAM,CAAC,CAAC,CAAE;YACpFI,SAAS,EAAC,OAAO;YACjBsB,WAAW,EAAC;UAAyB;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAOiE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtE,OAAA;YACE6D,KAAK,EAAEhD,WAAW,CAACI,GAAI;YACvBoE,QAAQ,EAAG/C,CAAC,IAAKxB,cAAc,CAACuC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEpC,GAAG,EAAEqB,CAAC,CAACgD,MAAM,CAACzB;YAAM,CAAC,CAAC,CAAE;YAC5EI,SAAS,EAAC,OAAO;YACjBuB,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAA+B;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAOiE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtE,OAAA;YACE6D,KAAK,EAAEhD,WAAW,CAACK,QAAS;YAC5BmE,QAAQ,EAAG/C,CAAC,IAAKxB,cAAc,CAACuC,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEnC,QAAQ,EAAEoB,CAAC,CAACgD,MAAM,CAACzB;YAAM,CAAC,CAAC,CAAE;YACjFI,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBlE,OAAA;cAAQ6D,KAAK,EAAC,KAAK;cAAAK,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCtE,OAAA;cAAQ6D,KAAK,EAAC,kBAAkB;cAAAK,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtDtE,OAAA;cAAQ6D,KAAK,EAAC,iBAAiB;cAAAK,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrDtE,OAAA;cAAQ6D,KAAK,EAAC,gBAAgB;cAAAK,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrDtE,OAAA;cAAQ6D,KAAK,EAAC,qBAAqB;cAAAK,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzDtE,OAAA;cAAQ6D,KAAK,EAAC,eAAe;cAAAK,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7CtE,OAAA;cAAQ6D,KAAK,EAAC,cAAc;cAAAK,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CtE,OAAA;cAAQ6D,KAAK,EAAC,YAAY;cAAAK,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtE,OAAA;UACEoF,IAAI,EAAC,QAAQ;UACbK,QAAQ,EAAEhF,OAAQ;UAClBwD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEnDlE,OAAA,CAACH,IAAI;YAACoE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BtE,OAAA;YAAAkE,QAAA,EAAOzD,OAAO,GAAG,WAAW,GAAG;UAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACP,EAEA/D,SAAS,KAAK,SAAS,iBACtBP,OAAA;QAAMmF,QAAQ,EAAErC,mBAAoB;QAACmB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxDlE,OAAA;UAAIiE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEzEtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAIiE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEtE,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC,eACNtE,OAAA;cAAOiE,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjElE,OAAA;gBACEoF,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAEvE,WAAW,CAACE,eAAgB;gBACrCgE,QAAQ,EAAG/C,CAAC,IAAKlB,cAAc,CAACiC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEhC,eAAe,EAAEiB,CAAC,CAACgD,MAAM,CAACI;gBAAQ,CAAC,CAAC,CAAE;gBAC1FzB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFtE,OAAA;gBAAKiE,SAAS,EAAC;cAA+X;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAIiE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEtE,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eACNtE,OAAA;cAAOiE,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjElE,OAAA;gBACEoF,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAEvE,WAAW,CAACI,iBAAkB;gBACvC8D,QAAQ,EAAG/C,CAAC,IAAKlB,cAAc,CAACiC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE9B,iBAAiB,EAAEe,CAAC,CAACgD,MAAM,CAACI;gBAAQ,CAAC,CAAC,CAAE;gBAC5FzB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFtE,OAAA;gBAAKiE,SAAS,EAAC;cAA+X;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAIiE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEtE,OAAA;gBAAGiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNtE,OAAA;cAAOiE,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBACjElE,OAAA;gBACEoF,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAEvE,WAAW,CAACK,mBAAoB;gBACzC6D,QAAQ,EAAG/C,CAAC,IAAKlB,cAAc,CAACiC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE7B,mBAAmB,EAAEc,CAAC,CAACgD,MAAM,CAACI;gBAAQ,CAAC,CAAC,CAAE;gBAC9FzB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACFtE,OAAA;gBAAKiE,SAAS,EAAC;cAA+X;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UACEoF,IAAI,EAAC,QAAQ;UACbK,QAAQ,EAAEhF,OAAQ;UAClBwD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEnDlE,OAAA,CAACH,IAAI;YAACoE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BtE,OAAA;YAAAkE,QAAA,EAAOzD,OAAO,GAAG,WAAW,GAAG;UAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACP,EAEA/D,SAAS,KAAK,OAAO,iBACpBP,OAAA;QAAMmF,QAAQ,EAAElC,iBAAkB;QAACgB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtDlE,OAAA;UAAIiE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1EtE,OAAA;UAAKiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOiE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEoF,IAAI,EAAC,QAAQ;cACbvB,KAAK,EAAEpC,SAAS,CAACE,UAAW;cAC5B0D,QAAQ,EAAG/C,CAAC,IAAKZ,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1B,UAAU,EAAEgE,QAAQ,CAACrD,CAAC,CAACgD,MAAM,CAACzB,KAAK,CAAC,IAAI;cAAE,CAAC,CAAC,CAAE;cAChGI,SAAS,EAAC,OAAO;cACjB2B,GAAG,EAAC,GAAG;cACPL,WAAW,EAAC;YAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOiE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEoF,IAAI,EAAC,QAAQ;cACbvB,KAAK,EAAEpC,SAAS,CAACM,WAAY;cAC7BsD,QAAQ,EAAG/C,CAAC,IAAKZ,YAAY,CAAC2B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtB,WAAW,EAAE4D,QAAQ,CAACrD,CAAC,CAACgD,MAAM,CAACzB,KAAK,CAAC,IAAI;cAAE,CAAC,CAAC,CAAE;cACjGI,SAAS,EAAC,OAAO;cACjB2B,GAAG,EAAC,GAAG;cACPL,WAAW,EAAC;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlE,OAAA;cAAIiE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DtE,OAAA;cACEoF,IAAI,EAAC,QAAQ;cACbH,OAAO,EAAE7B,WAAY;cACrBa,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAKiE,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBzC,SAAS,CAACO,UAAU,CAACC,GAAG,CAAC,CAAC4D,QAAQ,EAAEtC,KAAK,kBACxCvD,OAAA;cAAiBiE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACtDlE,OAAA;gBACEoF,IAAI,EAAC,MAAM;gBACXvB,KAAK,EAAEgC,QAAQ,CAAC1D,IAAK;gBACrBkD,QAAQ,EAAG/C,CAAC,IAAKqB,cAAc,CAACJ,KAAK,EAAE,MAAM,EAAEjB,CAAC,CAACgD,MAAM,CAACzB,KAAK,CAAE;gBAC/DI,SAAS,EAAC,cAAc;gBACxBsB,WAAW,EAAC;cAAe;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFtE,OAAA;gBACEoF,IAAI,EAAC,QAAQ;gBACbvB,KAAK,EAAEgC,QAAQ,CAACzD,KAAM;gBACtBiD,QAAQ,EAAG/C,CAAC,IAAKqB,cAAc,CAACJ,KAAK,EAAE,OAAO,EAAEoC,QAAQ,CAACrD,CAAC,CAACgD,MAAM,CAACzB,KAAK,CAAC,IAAI,CAAC,CAAE;gBAC/EI,SAAS,EAAC,YAAY;gBACtB2B,GAAG,EAAC,GAAG;gBACPL,WAAW,EAAC;cAAS;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACFtE,OAAA;gBACEoF,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAACC,KAAK,CAAE;gBACrCU,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAC/B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAtBDf,KAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UACEoF,IAAI,EAAC,QAAQ;UACbK,QAAQ,EAAEhF,OAAQ;UAClBwD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEnDlE,OAAA,CAACH,IAAI;YAACoE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BtE,OAAA;YAAAkE,QAAA,EAAOzD,OAAO,GAAG,WAAW,GAAG;UAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CA5YID,OAAiB;EAAA,QACQT,OAAO;AAAA;AAAAsG,EAAA,GADhC7F,OAAiB;AA8YvB,eAAeA,OAAO;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
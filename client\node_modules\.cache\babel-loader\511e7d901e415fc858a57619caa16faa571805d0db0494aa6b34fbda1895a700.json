{"ast": null, "code": "import { differenceWith } from './differenceWith.mjs';\nfunction isSubsetWith(superset, subset, areItemsEqual) {\n  return differenceWith(subset, superset, areItemsEqual).length === 0;\n}\nexport { isSubsetWith };", "map": {"version": 3, "names": ["differenceWith", "isSubsetWith", "superset", "subset", "areItemsEqual", "length"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/isSubsetWith.mjs"], "sourcesContent": ["import { differenceWith } from './differenceWith.mjs';\n\nfunction isSubsetWith(superset, subset, areItemsEqual) {\n    return differenceWith(subset, superset, areItemsEqual).length === 0;\n}\n\nexport { isSubsetWith };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AAErD,SAASC,YAAYA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAE;EACnD,OAAOJ,cAAc,CAACG,MAAM,EAAED,QAAQ,EAAEE,aAAa,CAAC,CAACC,MAAM,KAAK,CAAC;AACvE;AAEA,SAASJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function rangeRight(start, end, step = 1) {\n  if (end == null) {\n    end = start;\n    start = 0;\n  }\n  if (!Number.isInteger(step) || step === 0) {\n    throw new Error(`The step value must be a non-zero integer.`);\n  }\n  const length = Math.max(Math.ceil((end - start) / step), 0);\n  const result = new Array(length);\n  for (let i = 0; i < length; i++) {\n    result[i] = start + (length - i - 1) * step;\n  }\n  return result;\n}\nexport { rangeRight };", "map": {"version": 3, "names": ["rangeRight", "start", "end", "step", "Number", "isInteger", "Error", "length", "Math", "max", "ceil", "result", "Array", "i"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/math/rangeRight.mjs"], "sourcesContent": ["function rangeRight(start, end, step = 1) {\n    if (end == null) {\n        end = start;\n        start = 0;\n    }\n    if (!Number.isInteger(step) || step === 0) {\n        throw new Error(`The step value must be a non-zero integer.`);\n    }\n    const length = Math.max(Math.ceil((end - start) / step), 0);\n    const result = new Array(length);\n    for (let i = 0; i < length; i++) {\n        result[i] = start + (length - i - 1) * step;\n    }\n    return result;\n}\n\nexport { rangeRight };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,GAAG,CAAC,EAAE;EACtC,IAAID,GAAG,IAAI,IAAI,EAAE;IACbA,GAAG,GAAGD,KAAK;IACXA,KAAK,GAAG,CAAC;EACb;EACA,IAAI,CAACG,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,CAAC,EAAE;IACvC,MAAM,IAAIG,KAAK,CAAC,4CAA4C,CAAC;EACjE;EACA,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAACR,GAAG,GAAGD,KAAK,IAAIE,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D,MAAMQ,MAAM,GAAG,IAAIC,KAAK,CAACL,MAAM,CAAC;EAChC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC7BF,MAAM,CAACE,CAAC,CAAC,GAAGZ,KAAK,GAAG,CAACM,MAAM,GAAGM,CAAC,GAAG,CAAC,IAAIV,IAAI;EAC/C;EACA,OAAOQ,MAAM;AACjB;AAEA,SAASX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
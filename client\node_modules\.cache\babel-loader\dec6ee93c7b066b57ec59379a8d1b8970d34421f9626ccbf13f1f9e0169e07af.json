{"ast": null, "code": "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}", "map": {"version": 3, "names": ["filter", "values", "test", "TypeError", "array", "index", "value", "push"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/d3-array/src/filter.js"], "sourcesContent": ["export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC3C,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIL,MAAM,EAAE;IAC1B,IAAIC,IAAI,CAACI,KAAK,EAAE,EAAED,KAAK,EAAEJ,MAAM,CAAC,EAAE;MAChCG,KAAK,CAACG,IAAI,CAACD,KAAK,CAAC;IACnB;EACF;EACA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
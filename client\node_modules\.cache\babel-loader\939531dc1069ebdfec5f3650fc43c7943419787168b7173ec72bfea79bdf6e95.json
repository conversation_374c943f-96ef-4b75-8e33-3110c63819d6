{"ast": null, "code": "import constant from \"./constant.js\";\nimport { withPath } from \"./path.js\";\nimport asterisk from \"./symbol/asterisk.js\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport diamond2 from \"./symbol/diamond2.js\";\nimport plus from \"./symbol/plus.js\";\nimport square from \"./symbol/square.js\";\nimport square2 from \"./symbol/square2.js\";\nimport star from \"./symbol/star.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport triangle2 from \"./symbol/triangle2.js\";\nimport wye from \"./symbol/wye.js\";\nimport times from \"./symbol/times.js\";\n\n// These symbols are designed to be filled.\nexport const symbolsFill = [circle, cross, diamond, square, star, triangle, wye];\n\n// These symbols are designed to be stroked (with a width of 1.5px and round caps).\nexport const symbolsStroke = [circle, plus, times, triangle2, asterisk, square2, diamond2];\nexport default function Symbol(type, size) {\n  let context = null,\n    path = withPath(symbol);\n  type = typeof type === \"function\" ? type : constant(type || circle);\n  size = typeof size === \"function\" ? size : constant(size === undefined ? 64 : +size);\n  function symbol() {\n    let buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n  symbol.type = function (_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n  symbol.size = function (_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n  symbol.context = function (_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n  return symbol;\n}", "map": {"version": 3, "names": ["constant", "with<PERSON><PERSON>", "asterisk", "circle", "cross", "diamond", "diamond2", "plus", "square", "square2", "star", "triangle", "triangle2", "wye", "times", "symbolsFill", "symbolsStroke", "Symbol", "type", "size", "context", "path", "symbol", "undefined", "buffer", "apply", "arguments", "draw", "_", "length"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/d3-shape/src/symbol.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport {withPath} from \"./path.js\";\nimport asterisk from \"./symbol/asterisk.js\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport diamond2 from \"./symbol/diamond2.js\";\nimport plus from \"./symbol/plus.js\";\nimport square from \"./symbol/square.js\";\nimport square2 from \"./symbol/square2.js\";\nimport star from \"./symbol/star.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport triangle2 from \"./symbol/triangle2.js\";\nimport wye from \"./symbol/wye.js\";\nimport times from \"./symbol/times.js\";\n\n// These symbols are designed to be filled.\nexport const symbolsFill = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\n// These symbols are designed to be stroked (with a width of 1.5px and round caps).\nexport const symbolsStroke = [\n  circle,\n  plus,\n  times,\n  triangle2,\n  asterisk,\n  square2,\n  diamond2\n];\n\nexport default function Symbol(type, size) {\n  let context = null,\n      path = withPath(symbol);\n\n  type = typeof type === \"function\" ? type : constant(type || circle);\n  size = typeof size === \"function\" ? size : constant(size === undefined ? 64 : +size);\n\n  function symbol() {\n    let buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAAQC,QAAQ,QAAO,WAAW;AAClC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,KAAK,MAAM,mBAAmB;;AAErC;AACA,OAAO,MAAMC,WAAW,GAAG,CACzBZ,MAAM,EACNC,KAAK,EACLC,OAAO,EACPG,MAAM,EACNE,IAAI,EACJC,QAAQ,EACRE,GAAG,CACJ;;AAED;AACA,OAAO,MAAMG,aAAa,GAAG,CAC3Bb,MAAM,EACNI,IAAI,EACJO,KAAK,EACLF,SAAS,EACTV,QAAQ,EACRO,OAAO,EACPH,QAAQ,CACT;AAED,eAAe,SAASW,MAAMA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACzC,IAAIC,OAAO,GAAG,IAAI;IACdC,IAAI,GAAGpB,QAAQ,CAACqB,MAAM,CAAC;EAE3BJ,IAAI,GAAG,OAAOA,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGlB,QAAQ,CAACkB,IAAI,IAAIf,MAAM,CAAC;EACnEgB,IAAI,GAAG,OAAOA,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGnB,QAAQ,CAACmB,IAAI,KAAKI,SAAS,GAAG,EAAE,GAAG,CAACJ,IAAI,CAAC;EAEpF,SAASG,MAAMA,CAAA,EAAG;IAChB,IAAIE,MAAM;IACV,IAAI,CAACJ,OAAO,EAAEA,OAAO,GAAGI,MAAM,GAAGH,IAAI,CAAC,CAAC;IACvCH,IAAI,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACC,IAAI,CAACP,OAAO,EAAE,CAACD,IAAI,CAACM,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;IACvE,IAAIF,MAAM,EAAE,OAAOJ,OAAO,GAAG,IAAI,EAAEI,MAAM,GAAG,EAAE,IAAI,IAAI;EACxD;EAEAF,MAAM,CAACJ,IAAI,GAAG,UAASU,CAAC,EAAE;IACxB,OAAOF,SAAS,CAACG,MAAM,IAAIX,IAAI,GAAG,OAAOU,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5B,QAAQ,CAAC4B,CAAC,CAAC,EAAEN,MAAM,IAAIJ,IAAI;EAC7F,CAAC;EAEDI,MAAM,CAACH,IAAI,GAAG,UAASS,CAAC,EAAE;IACxB,OAAOF,SAAS,CAACG,MAAM,IAAIV,IAAI,GAAG,OAAOS,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5B,QAAQ,CAAC,CAAC4B,CAAC,CAAC,EAAEN,MAAM,IAAIH,IAAI;EAC9F,CAAC;EAEDG,MAAM,CAACF,OAAO,GAAG,UAASQ,CAAC,EAAE;IAC3B,OAAOF,SAAS,CAACG,MAAM,IAAIT,OAAO,GAAGQ,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,CAAC,EAAEN,MAAM,IAAIF,OAAO;EAC9E,CAAC;EAED,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
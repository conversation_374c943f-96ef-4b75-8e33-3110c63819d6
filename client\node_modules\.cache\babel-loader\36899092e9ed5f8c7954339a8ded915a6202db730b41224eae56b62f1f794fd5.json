{"ast": null, "code": "import { cloneDeepWithImpl } from './cloneDeepWith.mjs';\nfunction cloneDeep(obj) {\n  return cloneDeepWithImpl(obj, undefined, obj, new Map(), undefined);\n}\nexport { cloneDeep };", "map": {"version": 3, "names": ["cloneDeepWithImpl", "cloneDeep", "obj", "undefined", "Map"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/cloneDeep.mjs"], "sourcesContent": ["import { cloneDeepWithImpl } from './cloneDeepWith.mjs';\n\nfunction cloneDeep(obj) {\n    return cloneDeepWithImpl(obj, undefined, obj, new Map(), undefined);\n}\n\nexport { cloneDeep };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AAEvD,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,OAAOF,iBAAiB,CAACE,GAAG,EAAEC,SAAS,EAAED,GAAG,EAAE,IAAIE,GAAG,CAAC,CAAC,EAAED,SAAS,CAAC;AACvE;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
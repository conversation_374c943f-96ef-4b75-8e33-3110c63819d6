{"ast": null, "code": "import { createContext, useContext, useMemo } from 'react';\nimport { createDefaultAnimationManager } from './createDefaultAnimationManager';\nexport var AnimationManagerContext = /*#__PURE__*/createContext(createDefaultAnimationManager);\nexport function useAnimationManager(animationId, animationManagerFromProps) {\n  var contextAnimationManager = useContext(AnimationManagerContext);\n  return useMemo(() => animationManagerFromProps !== null && animationManagerFromProps !== void 0 ? animationManagerFromProps : contextAnimationManager(animationId), [animationId, animationManagerFromProps, contextAnimationManager]);\n}", "map": {"version": 3, "names": ["createContext", "useContext", "useMemo", "createDefaultAnimationManager", "AnimationManagerContext", "useAnimationManager", "animationId", "animationManagerFromProps", "contextAnimationManager"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/animation/useAnimationManager.js"], "sourcesContent": ["import { createContext, useContext, useMemo } from 'react';\nimport { createDefaultAnimationManager } from './createDefaultAnimationManager';\nexport var AnimationManagerContext = /*#__PURE__*/createContext(createDefaultAnimationManager);\nexport function useAnimationManager(animationId, animationManagerFromProps) {\n  var contextAnimationManager = useContext(AnimationManagerContext);\n  return useMemo(() => animationManagerFromProps !== null && animationManagerFromProps !== void 0 ? animationManagerFromProps : contextAnimationManager(animationId), [animationId, animationManagerFromProps, contextAnimationManager]);\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC1D,SAASC,6BAA6B,QAAQ,iCAAiC;AAC/E,OAAO,IAAIC,uBAAuB,GAAG,aAAaJ,aAAa,CAACG,6BAA6B,CAAC;AAC9F,OAAO,SAASE,mBAAmBA,CAACC,WAAW,EAAEC,yBAAyB,EAAE;EAC1E,IAAIC,uBAAuB,GAAGP,UAAU,CAACG,uBAAuB,CAAC;EACjE,OAAOF,OAAO,CAAC,MAAMK,yBAAyB,KAAK,IAAI,IAAIA,yBAAyB,KAAK,KAAK,CAAC,GAAGA,yBAAyB,GAAGC,uBAAuB,CAACF,WAAW,CAAC,EAAE,CAACA,WAAW,EAAEC,yBAAyB,EAAEC,uBAAuB,CAAC,CAAC;AACxO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
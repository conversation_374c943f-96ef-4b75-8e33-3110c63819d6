{"ast": null, "code": "import { delay } from '../promise/delay.mjs';\nconst DEFAULT_DELAY = 0;\nconst DEFAULT_RETRIES = Number.POSITIVE_INFINITY;\nasync function retry(func, _options) {\n  let delay$1;\n  let retries;\n  let signal;\n  if (typeof _options === 'number') {\n    delay$1 = DEFAULT_DELAY;\n    retries = _options;\n    signal = undefined;\n  } else {\n    delay$1 = _options?.delay ?? DEFAULT_DELAY;\n    retries = _options?.retries ?? DEFAULT_RETRIES;\n    signal = _options?.signal;\n  }\n  let error;\n  for (let attempts = 0; attempts < retries; attempts++) {\n    if (signal?.aborted) {\n      throw error ?? new Error(`The retry operation was aborted due to an abort signal.`);\n    }\n    try {\n      return await func();\n    } catch (err) {\n      error = err;\n      const currentDelay = typeof delay$1 === 'function' ? delay$1(attempts) : delay$1;\n      await delay(currentDelay);\n    }\n  }\n  throw error;\n}\nexport { retry };", "map": {"version": 3, "names": ["delay", "DEFAULT_DELAY", "DEFAULT_RETRIES", "Number", "POSITIVE_INFINITY", "retry", "func", "_options", "delay$1", "retries", "signal", "undefined", "error", "attempts", "aborted", "Error", "err", "current<PERSON><PERSON><PERSON>"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/retry.mjs"], "sourcesContent": ["import { delay } from '../promise/delay.mjs';\n\nconst DEFAULT_DELAY = 0;\nconst DEFAULT_RETRIES = Number.POSITIVE_INFINITY;\nasync function retry(func, _options) {\n    let delay$1;\n    let retries;\n    let signal;\n    if (typeof _options === 'number') {\n        delay$1 = DEFAULT_DELAY;\n        retries = _options;\n        signal = undefined;\n    }\n    else {\n        delay$1 = _options?.delay ?? DEFAULT_DELAY;\n        retries = _options?.retries ?? DEFAULT_RETRIES;\n        signal = _options?.signal;\n    }\n    let error;\n    for (let attempts = 0; attempts < retries; attempts++) {\n        if (signal?.aborted) {\n            throw error ?? new Error(`The retry operation was aborted due to an abort signal.`);\n        }\n        try {\n            return await func();\n        }\n        catch (err) {\n            error = err;\n            const currentDelay = typeof delay$1 === 'function' ? delay$1(attempts) : delay$1;\n            await delay(currentDelay);\n        }\n    }\n    throw error;\n}\n\nexport { retry };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,sBAAsB;AAE5C,MAAMC,aAAa,GAAG,CAAC;AACvB,MAAMC,eAAe,GAAGC,MAAM,CAACC,iBAAiB;AAChD,eAAeC,KAAKA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACjC,IAAIC,OAAO;EACX,IAAIC,OAAO;EACX,IAAIC,MAAM;EACV,IAAI,OAAOH,QAAQ,KAAK,QAAQ,EAAE;IAC9BC,OAAO,GAAGP,aAAa;IACvBQ,OAAO,GAAGF,QAAQ;IAClBG,MAAM,GAAGC,SAAS;EACtB,CAAC,MACI;IACDH,OAAO,GAAGD,QAAQ,EAAEP,KAAK,IAAIC,aAAa;IAC1CQ,OAAO,GAAGF,QAAQ,EAAEE,OAAO,IAAIP,eAAe;IAC9CQ,MAAM,GAAGH,QAAQ,EAAEG,MAAM;EAC7B;EACA,IAAIE,KAAK;EACT,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGJ,OAAO,EAAEI,QAAQ,EAAE,EAAE;IACnD,IAAIH,MAAM,EAAEI,OAAO,EAAE;MACjB,MAAMF,KAAK,IAAI,IAAIG,KAAK,CAAC,yDAAyD,CAAC;IACvF;IACA,IAAI;MACA,OAAO,MAAMT,IAAI,CAAC,CAAC;IACvB,CAAC,CACD,OAAOU,GAAG,EAAE;MACRJ,KAAK,GAAGI,GAAG;MACX,MAAMC,YAAY,GAAG,OAAOT,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACK,QAAQ,CAAC,GAAGL,OAAO;MAChF,MAAMR,KAAK,CAACiB,YAAY,CAAC;IAC7B;EACJ;EACA,MAAML,KAAK;AACf;AAEA,SAASP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
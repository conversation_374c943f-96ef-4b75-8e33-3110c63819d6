{"ast": null, "code": "import { useEffect, useState } from 'react';\nimport { noop } from 'es-toolkit';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { useAnimationManager } from './useAnimationManager';\nimport { getTransitionVal } from './util';\nvar defaultProps = {\n  begin: 0,\n  duration: 1000,\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n};\nexport function CSSTransitionAnimate(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultProps);\n  var {\n    from,\n    to,\n    attributeName,\n    isActive,\n    canBegin,\n    duration,\n    easing,\n    begin,\n    onAnimationEnd,\n    onAnimationStart,\n    children\n  } = props;\n  var animationManager = useAnimationManager(attributeName, props.animationManager);\n  var [style, setStyle] = useState(isActive ? from : to);\n  useEffect(() => {\n    if (!isActive) {\n      setStyle(to);\n    }\n  }, [isActive, to]);\n  useEffect(() => {\n    if (!isActive || !canBegin) {\n      return noop;\n    }\n    var unsubscribe = animationManager.subscribe(setStyle);\n    animationManager.start([onAnimationStart, begin, to, duration, onAnimationEnd]);\n    return () => {\n      animationManager.stop();\n      if (unsubscribe) {\n        unsubscribe();\n      }\n      onAnimationEnd();\n    };\n  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager, to]);\n  if (isActive && canBegin) {\n    var transition = getTransitionVal([attributeName], duration, easing);\n    return children({\n      transition,\n      [attributeName]: style\n    });\n  }\n  return children({\n    [attributeName]: style\n  });\n}", "map": {"version": 3, "names": ["useEffect", "useState", "noop", "resolveDefaultProps", "useAnimationManager", "getTransitionVal", "defaultProps", "begin", "duration", "easing", "isActive", "canBegin", "onAnimationEnd", "onAnimationStart", "CSSTransitionAnimate", "outsideProps", "props", "from", "to", "attributeName", "children", "animationManager", "style", "setStyle", "unsubscribe", "subscribe", "start", "stop", "transition"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/animation/CSSTransitionAnimate.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { noop } from 'es-toolkit';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { useAnimationManager } from './useAnimationManager';\nimport { getTransitionVal } from './util';\nvar defaultProps = {\n  begin: 0,\n  duration: 1000,\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  onAnimationEnd: () => {},\n  onAnimationStart: () => {}\n};\nexport function CSSTransitionAnimate(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultProps);\n  var {\n    from,\n    to,\n    attributeName,\n    isActive,\n    canBegin,\n    duration,\n    easing,\n    begin,\n    onAnimationEnd,\n    onAnimationStart,\n    children\n  } = props;\n  var animationManager = useAnimationManager(attributeName, props.animationManager);\n  var [style, setStyle] = useState(isActive ? from : to);\n  useEffect(() => {\n    if (!isActive) {\n      setStyle(to);\n    }\n  }, [isActive, to]);\n  useEffect(() => {\n    if (!isActive || !canBegin) {\n      return noop;\n    }\n    var unsubscribe = animationManager.subscribe(setStyle);\n    animationManager.start([onAnimationStart, begin, to, duration, onAnimationEnd]);\n    return () => {\n      animationManager.stop();\n      if (unsubscribe) {\n        unsubscribe();\n      }\n      onAnimationEnd();\n    };\n  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager, to]);\n  if (isActive && canBegin) {\n    var transition = getTransitionVal([attributeName], duration, easing);\n    return children({\n      transition,\n      [attributeName]: style\n    });\n  }\n  return children({\n    [attributeName]: style\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,gBAAgB,QAAQ,QAAQ;AACzC,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;EACxBC,gBAAgB,EAAEA,CAAA,KAAM,CAAC;AAC3B,CAAC;AACD,OAAO,SAASC,oBAAoBA,CAACC,YAAY,EAAE;EACjD,IAAIC,KAAK,GAAGb,mBAAmB,CAACY,YAAY,EAAET,YAAY,CAAC;EAC3D,IAAI;IACFW,IAAI;IACJC,EAAE;IACFC,aAAa;IACbT,QAAQ;IACRC,QAAQ;IACRH,QAAQ;IACRC,MAAM;IACNF,KAAK;IACLK,cAAc;IACdC,gBAAgB;IAChBO;EACF,CAAC,GAAGJ,KAAK;EACT,IAAIK,gBAAgB,GAAGjB,mBAAmB,CAACe,aAAa,EAAEH,KAAK,CAACK,gBAAgB,CAAC;EACjF,IAAI,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAACS,QAAQ,GAAGO,IAAI,GAAGC,EAAE,CAAC;EACtDlB,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,QAAQ,EAAE;MACba,QAAQ,CAACL,EAAE,CAAC;IACd;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEQ,EAAE,CAAC,CAAC;EAClBlB,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,QAAQ,IAAI,CAACC,QAAQ,EAAE;MAC1B,OAAOT,IAAI;IACb;IACA,IAAIsB,WAAW,GAAGH,gBAAgB,CAACI,SAAS,CAACF,QAAQ,CAAC;IACtDF,gBAAgB,CAACK,KAAK,CAAC,CAACb,gBAAgB,EAAEN,KAAK,EAAEW,EAAE,EAAEV,QAAQ,EAAEI,cAAc,CAAC,CAAC;IAC/E,OAAO,MAAM;MACXS,gBAAgB,CAACM,IAAI,CAAC,CAAC;MACvB,IAAIH,WAAW,EAAE;QACfA,WAAW,CAAC,CAAC;MACf;MACAZ,cAAc,CAAC,CAAC;IAClB,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,EAAEC,QAAQ,EAAEH,QAAQ,EAAEC,MAAM,EAAEF,KAAK,EAAEM,gBAAgB,EAAED,cAAc,EAAES,gBAAgB,EAAEH,EAAE,CAAC,CAAC;EACzG,IAAIR,QAAQ,IAAIC,QAAQ,EAAE;IACxB,IAAIiB,UAAU,GAAGvB,gBAAgB,CAAC,CAACc,aAAa,CAAC,EAAEX,QAAQ,EAAEC,MAAM,CAAC;IACpE,OAAOW,QAAQ,CAAC;MACdQ,UAAU;MACV,CAACT,aAAa,GAAGG;IACnB,CAAC,CAAC;EACJ;EACA,OAAOF,QAAQ,CAAC;IACd,CAACD,aAAa,GAAGG;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
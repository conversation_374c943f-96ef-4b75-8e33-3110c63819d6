{"ast": null, "code": "import { capitalize } from './capitalize.mjs';\nimport { words } from './words.mjs';\nfunction camelCase(str) {\n  const words$1 = words(str);\n  if (words$1.length === 0) {\n    return '';\n  }\n  const [first, ...rest] = words$1;\n  return `${first.toLowerCase()}${rest.map(word => capitalize(word)).join('')}`;\n}\nexport { camelCase };", "map": {"version": 3, "names": ["capitalize", "words", "camelCase", "str", "words$1", "length", "first", "rest", "toLowerCase", "map", "word", "join"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/camelCase.mjs"], "sourcesContent": ["import { capitalize } from './capitalize.mjs';\nimport { words } from './words.mjs';\n\nfunction camelCase(str) {\n    const words$1 = words(str);\n    if (words$1.length === 0) {\n        return '';\n    }\n    const [first, ...rest] = words$1;\n    return `${first.toLowerCase()}${rest.map(word => capitalize(word)).join('')}`;\n}\n\nexport { camelCase };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,IAAIC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,EAAE;EACb;EACA,MAAM,CAACC,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGH,OAAO;EAChC,OAAO,GAAGE,KAAK,CAACE,WAAW,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACC,IAAI,IAAIV,UAAU,CAACU,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,EAAE;AACjF;AAEA,SAAST,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
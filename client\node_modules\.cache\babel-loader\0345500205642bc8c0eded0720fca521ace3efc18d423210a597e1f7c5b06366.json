{"ast": null, "code": "function pick(obj, keys) {\n  const result = {};\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if (Object.hasOwn(obj, key)) {\n      result[key] = obj[key];\n    }\n  }\n  return result;\n}\nexport { pick };", "map": {"version": 3, "names": ["pick", "obj", "keys", "result", "i", "length", "key", "Object", "hasOwn"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/pick.mjs"], "sourcesContent": ["function pick(obj, keys) {\n    const result = {};\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        if (Object.hasOwn(obj, key)) {\n            result[key] = obj[key];\n        }\n    }\n    return result;\n}\n\nexport { pick };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACrB,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,IAAIG,MAAM,CAACC,MAAM,CAACP,GAAG,EAAEK,GAAG,CAAC,EAAE;MACzBH,MAAM,CAACG,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC;IAC1B;EACJ;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
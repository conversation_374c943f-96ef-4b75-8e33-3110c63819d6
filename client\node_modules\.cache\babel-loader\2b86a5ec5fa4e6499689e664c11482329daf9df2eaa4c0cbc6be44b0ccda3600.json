{"ast": null, "code": "import { cloneDeep } from './cloneDeep.mjs';\nimport { merge } from './merge.mjs';\nfunction toMerged(target, source) {\n  return merge(cloneDeep(target), source);\n}\nexport { toMerged };", "map": {"version": 3, "names": ["cloneDeep", "merge", "toMerged", "target", "source"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/toMerged.mjs"], "sourcesContent": ["import { cloneDeep } from './cloneDeep.mjs';\nimport { merge } from './merge.mjs';\n\nfunction toMerged(target, source) {\n    return merge(cloneDeep(target), source);\n}\n\nexport { toMerged };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC9B,OAAOH,KAAK,CAACD,SAAS,CAACG,MAAM,CAAC,EAAEC,MAAM,CAAC;AAC3C;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as React from 'react';\nimport { createContext, useContext } from 'react';\nimport { useUniqueId } from '../util/useUniqueId';\nvar GraphicalItemIdContext = /*#__PURE__*/createContext(undefined);\nexport var RegisterGraphicalItemId = _ref => {\n  var {\n    id,\n    type,\n    children\n  } = _ref;\n  var resolvedId = useUniqueId(\"recharts-\".concat(type), id);\n  return /*#__PURE__*/React.createElement(GraphicalItemIdContext.Provider, {\n    value: resolvedId\n  }, children(resolvedId));\n};\nexport function useGraphicalItemId() {\n  return useContext(GraphicalItemIdContext);\n}", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useUniqueId", "GraphicalItemIdContext", "undefined", "RegisterGraphicalItemId", "_ref", "id", "type", "children", "resolvedId", "concat", "createElement", "Provider", "value", "useGraphicalItemId"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/context/RegisterGraphicalItemId.js"], "sourcesContent": ["import * as React from 'react';\nimport { createContext, useContext } from 'react';\nimport { useUniqueId } from '../util/useUniqueId';\nvar GraphicalItemIdContext = /*#__PURE__*/createContext(undefined);\nexport var RegisterGraphicalItemId = _ref => {\n  var {\n    id,\n    type,\n    children\n  } = _ref;\n  var resolvedId = useUniqueId(\"recharts-\".concat(type), id);\n  return /*#__PURE__*/React.createElement(GraphicalItemIdContext.Provider, {\n    value: resolvedId\n  }, children(resolvedId));\n};\nexport function useGraphicalItemId() {\n  return useContext(GraphicalItemIdContext);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,IAAIC,sBAAsB,GAAG,aAAaH,aAAa,CAACI,SAAS,CAAC;AAClE,OAAO,IAAIC,uBAAuB,GAAGC,IAAI,IAAI;EAC3C,IAAI;IACFC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAGH,IAAI;EACR,IAAII,UAAU,GAAGR,WAAW,CAAC,WAAW,CAACS,MAAM,CAACH,IAAI,CAAC,EAAED,EAAE,CAAC;EAC1D,OAAO,aAAaR,KAAK,CAACa,aAAa,CAACT,sBAAsB,CAACU,QAAQ,EAAE;IACvEC,KAAK,EAAEJ;EACT,CAAC,EAAED,QAAQ,CAACC,UAAU,CAAC,CAAC;AAC1B,CAAC;AACD,OAAO,SAASK,kBAAkBA,CAAA,EAAG;EACnC,OAAOd,UAAU,CAACE,sBAAsB,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
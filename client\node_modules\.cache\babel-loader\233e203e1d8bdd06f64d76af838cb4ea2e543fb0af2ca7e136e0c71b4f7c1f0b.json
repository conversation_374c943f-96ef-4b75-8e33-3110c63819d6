{"ast": null, "code": "var _excluded = [\"option\", \"shapeType\", \"propTransformer\", \"activeClassName\", \"isActive\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from 'react';\nimport isPlainObject from 'es-toolkit/compat/isPlainObject';\nimport { Rectangle } from '../shape/Rectangle';\nimport { Trapezoid } from '../shape/Trapezoid';\nimport { Sector } from '../shape/Sector';\nimport { Layer } from '../container/Layer';\nimport { Symbols } from '../shape/Symbols';\n\n/**\n * This is an abstraction for rendering a user defined prop for a customized shape in several forms.\n *\n * <Shape /> is the root and will handle taking in:\n *  - an object of svg properties\n *  - a boolean\n *  - a render prop(inline function that returns jsx)\n *  - a React element\n *\n * <ShapeSelector /> is a subcomponent of <Shape /> and used to match a component\n * to the value of props.shapeType that is passed to the root.\n *\n */\n\nfunction defaultPropTransformer(option, props) {\n  return _objectSpread(_objectSpread({}, props), option);\n}\nfunction isSymbolsProps(shapeType, _elementProps) {\n  return shapeType === 'symbols';\n}\nfunction ShapeSelector(_ref) {\n  var {\n    shapeType,\n    elementProps\n  } = _ref;\n  switch (shapeType) {\n    case 'rectangle':\n      return /*#__PURE__*/React.createElement(Rectangle, elementProps);\n    case 'trapezoid':\n      return /*#__PURE__*/React.createElement(Trapezoid, elementProps);\n    case 'sector':\n      return /*#__PURE__*/React.createElement(Sector, elementProps);\n    case 'symbols':\n      if (isSymbolsProps(shapeType, elementProps)) {\n        return /*#__PURE__*/React.createElement(Symbols, elementProps);\n      }\n      break;\n    default:\n      return null;\n  }\n}\nexport function getPropsFromShapeOption(option) {\n  if (/*#__PURE__*/isValidElement(option)) {\n    return option.props;\n  }\n  return option;\n}\nexport function Shape(_ref2) {\n  var {\n      option,\n      shapeType,\n      propTransformer = defaultPropTransformer,\n      activeClassName = 'recharts-active-shape',\n      isActive\n    } = _ref2,\n    props = _objectWithoutProperties(_ref2, _excluded);\n  var shape;\n  if (/*#__PURE__*/isValidElement(option)) {\n    shape = /*#__PURE__*/cloneElement(option, _objectSpread(_objectSpread({}, props), getPropsFromShapeOption(option)));\n  } else if (typeof option === 'function') {\n    shape = option(props);\n  } else if (isPlainObject(option) && typeof option !== 'boolean') {\n    var nextProps = propTransformer(option, props);\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: nextProps\n    });\n  } else {\n    var elementProps = props;\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: elementProps\n    });\n  }\n  if (isActive) {\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: activeClassName\n    }, shape);\n  }\n  return shape;\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "cloneElement", "isValidElement", "isPlainObject", "Rectangle", "Trapezoid", "Sector", "Layer", "Symbols", "defaultPropTransformer", "option", "props", "isSymbolsProps", "shapeType", "_elementProps", "ShapeSelector", "_ref", "elementProps", "createElement", "getPropsFromShapeOption", "<PERSON><PERSON><PERSON>", "_ref2", "propTransformer", "activeClassName", "isActive", "shape", "nextProps", "className"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/ActiveShapeUtils.js"], "sourcesContent": ["var _excluded = [\"option\", \"shapeType\", \"propTransformer\", \"activeClassName\", \"isActive\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from 'react';\nimport isPlainObject from 'es-toolkit/compat/isPlainObject';\nimport { Rectangle } from '../shape/Rectangle';\nimport { Trapezoid } from '../shape/Trapezoid';\nimport { Sector } from '../shape/Sector';\nimport { Layer } from '../container/Layer';\nimport { Symbols } from '../shape/Symbols';\n\n/**\n * This is an abstraction for rendering a user defined prop for a customized shape in several forms.\n *\n * <Shape /> is the root and will handle taking in:\n *  - an object of svg properties\n *  - a boolean\n *  - a render prop(inline function that returns jsx)\n *  - a React element\n *\n * <ShapeSelector /> is a subcomponent of <Shape /> and used to match a component\n * to the value of props.shapeType that is passed to the root.\n *\n */\n\nfunction defaultPropTransformer(option, props) {\n  return _objectSpread(_objectSpread({}, props), option);\n}\nfunction isSymbolsProps(shapeType, _elementProps) {\n  return shapeType === 'symbols';\n}\nfunction ShapeSelector(_ref) {\n  var {\n    shapeType,\n    elementProps\n  } = _ref;\n  switch (shapeType) {\n    case 'rectangle':\n      return /*#__PURE__*/React.createElement(Rectangle, elementProps);\n    case 'trapezoid':\n      return /*#__PURE__*/React.createElement(Trapezoid, elementProps);\n    case 'sector':\n      return /*#__PURE__*/React.createElement(Sector, elementProps);\n    case 'symbols':\n      if (isSymbolsProps(shapeType, elementProps)) {\n        return /*#__PURE__*/React.createElement(Symbols, elementProps);\n      }\n      break;\n    default:\n      return null;\n  }\n}\nexport function getPropsFromShapeOption(option) {\n  if (/*#__PURE__*/isValidElement(option)) {\n    return option.props;\n  }\n  return option;\n}\nexport function Shape(_ref2) {\n  var {\n      option,\n      shapeType,\n      propTransformer = defaultPropTransformer,\n      activeClassName = 'recharts-active-shape',\n      isActive\n    } = _ref2,\n    props = _objectWithoutProperties(_ref2, _excluded);\n  var shape;\n  if (/*#__PURE__*/isValidElement(option)) {\n    shape = /*#__PURE__*/cloneElement(option, _objectSpread(_objectSpread({}, props), getPropsFromShapeOption(option)));\n  } else if (typeof option === 'function') {\n    shape = option(props);\n  } else if (isPlainObject(option) && typeof option !== 'boolean') {\n    var nextProps = propTransformer(option, props);\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: nextProps\n    });\n  } else {\n    var elementProps = props;\n    shape = /*#__PURE__*/React.createElement(ShapeSelector, {\n      shapeType: shapeType,\n      elementProps: elementProps\n    });\n  }\n  if (isActive) {\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: activeClassName\n    }, shape);\n  }\n  return shape;\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,UAAU,CAAC;AACzF,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKqC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,cAAc,QAAQ,OAAO;AACpD,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC7C,OAAO5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAAC,EAAED,MAAM,CAAC;AACxD;AACA,SAASE,cAAcA,CAACC,SAAS,EAAEC,aAAa,EAAE;EAChD,OAAOD,SAAS,KAAK,SAAS;AAChC;AACA,SAASE,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAI;IACFH,SAAS;IACTI;EACF,CAAC,GAAGD,IAAI;EACR,QAAQH,SAAS;IACf,KAAK,WAAW;MACd,OAAO,aAAab,KAAK,CAACkB,aAAa,CAACd,SAAS,EAAEa,YAAY,CAAC;IAClE,KAAK,WAAW;MACd,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACb,SAAS,EAAEY,YAAY,CAAC;IAClE,KAAK,QAAQ;MACX,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACZ,MAAM,EAAEW,YAAY,CAAC;IAC/D,KAAK,SAAS;MACZ,IAAIL,cAAc,CAACC,SAAS,EAAEI,YAAY,CAAC,EAAE;QAC3C,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACV,OAAO,EAAES,YAAY,CAAC;MAChE;MACA;IACF;MACE,OAAO,IAAI;EACf;AACF;AACA,OAAO,SAASE,uBAAuBA,CAACT,MAAM,EAAE;EAC9C,IAAI,aAAaR,cAAc,CAACQ,MAAM,CAAC,EAAE;IACvC,OAAOA,MAAM,CAACC,KAAK;EACrB;EACA,OAAOD,MAAM;AACf;AACA,OAAO,SAASU,KAAKA,CAACC,KAAK,EAAE;EAC3B,IAAI;MACAX,MAAM;MACNG,SAAS;MACTS,eAAe,GAAGb,sBAAsB;MACxCc,eAAe,GAAG,uBAAuB;MACzCC;IACF,CAAC,GAAGH,KAAK;IACTV,KAAK,GAAGlD,wBAAwB,CAAC4D,KAAK,EAAE7D,SAAS,CAAC;EACpD,IAAIiE,KAAK;EACT,IAAI,aAAavB,cAAc,CAACQ,MAAM,CAAC,EAAE;IACvCe,KAAK,GAAG,aAAaxB,YAAY,CAACS,MAAM,EAAE3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAAC,EAAEQ,uBAAuB,CAACT,MAAM,CAAC,CAAC,CAAC;EACrH,CAAC,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IACvCe,KAAK,GAAGf,MAAM,CAACC,KAAK,CAAC;EACvB,CAAC,MAAM,IAAIR,aAAa,CAACO,MAAM,CAAC,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;IAC/D,IAAIgB,SAAS,GAAGJ,eAAe,CAACZ,MAAM,EAAEC,KAAK,CAAC;IAC9Cc,KAAK,GAAG,aAAazB,KAAK,CAACkB,aAAa,CAACH,aAAa,EAAE;MACtDF,SAAS,EAAEA,SAAS;MACpBI,YAAY,EAAES;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAIT,YAAY,GAAGN,KAAK;IACxBc,KAAK,GAAG,aAAazB,KAAK,CAACkB,aAAa,CAACH,aAAa,EAAE;MACtDF,SAAS,EAAEA,SAAS;MACpBI,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ;EACA,IAAIO,QAAQ,EAAE;IACZ,OAAO,aAAaxB,KAAK,CAACkB,aAAa,CAACX,KAAK,EAAE;MAC7CoB,SAAS,EAAEJ;IACb,CAAC,EAAEE,KAAK,CAAC;EACX;EACA,OAAOA,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
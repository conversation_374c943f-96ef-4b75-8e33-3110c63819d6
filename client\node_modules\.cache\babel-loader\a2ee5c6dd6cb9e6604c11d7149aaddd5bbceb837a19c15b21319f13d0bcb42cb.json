{"ast": null, "code": "function median(nums) {\n  if (nums.length === 0) {\n    return NaN;\n  }\n  const sorted = nums.slice().sort((a, b) => a - b);\n  const middleIndex = Math.floor(sorted.length / 2);\n  if (sorted.length % 2 === 0) {\n    return (sorted[middleIndex - 1] + sorted[middleIndex]) / 2;\n  } else {\n    return sorted[middleIndex];\n  }\n}\nexport { median };", "map": {"version": 3, "names": ["median", "nums", "length", "NaN", "sorted", "slice", "sort", "a", "b", "middleIndex", "Math", "floor"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/math/median.mjs"], "sourcesContent": ["function median(nums) {\n    if (nums.length === 0) {\n        return NaN;\n    }\n    const sorted = nums.slice().sort((a, b) => a - b);\n    const middleIndex = Math.floor(sorted.length / 2);\n    if (sorted.length % 2 === 0) {\n        return (sorted[middleIndex - 1] + sorted[middleIndex]) / 2;\n    }\n    else {\n        return sorted[middleIndex];\n    }\n}\n\nexport { median };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,IAAI,EAAE;EAClB,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACnB,OAAOC,GAAG;EACd;EACA,MAAMC,MAAM,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACjD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACP,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;EACjD,IAAIE,MAAM,CAACF,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACzB,OAAO,CAACE,MAAM,CAACK,WAAW,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACK,WAAW,CAAC,IAAI,CAAC;EAC9D,CAAC,MACI;IACD,OAAOL,MAAM,CAACK,WAAW,CAAC;EAC9B;AACJ;AAEA,SAAST,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
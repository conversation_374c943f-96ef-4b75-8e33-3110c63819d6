{"ast": null, "code": "import { isSymbol } from '../predicate/isSymbol.mjs';\nfunction toNumber(value) {\n  if (isSymbol(value)) {\n    return NaN;\n  }\n  return Number(value);\n}\nexport { toNumber };", "map": {"version": 3, "names": ["isSymbol", "toNumber", "value", "NaN", "Number"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/util/toNumber.mjs"], "sourcesContent": ["import { isSymbol } from '../predicate/isSymbol.mjs';\n\nfunction toNumber(value) {\n    if (isSymbol(value)) {\n        return NaN;\n    }\n    return Number(value);\n}\n\nexport { toNumber };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,2BAA2B;AAEpD,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,IAAIF,QAAQ,CAACE,KAAK,CAAC,EAAE;IACjB,OAAOC,GAAG;EACd;EACA,OAAOC,MAAM,CAACF,KAAK,CAAC;AACxB;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
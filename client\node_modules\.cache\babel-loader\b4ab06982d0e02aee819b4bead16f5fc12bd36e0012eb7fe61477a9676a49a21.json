{"ast": null, "code": "function Linear(context) {\n  this._context = context;\n}\nLinear.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // falls through\n      default:\n        this._context.lineTo(x, y);\n        break;\n    }\n  }\n};\nexport default function (context) {\n  return new Linear(context);\n}", "map": {"version": 3, "names": ["Linear", "context", "_context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_point", "lineEnd", "closePath", "point", "x", "y", "lineTo", "moveTo"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/d3-shape/src/curve/linear.js"], "sourcesContent": ["function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,OAAO,EAAE;EACvB,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,MAAM,CAACG,SAAS,GAAG;EACjBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACL,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,KAAK,CAAE,EAAE,IAAI,CAACP,QAAQ,CAACS,SAAS,CAAC,CAAC;IACpF,IAAI,CAACN,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDO,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACL,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACa,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACZ,QAAQ,CAACc,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;QAAE;MAC/F,KAAK,CAAC;QAAE,IAAI,CAACL,MAAM,GAAG,CAAC;MAAE;MACzB;QAAS,IAAI,CAACP,QAAQ,CAACa,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;QAAE;IACvC;EACF;AACF,CAAC;AAED,eAAe,UAASb,OAAO,EAAE;EAC/B,OAAO,IAAID,MAAM,CAACC,OAAO,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
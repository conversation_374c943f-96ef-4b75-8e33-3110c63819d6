{"ast": null, "code": "var _jsxFileName = \"C:\\\\vibe coding\\\\screentime_management_app\\\\client\\\\src\\\\pages\\\\Friends.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, UserPlus, Search, Check, X, UserMinus } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Friends = () => {\n  _s();\n  const [friends, setFriends] = useState([]);\n  const [pendingRequests, setPendingRequests] = useState([]);\n  const [searchResults, setSearchResults] = useState([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchFriends();\n  }, []);\n  useEffect(() => {\n    const delayedSearch = setTimeout(() => {\n      if (searchQuery.length >= 2) {\n        searchUsers();\n      } else {\n        setSearchResults([]);\n      }\n    }, 300);\n    return () => clearTimeout(delayedSearch);\n  }, [searchQuery]);\n  const fetchFriends = async () => {\n    try {\n      const response = await axios.get('/friends');\n      setFriends(response.data.friends);\n      setPendingRequests(response.data.pendingRequests);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to fetch friends');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const searchUsers = async () => {\n    setSearchLoading(true);\n    try {\n      const response = await axios.get(`/friends/search?q=${encodeURIComponent(searchQuery)}`);\n      setSearchResults(response.data.users);\n    } catch (err) {\n      console.error('Search failed:', err);\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n  const sendFriendRequest = async username => {\n    try {\n      await axios.post('/friends/request', {\n        username\n      });\n      // Update search results to reflect new status\n      setSearchResults(prev => prev.map(user => user.username === username ? {\n        ...user,\n        relationshipStatus: 'pending'\n      } : user));\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      alert(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to send friend request');\n    }\n  };\n  const acceptFriendRequest = async friendId => {\n    try {\n      await axios.put(`/friends/${friendId}/accept`);\n      fetchFriends(); // Refresh the lists\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      alert(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to accept friend request');\n    }\n  };\n  const rejectFriendRequest = async friendId => {\n    try {\n      await axios.put(`/friends/${friendId}/reject`);\n      fetchFriends(); // Refresh the lists\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      alert(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to reject friend request');\n    }\n  };\n  const removeFriend = async friendId => {\n    if (window.confirm('Are you sure you want to remove this friend?')) {\n      try {\n        await axios.delete(`/friends/${friendId}`);\n        fetchFriends(); // Refresh the lists\n      } catch (err) {\n        var _err$response5, _err$response5$data;\n        alert(((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.message) || 'Failed to remove friend');\n      }\n    }\n  };\n  const getStatusButton = user => {\n    switch (user.relationshipStatus) {\n      case 'none':\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => sendFriendRequest(user.username),\n          className: \"btn-primary text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n            className: \"h-4 w-4 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), \"Add Friend\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this);\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-md\",\n          children: \"Request Sent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this);\n      case 'accepted':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-success-600 bg-success-100 px-3 py-1 rounded-md\",\n          children: \"Friends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this);\n      case 'blocked':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-danger-600 bg-danger-100 px-3 py-1 rounded-md\",\n          children: \"Blocked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"Friends\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Connect with friends and compete together\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Find Friends\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            className: \"h-5 w-5 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          className: \"input pl-10\",\n          placeholder: \"Search by username or display name...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), searchLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 space-y-2\",\n        children: searchResults.map(user => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: user.displayName.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: user.displayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"@\", user.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this), getStatusButton(user)]\n        }, user._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), pendingRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Pending Requests\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: pendingRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: request.user.displayName.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: request.user.displayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"@\", request.user.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => acceptFriendRequest(request.user._id),\n              className: \"btn-success text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(Check, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), \"Accept\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => rejectFriendRequest(request.user._id),\n              className: \"btn-secondary text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this), \"Decline\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 17\n          }, this)]\n        }, request._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: [\"Your Friends (\", friends.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Users, {\n          className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), friends.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: friends.map(friend => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: friend.user.displayName.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: friend.user.displayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"@\", friend.user.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: [\"Friends since \", new Date(friend.addedAt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => removeFriend(friend.user._id),\n            className: \"btn-danger text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(UserMinus, {\n              className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this), \"Remove\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this)]\n        }, friend._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"h-12 w-12 mx-auto text-gray-300 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No friends yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mt-2\",\n          children: \"Search for friends above to start building your network!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n};\n_s(Friends, \"BhZj94OFDSbJwpmhfFt53qDz9zg=\");\n_c = Friends;\nexport default Friends;\nvar _c;\n$RefreshReg$(_c, \"Friends\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Users", "UserPlus", "Search", "Check", "X", "UserMinus", "jsxDEV", "_jsxDEV", "Friends", "_s", "friends", "setFriends", "pendingRequests", "setPendingRequests", "searchResults", "setSearchResults", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "searchLoading", "setSearchLoading", "error", "setError", "fetchFriends", "delayedSearch", "setTimeout", "length", "searchUsers", "clearTimeout", "response", "get", "data", "err", "_err$response", "_err$response$data", "message", "encodeURIComponent", "users", "console", "sendFriendRequest", "username", "post", "prev", "map", "user", "relationshipStatus", "_err$response2", "_err$response2$data", "alert", "acceptFriendRequest", "friendId", "put", "_err$response3", "_err$response3$data", "rejectFriendRequest", "_err$response4", "_err$response4$data", "removeFriend", "window", "confirm", "delete", "_err$response5", "_err$response5$data", "getStatusButton", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "displayName", "char<PERSON>t", "toUpperCase", "_id", "request", "friend", "Date", "addedAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/vibe coding/screentime_management_app/client/src/pages/Friends.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Users, UserPlus, Search, Check, X, UserMinus } from 'lucide-react';\n\ninterface Friend {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    displayName: string;\n    avatar?: string;\n  };\n  status: 'accepted' | 'pending' | 'blocked';\n  addedAt: string;\n}\n\ninterface SearchResult {\n  _id: string;\n  username: string;\n  displayName: string;\n  avatar?: string;\n  relationshipStatus: 'none' | 'pending' | 'accepted' | 'blocked';\n}\n\nconst Friends: React.FC = () => {\n  const [friends, setFriends] = useState<Friend[]>([]);\n  const [pendingRequests, setPendingRequests] = useState<Friend[]>([]);\n  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchFriends();\n  }, []);\n\n  useEffect(() => {\n    const delayedSearch = setTimeout(() => {\n      if (searchQuery.length >= 2) {\n        searchUsers();\n      } else {\n        setSearchResults([]);\n      }\n    }, 300);\n\n    return () => clearTimeout(delayedSearch);\n  }, [searchQuery]);\n\n  const fetchFriends = async () => {\n    try {\n      const response = await axios.get('/friends');\n      setFriends(response.data.friends);\n      setPendingRequests(response.data.pendingRequests);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to fetch friends');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const searchUsers = async () => {\n    setSearchLoading(true);\n    try {\n      const response = await axios.get(`/friends/search?q=${encodeURIComponent(searchQuery)}`);\n      setSearchResults(response.data.users);\n    } catch (err: any) {\n      console.error('Search failed:', err);\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n\n  const sendFriendRequest = async (username: string) => {\n    try {\n      await axios.post('/friends/request', { username });\n      // Update search results to reflect new status\n      setSearchResults(prev => \n        prev.map(user => \n          user.username === username \n            ? { ...user, relationshipStatus: 'pending' }\n            : user\n        )\n      );\n    } catch (err: any) {\n      alert(err.response?.data?.message || 'Failed to send friend request');\n    }\n  };\n\n  const acceptFriendRequest = async (friendId: string) => {\n    try {\n      await axios.put(`/friends/${friendId}/accept`);\n      fetchFriends(); // Refresh the lists\n    } catch (err: any) {\n      alert(err.response?.data?.message || 'Failed to accept friend request');\n    }\n  };\n\n  const rejectFriendRequest = async (friendId: string) => {\n    try {\n      await axios.put(`/friends/${friendId}/reject`);\n      fetchFriends(); // Refresh the lists\n    } catch (err: any) {\n      alert(err.response?.data?.message || 'Failed to reject friend request');\n    }\n  };\n\n  const removeFriend = async (friendId: string) => {\n    if (window.confirm('Are you sure you want to remove this friend?')) {\n      try {\n        await axios.delete(`/friends/${friendId}`);\n        fetchFriends(); // Refresh the lists\n      } catch (err: any) {\n        alert(err.response?.data?.message || 'Failed to remove friend');\n      }\n    }\n  };\n\n  const getStatusButton = (user: SearchResult) => {\n    switch (user.relationshipStatus) {\n      case 'none':\n        return (\n          <button\n            onClick={() => sendFriendRequest(user.username)}\n            className=\"btn-primary text-sm\"\n          >\n            <UserPlus className=\"h-4 w-4 mr-1\" />\n            Add Friend\n          </button>\n        );\n      case 'pending':\n        return (\n          <span className=\"text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-md\">\n            Request Sent\n          </span>\n        );\n      case 'accepted':\n        return (\n          <span className=\"text-sm text-success-600 bg-success-100 px-3 py-1 rounded-md\">\n            Friends\n          </span>\n        );\n      case 'blocked':\n        return (\n          <span className=\"text-sm text-danger-600 bg-danger-100 px-3 py-1 rounded-md\">\n            Blocked\n          </span>\n        );\n      default:\n        return null;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Friends</h1>\n        <p className=\"text-gray-600\">Connect with friends and compete together</p>\n      </div>\n\n      {/* Search */}\n      <div className=\"card\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Find Friends</h2>\n        <div className=\"relative\">\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <Search className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"input pl-10\"\n            placeholder=\"Search by username or display name...\"\n          />\n        </div>\n\n        {searchLoading && (\n          <div className=\"mt-4 text-center\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto\"></div>\n          </div>\n        )}\n\n        {searchResults.length > 0 && (\n          <div className=\"mt-4 space-y-2\">\n            {searchResults.map((user) => (\n              <div key={user._id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {user.displayName.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">{user.displayName}</p>\n                    <p className=\"text-sm text-gray-500\">@{user.username}</p>\n                  </div>\n                </div>\n                {getStatusButton(user)}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Pending Requests */}\n      {pendingRequests.length > 0 && (\n        <div className=\"card\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Pending Requests</h2>\n          <div className=\"space-y-3\">\n            {pendingRequests.map((request) => (\n              <div key={request._id} className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {request.user.displayName.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">{request.user.displayName}</p>\n                    <p className=\"text-sm text-gray-500\">@{request.user.username}</p>\n                  </div>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => acceptFriendRequest(request.user._id)}\n                    className=\"btn-success text-sm\"\n                  >\n                    <Check className=\"h-4 w-4 mr-1\" />\n                    Accept\n                  </button>\n                  <button\n                    onClick={() => rejectFriendRequest(request.user._id)}\n                    className=\"btn-secondary text-sm\"\n                  >\n                    <X className=\"h-4 w-4 mr-1\" />\n                    Decline\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Friends List */}\n      <div className=\"card\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">\n            Your Friends ({friends.length})\n          </h2>\n          <Users className=\"h-5 w-5 text-gray-400\" />\n        </div>\n\n        {friends.length > 0 ? (\n          <div className=\"space-y-3\">\n            {friends.map((friend) => (\n              <div key={friend._id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {friend.user.displayName.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">{friend.user.displayName}</p>\n                    <p className=\"text-sm text-gray-500\">@{friend.user.username}</p>\n                    <p className=\"text-xs text-gray-400\">\n                      Friends since {new Date(friend.addedAt).toLocaleDateString()}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => removeFriend(friend.user._id)}\n                  className=\"btn-danger text-sm\"\n                >\n                  <UserMinus className=\"h-4 w-4 mr-1\" />\n                  Remove\n                </button>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <Users className=\"h-12 w-12 mx-auto text-gray-300 mb-4\" />\n            <p className=\"text-gray-500\">No friends yet</p>\n            <p className=\"text-sm text-gray-400 mt-2\">\n              Search for friends above to start building your network!\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Friends;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,CAAC,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB5E,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;EACpE,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN1B,SAAS,CAAC,MAAM;IACd,MAAM2B,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrC,IAAIV,WAAW,CAACW,MAAM,IAAI,CAAC,EAAE;QAC3BC,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLb,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMc,YAAY,CAACJ,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;EAEjB,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,UAAU,CAAC;MAC5CpB,UAAU,CAACmB,QAAQ,CAACE,IAAI,CAACtB,OAAO,CAAC;MACjCG,kBAAkB,CAACiB,QAAQ,CAACE,IAAI,CAACpB,eAAe,CAAC;IACnD,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBZ,QAAQ,CAAC,EAAAW,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,yBAAyB,CAAC;IACpE,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BP,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,qBAAqBM,kBAAkB,CAACrB,WAAW,CAAC,EAAE,CAAC;MACxFD,gBAAgB,CAACe,QAAQ,CAACE,IAAI,CAACM,KAAK,CAAC;IACvC,CAAC,CAAC,OAAOL,GAAQ,EAAE;MACjBM,OAAO,CAACjB,KAAK,CAAC,gBAAgB,EAAEW,GAAG,CAAC;IACtC,CAAC,SAAS;MACRZ,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmB,iBAAiB,GAAG,MAAOC,QAAgB,IAAK;IACpD,IAAI;MACF,MAAM1C,KAAK,CAAC2C,IAAI,CAAC,kBAAkB,EAAE;QAAED;MAAS,CAAC,CAAC;MAClD;MACA1B,gBAAgB,CAAC4B,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACC,IAAI,IACXA,IAAI,CAACJ,QAAQ,KAAKA,QAAQ,GACtB;QAAE,GAAGI,IAAI;QAAEC,kBAAkB,EAAE;MAAU,CAAC,GAC1CD,IACN,CACF,CAAC;IACH,CAAC,CAAC,OAAOZ,GAAQ,EAAE;MAAA,IAAAc,cAAA,EAAAC,mBAAA;MACjBC,KAAK,CAAC,EAAAF,cAAA,GAAAd,GAAG,CAACH,QAAQ,cAAAiB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcf,IAAI,cAAAgB,mBAAA,uBAAlBA,mBAAA,CAAoBZ,OAAO,KAAI,+BAA+B,CAAC;IACvE;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAOC,QAAgB,IAAK;IACtD,IAAI;MACF,MAAMpD,KAAK,CAACqD,GAAG,CAAC,YAAYD,QAAQ,SAAS,CAAC;MAC9C3B,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOS,GAAQ,EAAE;MAAA,IAAAoB,cAAA,EAAAC,mBAAA;MACjBL,KAAK,CAAC,EAAAI,cAAA,GAAApB,GAAG,CAACH,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBlB,OAAO,KAAI,iCAAiC,CAAC;IACzE;EACF,CAAC;EAED,MAAMmB,mBAAmB,GAAG,MAAOJ,QAAgB,IAAK;IACtD,IAAI;MACF,MAAMpD,KAAK,CAACqD,GAAG,CAAC,YAAYD,QAAQ,SAAS,CAAC;MAC9C3B,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOS,GAAQ,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACjBR,KAAK,CAAC,EAAAO,cAAA,GAAAvB,GAAG,CAACH,QAAQ,cAAA0B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBrB,OAAO,KAAI,iCAAiC,CAAC;IACzE;EACF,CAAC;EAED,MAAMsB,YAAY,GAAG,MAAOP,QAAgB,IAAK;IAC/C,IAAIQ,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAClE,IAAI;QACF,MAAM7D,KAAK,CAAC8D,MAAM,CAAC,YAAYV,QAAQ,EAAE,CAAC;QAC1C3B,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOS,GAAQ,EAAE;QAAA,IAAA6B,cAAA,EAAAC,mBAAA;QACjBd,KAAK,CAAC,EAAAa,cAAA,GAAA7B,GAAG,CAACH,QAAQ,cAAAgC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoB3B,OAAO,KAAI,yBAAyB,CAAC;MACjE;IACF;EACF,CAAC;EAED,MAAM4B,eAAe,GAAInB,IAAkB,IAAK;IAC9C,QAAQA,IAAI,CAACC,kBAAkB;MAC7B,KAAK,MAAM;QACT,oBACEvC,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACK,IAAI,CAACJ,QAAQ,CAAE;UAChDyB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAE/B5D,OAAA,CAACN,QAAQ;YAACiE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAEb,KAAK,SAAS;QACZ,oBACEhE,OAAA;UAAM2D,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,UAAU;QACb,oBACEhE,OAAA;UAAM2D,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACEhE,OAAA;UAAM2D,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE7E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAIrD,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK2D,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxD5D,OAAA;QAAK2D,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5D,OAAA;MAAA4D,QAAA,gBACE5D,OAAA;QAAI2D,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DhE,OAAA;QAAG2D,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAyC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC,eAGNhE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAI2D,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EhE,OAAA;QAAK2D,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvB5D,OAAA;UAAK2D,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eACnF5D,OAAA,CAACL,MAAM;YAACgE,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNhE,OAAA;UACEiE,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEzD,WAAY;UACnB0D,QAAQ,EAAGC,CAAC,IAAK1D,cAAc,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDP,SAAS,EAAC,aAAa;UACvBW,WAAW,EAAC;QAAuC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnD,aAAa,iBACZb,OAAA;QAAK2D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B5D,OAAA;UAAK2D,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CACN,EAEAzD,aAAa,CAACa,MAAM,GAAG,CAAC,iBACvBpB,OAAA;QAAK2D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BrD,aAAa,CAAC8B,GAAG,CAAEC,IAAI,iBACtBtC,OAAA;UAAoB2D,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBACzF5D,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5D,OAAA;cAAK2D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF5D,OAAA;gBAAM2D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC7CtB,IAAI,CAACiC,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAG2D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEtB,IAAI,CAACiC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DhE,OAAA;gBAAG2D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAAC,EAACtB,IAAI,CAACJ,QAAQ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLP,eAAe,CAACnB,IAAI,CAAC;QAAA,GAZdA,IAAI,CAACoC,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAab,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL3D,eAAe,CAACe,MAAM,GAAG,CAAC,iBACzBpB,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAI2D,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9EhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBvD,eAAe,CAACgC,GAAG,CAAEsC,OAAO,iBAC3B3E,OAAA;UAAuB2D,SAAS,EAAC,wFAAwF;UAAAC,QAAA,gBACvH5D,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5D,OAAA;cAAK2D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF5D,OAAA;gBAAM2D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC7Ce,OAAO,CAACrC,IAAI,CAACiC,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAG2D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEe,OAAO,CAACrC,IAAI,CAACiC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEhE,OAAA;gBAAG2D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAAC,EAACe,OAAO,CAACrC,IAAI,CAACJ,QAAQ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YAAK2D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5D,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMf,mBAAmB,CAACgC,OAAO,CAACrC,IAAI,CAACoC,GAAG,CAAE;cACrDf,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAE/B5D,OAAA,CAACJ,KAAK;gBAAC+D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMV,mBAAmB,CAAC2B,OAAO,CAACrC,IAAI,CAACoC,GAAG,CAAE;cACrDf,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAEjC5D,OAAA,CAACH,CAAC;gBAAC8D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GA3BEW,OAAO,CAACD,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BhB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAK2D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5D,OAAA;UAAI2D,SAAS,EAAC,qCAAqC;UAAAC,QAAA,GAAC,gBACpC,EAACzD,OAAO,CAACiB,MAAM,EAAC,GAChC;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhE,OAAA,CAACP,KAAK;UAACkE,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAEL7D,OAAO,CAACiB,MAAM,GAAG,CAAC,gBACjBpB,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBzD,OAAO,CAACkC,GAAG,CAAEuC,MAAM,iBAClB5E,OAAA;UAAsB2D,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC3F5D,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5D,OAAA;cAAK2D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF5D,OAAA;gBAAM2D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC7CgB,MAAM,CAACtC,IAAI,CAACiC,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAG2D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEgB,MAAM,CAACtC,IAAI,CAACiC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEhE,OAAA;gBAAG2D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAAC,EAACgB,MAAM,CAACtC,IAAI,CAACJ,QAAQ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEhE,OAAA;gBAAG2D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,gBACrB,EAAC,IAAIiB,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhE,OAAA;YACE0D,OAAO,EAAEA,CAAA,KAAMP,YAAY,CAACyB,MAAM,CAACtC,IAAI,CAACoC,GAAG,CAAE;YAC7Cf,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAE9B5D,OAAA,CAACF,SAAS;cAAC6D,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GArBDY,MAAM,CAACF,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENhE,OAAA;QAAK2D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5D,OAAA,CAACP,KAAK;UAACkE,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DhE,OAAA;UAAG2D,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/ChE,OAAA;UAAG2D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAtRID,OAAiB;AAAA+E,EAAA,GAAjB/E,OAAiB;AAwRvB,eAAeA,OAAO;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { combineAxisTicks, combineCategoricalDomain, combineGraphicalItemTicks, combineScaleFunction, selectAxisSettings, selectDuplicateDomain, selectRealScaleType } from './axisSelectors';\nimport { selectAngleAxis, selectAngleAxisRangeWithReversed, selectRadiusAxis, selectRadiusAxisRangeWithReversed } from './polarAxisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectPolarAppliedValues, selectPolarAxisDomainIncludingNiceTicks, selectPolarNiceTicks } from './polarSelectors';\nimport { pickAxisType } from './pickAxisType';\nexport var selectPolarAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      {\n        throw new Error(\"Unexpected axis type: \".concat(axisType));\n      }\n  }\n};\nvar selectPolarAxisRangeWithReversed = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'angleAxis':\n      {\n        return selectAngleAxisRangeWithReversed(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxisRangeWithReversed(state, axisId);\n      }\n    default:\n      {\n        throw new Error(\"Unexpected axis type: \".concat(axisType));\n      }\n  }\n};\nexport var selectPolarAxisScale = createSelector([selectPolarAxis, selectRealScaleType, selectPolarAxisDomainIncludingNiceTicks, selectPolarAxisRangeWithReversed], combineScaleFunction);\nexport var selectPolarCategoricalDomain = createSelector([selectChartLayout, selectPolarAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectPolarAxisTicks = createSelector([selectChartLayout, selectPolarAxis, selectRealScaleType, selectPolarAxisScale, selectPolarNiceTicks, selectPolarAxisRangeWithReversed, selectDuplicateDomain, selectPolarCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var selectPolarGraphicalItemAxisTicks = createSelector([selectChartLayout, selectPolarAxis, selectPolarAxisScale, selectPolarAxisRangeWithReversed, selectDuplicateDomain, selectPolarCategoricalDomain, pickAxisType], combineGraphicalItemTicks);", "map": {"version": 3, "names": ["createSelector", "combineAxisTicks", "combineCategoricalDomain", "combineGraphicalItemTicks", "combineScaleFunction", "selectAxisSettings", "selectDuplicateDomain", "selectRealScaleType", "selectAngleAxis", "selectAngleAxisRangeWithReversed", "selectRadiusAxis", "selectRadiusAxisRangeWithReversed", "selectChartLayout", "selectPolarAppliedValues", "selectPolarAxisDomainIncludingNiceTicks", "selectPolarNiceTicks", "pickAxisType", "selectPolarAxis", "state", "axisType", "axisId", "Error", "concat", "selectPolarAxisRangeWithReversed", "selectPolarAxisScale", "selectPolarCategoricalDomain", "selectPolarAxisTicks", "selectPolarGraphicalItemAxisTicks"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/polarScaleSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { combineAxisTicks, combineCategoricalDomain, combineGraphicalItemTicks, combineScaleFunction, selectAxisSettings, selectDuplicateDomain, selectRealScaleType } from './axisSelectors';\nimport { selectAngleAxis, selectAngleAxisRangeWithReversed, selectRadiusAxis, selectRadiusAxisRangeWithReversed } from './polarAxisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectPolarAppliedValues, selectPolarAxisDomainIncludingNiceTicks, selectPolarNiceTicks } from './polarSelectors';\nimport { pickAxisType } from './pickAxisType';\nexport var selectPolarAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      {\n        throw new Error(\"Unexpected axis type: \".concat(axisType));\n      }\n  }\n};\nvar selectPolarAxisRangeWithReversed = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'angleAxis':\n      {\n        return selectAngleAxisRangeWithReversed(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxisRangeWithReversed(state, axisId);\n      }\n    default:\n      {\n        throw new Error(\"Unexpected axis type: \".concat(axisType));\n      }\n  }\n};\nexport var selectPolarAxisScale = createSelector([selectPolarAxis, selectRealScaleType, selectPolarAxisDomainIncludingNiceTicks, selectPolarAxisRangeWithReversed], combineScaleFunction);\nexport var selectPolarCategoricalDomain = createSelector([selectChartLayout, selectPolarAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectPolarAxisTicks = createSelector([selectChartLayout, selectPolarAxis, selectRealScaleType, selectPolarAxisScale, selectPolarNiceTicks, selectPolarAxisRangeWithReversed, selectDuplicateDomain, selectPolarCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var selectPolarGraphicalItemAxisTicks = createSelector([selectChartLayout, selectPolarAxis, selectPolarAxisScale, selectPolarAxisRangeWithReversed, selectDuplicateDomain, selectPolarCategoricalDomain, pickAxisType], combineGraphicalItemTicks);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,gBAAgB,EAAEC,wBAAwB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,iBAAiB;AAC7L,SAASC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,iCAAiC,QAAQ,sBAAsB;AAC7I,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,wBAAwB,EAAEC,uCAAuC,EAAEC,oBAAoB,QAAQ,kBAAkB;AAC1H,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,IAAIC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EACxD,QAAQD,QAAQ;IACd,KAAK,WAAW;MACd;QACE,OAAOX,eAAe,CAACU,KAAK,EAAEE,MAAM,CAAC;MACvC;IACF,KAAK,YAAY;MACf;QACE,OAAOV,gBAAgB,CAACQ,KAAK,EAAEE,MAAM,CAAC;MACxC;IACF;MACE;QACE,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACH,QAAQ,CAAC,CAAC;MAC5D;EACJ;AACF,CAAC;AACD,IAAII,gCAAgC,GAAGA,CAACL,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAClE,QAAQD,QAAQ;IACd,KAAK,WAAW;MACd;QACE,OAAOV,gCAAgC,CAACS,KAAK,EAAEE,MAAM,CAAC;MACxD;IACF,KAAK,YAAY;MACf;QACE,OAAOT,iCAAiC,CAACO,KAAK,EAAEE,MAAM,CAAC;MACzD;IACF;MACE;QACE,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACH,QAAQ,CAAC,CAAC;MAC5D;EACJ;AACF,CAAC;AACD,OAAO,IAAIK,oBAAoB,GAAGxB,cAAc,CAAC,CAACiB,eAAe,EAAEV,mBAAmB,EAAEO,uCAAuC,EAAES,gCAAgC,CAAC,EAAEnB,oBAAoB,CAAC;AACzL,OAAO,IAAIqB,4BAA4B,GAAGzB,cAAc,CAAC,CAACY,iBAAiB,EAAEC,wBAAwB,EAAER,kBAAkB,EAAEW,YAAY,CAAC,EAAEd,wBAAwB,CAAC;AACnK,OAAO,IAAIwB,oBAAoB,GAAG1B,cAAc,CAAC,CAACY,iBAAiB,EAAEK,eAAe,EAAEV,mBAAmB,EAAEiB,oBAAoB,EAAET,oBAAoB,EAAEQ,gCAAgC,EAAEjB,qBAAqB,EAAEmB,4BAA4B,EAAET,YAAY,CAAC,EAAEf,gBAAgB,CAAC;AAC9Q,OAAO,IAAI0B,iCAAiC,GAAG3B,cAAc,CAAC,CAACY,iBAAiB,EAAEK,eAAe,EAAEO,oBAAoB,EAAED,gCAAgC,EAAEjB,qBAAqB,EAAEmB,4BAA4B,EAAET,YAAY,CAAC,EAAEb,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
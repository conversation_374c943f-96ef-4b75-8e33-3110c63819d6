{"ast": null, "code": "function isBuffer(x) {\n  return typeof Buffer !== 'undefined' && Buffer.isBuffer(x);\n}\nexport { isBuffer };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "x", "<PERSON><PERSON><PERSON>"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isBuffer.mjs"], "sourcesContent": ["function isBuffer(x) {\n    return typeof Buffer !== 'undefined' && Buffer.isBuffer(x);\n}\n\nexport { isBuffer };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAO,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACF,QAAQ,CAACC,CAAC,CAAC;AAC9D;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nimport { isStacked } from '../types/StackedGraphicalItem';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'bar').find(item => item.id === id));\nexport var selectMaxBarSize = createSelector([selectSynchronisedBarSettings], barSettings => barSettings === null || barSettings === void 0 ? void 0 : barSettings.maxBarSize);\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _id, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, id) => {\n  var _ref2, _getBandSizeOfAxis;\n  var barSettings = selectSynchronisedBarSettings(state, xAxisId, yAxisId, isPanorama, id);\n  if (barSettings == null) {\n    return undefined;\n  }\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nexport var selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, selectMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, selectSynchronisedBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null || barSettings == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && barSettings.dataKey != null && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  var stackSeriesIdentifier = getStackSeriesIdentifier(barSettings);\n  if (!stackGroups || stackSeriesIdentifier == null || barSettings == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  return stackedData.find(sd => sd.key === stackSeriesIdentifier);\n};\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, selectSynchronisedBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffsetInternal, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "selectAxisWithScale", "selectCartesianAxisSize", "selectStackGroups", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "getPercentValue", "<PERSON><PERSON><PERSON><PERSON>", "getBandSizeOfAxis", "computeBarRectangles", "selectChartLayout", "selectChartDataWithIndexesIfNotInPanorama", "selectChartOffsetInternal", "selectBarCategoryGap", "selectBarGap", "selectRootBarSize", "selectRootMaxBarSize", "isWellBehavedNumber", "getStackSeriesIdentifier", "isStacked", "pickXAxisId", "_state", "xAxisId", "pickYAxisId", "_xAxisId", "yAxisId", "pickIsPanorama", "_yAxisId", "isPanorama", "pickBarId", "_isPanorama", "id", "selectSynchronisedBarSettings", "graphicalItems", "item", "type", "find", "selectMaxBarSize", "barSettings", "maxBarSize", "pick<PERSON>ells", "_id", "cells", "getBarSize", "globalSize", "totalSize", "selfSize", "barSize", "undefined", "selectAllVisibleBars", "layout", "allItems", "hide", "selectBarStackGroups", "state", "selectBarCartesianAxisSize", "combineBarSizeList", "allBars", "initialValue", "stackedBars", "unstackedBars", "b", "stackId", "groupByStack", "reduce", "acc", "bar", "stackedSizeList", "entries", "map", "_ref", "bars", "dataKeys", "dataKey", "unstackedSizeList", "dk", "selectBarSizeList", "selectBarBandSize", "_ref2", "_getBandSizeOfAxis", "globalMaxBarSize", "childMaxBarSize", "axis", "ticks", "selectAxisBandSize", "getBarPositions", "barGap", "barCategoryGap", "bandSize", "sizeList", "len", "realBarGap", "result", "useFull", "fullBarSize", "sum", "res", "entry", "offset", "prev", "size", "_entry$barSize", "newPosition", "position", "newRes", "_offset", "originalSize", "Math", "min", "combineAllBarPositions", "barBandSize", "allBarPositions", "pos", "selectAllBarPositions", "selectXAxisWithScale", "selectYAxisWithScale", "selectXAxisTicks", "selectYAxisTicks", "selectBarPosition", "p", "includes", "combineStackedData", "stackGroups", "stackSeriesIdentifier", "stackGroup", "stackedData", "sd", "key", "selectStackedDataOfItem", "selectBarRectangles", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "_ref3", "chartData", "dataStartIndex", "dataEndIndex", "data", "displayedData", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/barSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { selectAxisWithScale, selectCartesianAxisSize, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { getPercentValue, isNullish } from '../../util/DataUtils';\nimport { getBandSizeOfAxis } from '../../util/ChartUtils';\nimport { computeBarRectangles } from '../../cartesian/Bar';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBarCategoryGap, selectBarGap, selectRootBarSize, selectRootMaxBarSize } from './rootPropsSelectors';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nimport { isStacked } from '../types/StackedGraphicalItem';\nvar pickXAxisId = (_state, xAxisId) => xAxisId;\nvar pickYAxisId = (_state, _xAxisId, yAxisId) => yAxisId;\nvar pickIsPanorama = (_state, _xAxisId, _yAxisId, isPanorama) => isPanorama;\nvar pickBarId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\nvar selectSynchronisedBarSettings = createSelector([selectUnfilteredCartesianItems, pickBarId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'bar').find(item => item.id === id));\nexport var selectMaxBarSize = createSelector([selectSynchronisedBarSettings], barSettings => barSettings === null || barSettings === void 0 ? void 0 : barSettings.maxBarSize);\nvar pickCells = (_state, _xAxisId, _yAxisId, _isPanorama, _id, cells) => cells;\nvar getBarSize = (globalSize, totalSize, selfSize) => {\n  var barSize = selfSize !== null && selfSize !== void 0 ? selfSize : globalSize;\n  if (isNullish(barSize)) {\n    return undefined;\n  }\n  return getPercentValue(barSize, totalSize, 0);\n};\nexport var selectAllVisibleBars = createSelector([selectChartLayout, selectUnfilteredCartesianItems, pickXAxisId, pickYAxisId, pickIsPanorama], (layout, allItems, xAxisId, yAxisId, isPanorama) => allItems.filter(i => {\n  if (layout === 'horizontal') {\n    return i.xAxisId === xAxisId;\n  }\n  return i.yAxisId === yAxisId;\n}).filter(i => i.isPanorama === isPanorama).filter(i => i.hide === false).filter(i => i.type === 'bar'));\nvar selectBarStackGroups = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n};\nexport var selectBarCartesianAxisSize = (state, xAxisId, yAxisId) => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return selectCartesianAxisSize(state, 'xAxis', xAxisId);\n  }\n  return selectCartesianAxisSize(state, 'yAxis', yAxisId);\n};\nexport var combineBarSizeList = (allBars, globalSize, totalSize) => {\n  var initialValue = {};\n  var stackedBars = allBars.filter(isStacked);\n  var unstackedBars = allBars.filter(b => b.stackId == null);\n  var groupByStack = stackedBars.reduce((acc, bar) => {\n    if (!acc[bar.stackId]) {\n      acc[bar.stackId] = [];\n    }\n    acc[bar.stackId].push(bar);\n    return acc;\n  }, initialValue);\n  var stackedSizeList = Object.entries(groupByStack).map(_ref => {\n    var [stackId, bars] = _ref;\n    var dataKeys = bars.map(b => b.dataKey);\n    var barSize = getBarSize(globalSize, totalSize, bars[0].barSize);\n    return {\n      stackId,\n      dataKeys,\n      barSize\n    };\n  });\n  var unstackedSizeList = unstackedBars.map(b => {\n    var dataKeys = [b.dataKey].filter(dk => dk != null);\n    var barSize = getBarSize(globalSize, totalSize, b.barSize);\n    return {\n      stackId: undefined,\n      dataKeys,\n      barSize\n    };\n  });\n  return [...stackedSizeList, ...unstackedSizeList];\n};\nexport var selectBarSizeList = createSelector([selectAllVisibleBars, selectRootBarSize, selectBarCartesianAxisSize], combineBarSizeList);\nexport var selectBarBandSize = (state, xAxisId, yAxisId, isPanorama, id) => {\n  var _ref2, _getBandSizeOfAxis;\n  var barSettings = selectSynchronisedBarSettings(state, xAxisId, yAxisId, isPanorama, id);\n  if (barSettings == null) {\n    return undefined;\n  }\n  var layout = selectChartLayout(state);\n  var globalMaxBarSize = selectRootMaxBarSize(state);\n  var {\n    maxBarSize: childMaxBarSize\n  } = barSettings;\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return (_ref2 = (_getBandSizeOfAxis = getBandSizeOfAxis(axis, ticks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref2 !== void 0 ? _ref2 : 0;\n};\nexport var selectAxisBandSize = (state, xAxisId, yAxisId, isPanorama) => {\n  var layout = selectChartLayout(state);\n  var axis, ticks;\n  if (layout === 'horizontal') {\n    axis = selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\n  } else {\n    axis = selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\n    ticks = selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\n  }\n  return getBandSizeOfAxis(axis, ticks);\n};\nfunction getBarPositions(barGap, barCategoryGap, bandSize, sizeList, maxBarSize) {\n  var len = sizeList.length;\n  if (len < 1) {\n    return undefined;\n  }\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether is barSize set by user\n  // Okay but why does it check only for the first element? What if the first element is set but others are not?\n  if (isWellBehavedNumber(sizeList[0].barSize)) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    var sum = sizeList.reduce((res, entry) => res + (entry.barSize || 0), 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce((res, entry) => {\n      var _entry$barSize;\n      var newPosition = {\n        stackId: entry.stackId,\n        dataKeys: entry.dataKeys,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          size: useFull ? fullBarSize : (_entry$barSize = entry.barSize) !== null && _entry$barSize !== void 0 ? _entry$barSize : 0\n        }\n      };\n      var newRes = [...res, newPosition];\n      prev = newRes[newRes.length - 1].position;\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = isWellBehavedNumber(maxBarSize) ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce((res, entry, i) => [...res, {\n      stackId: entry.stackId,\n      dataKeys: entry.dataKeys,\n      position: {\n        offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n        size\n      }\n    }], initialValue);\n  }\n  return result;\n}\nexport var combineAllBarPositions = (sizeList, globalMaxBarSize, barGap, barCategoryGap, barBandSize, bandSize, childMaxBarSize) => {\n  var maxBarSize = isNullish(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n  var allBarPositions = getBarPositions(barGap, barCategoryGap, barBandSize !== bandSize ? barBandSize : bandSize, sizeList, maxBarSize);\n  if (barBandSize !== bandSize && allBarPositions != null) {\n    allBarPositions = allBarPositions.map(pos => _objectSpread(_objectSpread({}, pos), {}, {\n      position: _objectSpread(_objectSpread({}, pos.position), {}, {\n        offset: pos.position.offset - barBandSize / 2\n      })\n    }));\n  }\n  return allBarPositions;\n};\nexport var selectAllBarPositions = createSelector([selectBarSizeList, selectRootMaxBarSize, selectBarGap, selectBarCategoryGap, selectBarBandSize, selectAxisBandSize, selectMaxBarSize], combineAllBarPositions);\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nexport var selectBarPosition = createSelector([selectAllBarPositions, selectSynchronisedBarSettings], (allBarPositions, barSettings) => {\n  if (allBarPositions == null || barSettings == null) {\n    return undefined;\n  }\n  var position = allBarPositions.find(p => p.stackId === barSettings.stackId && barSettings.dataKey != null && p.dataKeys.includes(barSettings.dataKey));\n  if (position == null) {\n    return undefined;\n  }\n  return position.position;\n});\nexport var combineStackedData = (stackGroups, barSettings) => {\n  var stackSeriesIdentifier = getStackSeriesIdentifier(barSettings);\n  if (!stackGroups || stackSeriesIdentifier == null || barSettings == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = barSettings;\n  if (stackId == null) {\n    return undefined;\n  }\n  var stackGroup = stackGroups[stackId];\n  if (!stackGroup) {\n    return undefined;\n  }\n  var {\n    stackedData\n  } = stackGroup;\n  if (!stackedData) {\n    return undefined;\n  }\n  return stackedData.find(sd => sd.key === stackSeriesIdentifier);\n};\nvar selectStackedDataOfItem = createSelector([selectBarStackGroups, selectSynchronisedBarSettings], combineStackedData);\nexport var selectBarRectangles = createSelector([selectChartOffsetInternal, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectBarPosition, selectChartLayout, selectChartDataWithIndexesIfNotInPanorama, selectAxisBandSize, selectStackedDataOfItem, selectSynchronisedBarSettings, pickCells], (offset, xAxis, yAxis, xAxisTicks, yAxisTicks, pos, layout, _ref3, bandSize, stackedData, barSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (barSettings == null || pos == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = barSettings;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return computeBarRectangles({\n    layout,\n    barSettings,\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    displayedData,\n    offset,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,SAASC,mBAAmB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AAC7J,SAASC,eAAe,EAAEC,SAAS,QAAQ,sBAAsB;AACjE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,oBAAoB,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAClH,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,IAAIC,WAAW,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAKA,OAAO;AAC9C,IAAIC,WAAW,GAAGA,CAACF,MAAM,EAAEG,QAAQ,EAAEC,OAAO,KAAKA,OAAO;AACxD,IAAIC,cAAc,GAAGA,CAACL,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,KAAKA,UAAU;AAC3E,IAAIC,SAAS,GAAGA,CAACR,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEG,WAAW,EAAEC,EAAE,KAAKA,EAAE;AACnE,IAAIC,6BAA6B,GAAGhC,cAAc,CAAC,CAACK,8BAA8B,EAAEwB,SAAS,CAAC,EAAE,CAACI,cAAc,EAAEF,EAAE,KAAKE,cAAc,CAAC1D,MAAM,CAAC2D,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,KAAK,CAAC,CAACC,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACH,EAAE,KAAKA,EAAE,CAAC,CAAC;AACxM,OAAO,IAAIM,gBAAgB,GAAGrC,cAAc,CAAC,CAACgC,6BAA6B,CAAC,EAAEM,WAAW,IAAIA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,UAAU,CAAC;AAC9K,IAAIC,SAAS,GAAGA,CAACnB,MAAM,EAAEG,QAAQ,EAAEG,QAAQ,EAAEG,WAAW,EAAEW,GAAG,EAAEC,KAAK,KAAKA,KAAK;AAC9E,IAAIC,UAAU,GAAGA,CAACC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EACpD,IAAIC,OAAO,GAAGD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGF,UAAU;EAC9E,IAAIrC,SAAS,CAACwC,OAAO,CAAC,EAAE;IACtB,OAAOC,SAAS;EAClB;EACA,OAAO1C,eAAe,CAACyC,OAAO,EAAEF,SAAS,EAAE,CAAC,CAAC;AAC/C,CAAC;AACD,OAAO,IAAII,oBAAoB,GAAGjD,cAAc,CAAC,CAACU,iBAAiB,EAAEL,8BAA8B,EAAEe,WAAW,EAAEG,WAAW,EAAEG,cAAc,CAAC,EAAE,CAACwB,MAAM,EAAEC,QAAQ,EAAE7B,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAKuB,QAAQ,CAAC5E,MAAM,CAACiB,CAAC,IAAI;EACvN,IAAI0D,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO1D,CAAC,CAAC8B,OAAO,KAAKA,OAAO;EAC9B;EACA,OAAO9B,CAAC,CAACiC,OAAO,KAAKA,OAAO;AAC9B,CAAC,CAAC,CAAClD,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACoC,UAAU,KAAKA,UAAU,CAAC,CAACrD,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAAC4D,IAAI,KAAK,KAAK,CAAC,CAAC7E,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAAC2C,IAAI,KAAK,KAAK,CAAC,CAAC;AACxG,IAAIkB,oBAAoB,GAAGA,CAACC,KAAK,EAAEhC,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAK;EAClE,IAAIsB,MAAM,GAAGxC,iBAAiB,CAAC4C,KAAK,CAAC;EACrC,IAAIJ,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO/C,iBAAiB,CAACmD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;EAC/D;EACA,OAAOzB,iBAAiB,CAACmD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;AAC/D,CAAC;AACD,OAAO,IAAI2B,0BAA0B,GAAGA,CAACD,KAAK,EAAEhC,OAAO,EAAEG,OAAO,KAAK;EACnE,IAAIyB,MAAM,GAAGxC,iBAAiB,CAAC4C,KAAK,CAAC;EACrC,IAAIJ,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOhD,uBAAuB,CAACoD,KAAK,EAAE,OAAO,EAAEhC,OAAO,CAAC;EACzD;EACA,OAAOpB,uBAAuB,CAACoD,KAAK,EAAE,OAAO,EAAE7B,OAAO,CAAC;AACzD,CAAC;AACD,OAAO,IAAI+B,kBAAkB,GAAGA,CAACC,OAAO,EAAEb,UAAU,EAAEC,SAAS,KAAK;EAClE,IAAIa,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,WAAW,GAAGF,OAAO,CAAClF,MAAM,CAAC4C,SAAS,CAAC;EAC3C,IAAIyC,aAAa,GAAGH,OAAO,CAAClF,MAAM,CAACsF,CAAC,IAAIA,CAAC,CAACC,OAAO,IAAI,IAAI,CAAC;EAC1D,IAAIC,YAAY,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAClD,IAAI,CAACD,GAAG,CAACC,GAAG,CAACJ,OAAO,CAAC,EAAE;MACrBG,GAAG,CAACC,GAAG,CAACJ,OAAO,CAAC,GAAG,EAAE;IACvB;IACAG,GAAG,CAACC,GAAG,CAACJ,OAAO,CAAC,CAACpF,IAAI,CAACwF,GAAG,CAAC;IAC1B,OAAOD,GAAG;EACZ,CAAC,EAAEP,YAAY,CAAC;EAChB,IAAIS,eAAe,GAAGhG,MAAM,CAACiG,OAAO,CAACL,YAAY,CAAC,CAACM,GAAG,CAACC,IAAI,IAAI;IAC7D,IAAI,CAACR,OAAO,EAAES,IAAI,CAAC,GAAGD,IAAI;IAC1B,IAAIE,QAAQ,GAAGD,IAAI,CAACF,GAAG,CAACR,CAAC,IAAIA,CAAC,CAACY,OAAO,CAAC;IACvC,IAAI1B,OAAO,GAAGJ,UAAU,CAACC,UAAU,EAAEC,SAAS,EAAE0B,IAAI,CAAC,CAAC,CAAC,CAACxB,OAAO,CAAC;IAChE,OAAO;MACLe,OAAO;MACPU,QAAQ;MACRzB;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAI2B,iBAAiB,GAAGd,aAAa,CAACS,GAAG,CAACR,CAAC,IAAI;IAC7C,IAAIW,QAAQ,GAAG,CAACX,CAAC,CAACY,OAAO,CAAC,CAAClG,MAAM,CAACoG,EAAE,IAAIA,EAAE,IAAI,IAAI,CAAC;IACnD,IAAI5B,OAAO,GAAGJ,UAAU,CAACC,UAAU,EAAEC,SAAS,EAAEgB,CAAC,CAACd,OAAO,CAAC;IAC1D,OAAO;MACLe,OAAO,EAAEd,SAAS;MAClBwB,QAAQ;MACRzB;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,CAAC,GAAGoB,eAAe,EAAE,GAAGO,iBAAiB,CAAC;AACnD,CAAC;AACD,OAAO,IAAIE,iBAAiB,GAAG5E,cAAc,CAAC,CAACiD,oBAAoB,EAAElC,iBAAiB,EAAEwC,0BAA0B,CAAC,EAAEC,kBAAkB,CAAC;AACxI,OAAO,IAAIqB,iBAAiB,GAAGA,CAACvB,KAAK,EAAEhC,OAAO,EAAEG,OAAO,EAAEG,UAAU,EAAEG,EAAE,KAAK;EAC1E,IAAI+C,KAAK,EAAEC,kBAAkB;EAC7B,IAAIzC,WAAW,GAAGN,6BAA6B,CAACsB,KAAK,EAAEhC,OAAO,EAAEG,OAAO,EAAEG,UAAU,EAAEG,EAAE,CAAC;EACxF,IAAIO,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOU,SAAS;EAClB;EACA,IAAIE,MAAM,GAAGxC,iBAAiB,CAAC4C,KAAK,CAAC;EACrC,IAAI0B,gBAAgB,GAAGhE,oBAAoB,CAACsC,KAAK,CAAC;EAClD,IAAI;IACFf,UAAU,EAAE0C;EACd,CAAC,GAAG3C,WAAW;EACf,IAAIC,UAAU,GAAGhC,SAAS,CAAC0E,eAAe,CAAC,GAAGD,gBAAgB,GAAGC,eAAe;EAChF,IAAIC,IAAI,EAAEC,KAAK;EACf,IAAIjC,MAAM,KAAK,YAAY,EAAE;IAC3BgC,IAAI,GAAGjF,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;IAC/DuD,KAAK,GAAG/E,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;EACzE,CAAC,MAAM;IACLsD,IAAI,GAAGjF,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;IAC/DuD,KAAK,GAAG/E,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;EACzE;EACA,OAAO,CAACkD,KAAK,GAAG,CAACC,kBAAkB,GAAGvE,iBAAiB,CAAC0E,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIJ,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGxC,UAAU,MAAM,IAAI,IAAIuC,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;AACnM,CAAC;AACD,OAAO,IAAIM,kBAAkB,GAAGA,CAAC9B,KAAK,EAAEhC,OAAO,EAAEG,OAAO,EAAEG,UAAU,KAAK;EACvE,IAAIsB,MAAM,GAAGxC,iBAAiB,CAAC4C,KAAK,CAAC;EACrC,IAAI4B,IAAI,EAAEC,KAAK;EACf,IAAIjC,MAAM,KAAK,YAAY,EAAE;IAC3BgC,IAAI,GAAGjF,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;IAC/DuD,KAAK,GAAG/E,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;EACzE,CAAC,MAAM;IACLsD,IAAI,GAAGjF,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;IAC/DuD,KAAK,GAAG/E,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;EACzE;EACA,OAAOpB,iBAAiB,CAAC0E,IAAI,EAAEC,KAAK,CAAC;AACvC,CAAC;AACD,SAASE,eAAeA,CAACC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAElD,UAAU,EAAE;EAC/E,IAAImD,GAAG,GAAGD,QAAQ,CAAC3G,MAAM;EACzB,IAAI4G,GAAG,GAAG,CAAC,EAAE;IACX,OAAO1C,SAAS;EAClB;EACA,IAAI2C,UAAU,GAAGrF,eAAe,CAACgF,MAAM,EAAEE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;EAC3D,IAAII,MAAM;EACV,IAAIlC,YAAY,GAAG,EAAE;;EAErB;EACA;EACA,IAAIzC,mBAAmB,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC1C,OAAO,CAAC,EAAE;IAC5C,IAAI8C,OAAO,GAAG,KAAK;IACnB,IAAIC,WAAW,GAAGN,QAAQ,GAAGE,GAAG;IAChC,IAAIK,GAAG,GAAGN,QAAQ,CAACzB,MAAM,CAAC,CAACgC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,CAAClD,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACxEgD,GAAG,IAAI,CAACL,GAAG,GAAG,CAAC,IAAIC,UAAU;IAC7B,IAAII,GAAG,IAAIP,QAAQ,EAAE;MACnBO,GAAG,IAAI,CAACL,GAAG,GAAG,CAAC,IAAIC,UAAU;MAC7BA,UAAU,GAAG,CAAC;IAChB;IACA,IAAII,GAAG,IAAIP,QAAQ,IAAIM,WAAW,GAAG,CAAC,EAAE;MACtCD,OAAO,GAAG,IAAI;MACdC,WAAW,IAAI,GAAG;MAClBC,GAAG,GAAGL,GAAG,GAAGI,WAAW;IACzB;IACA,IAAII,MAAM,GAAG,CAACV,QAAQ,GAAGO,GAAG,IAAI,CAAC,IAAI,CAAC;IACtC,IAAII,IAAI,GAAG;MACTD,MAAM,EAAEA,MAAM,GAAGP,UAAU;MAC3BS,IAAI,EAAE;IACR,CAAC;IACDR,MAAM,GAAGH,QAAQ,CAACzB,MAAM,CAAC,CAACgC,GAAG,EAAEC,KAAK,KAAK;MACvC,IAAII,cAAc;MAClB,IAAIC,WAAW,GAAG;QAChBxC,OAAO,EAAEmC,KAAK,CAACnC,OAAO;QACtBU,QAAQ,EAAEyB,KAAK,CAACzB,QAAQ;QACxB+B,QAAQ,EAAE;UACRL,MAAM,EAAEC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI,GAAGT,UAAU;UAC5CS,IAAI,EAAEP,OAAO,GAAGC,WAAW,GAAG,CAACO,cAAc,GAAGJ,KAAK,CAAClD,OAAO,MAAM,IAAI,IAAIsD,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG;QAC1H;MACF,CAAC;MACD,IAAIG,MAAM,GAAG,CAAC,GAAGR,GAAG,EAAEM,WAAW,CAAC;MAClCH,IAAI,GAAGK,MAAM,CAACA,MAAM,CAAC1H,MAAM,GAAG,CAAC,CAAC,CAACyH,QAAQ;MACzC,OAAOC,MAAM;IACf,CAAC,EAAE9C,YAAY,CAAC;EAClB,CAAC,MAAM;IACL,IAAI+C,OAAO,GAAGnG,eAAe,CAACiF,cAAc,EAAEC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;IAChE,IAAIA,QAAQ,GAAG,CAAC,GAAGiB,OAAO,GAAG,CAACf,GAAG,GAAG,CAAC,IAAIC,UAAU,IAAI,CAAC,EAAE;MACxDA,UAAU,GAAG,CAAC;IAChB;IACA,IAAIe,YAAY,GAAG,CAAClB,QAAQ,GAAG,CAAC,GAAGiB,OAAO,GAAG,CAACf,GAAG,GAAG,CAAC,IAAIC,UAAU,IAAID,GAAG;IAC1E,IAAIgB,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,KAAK,CAAC;IACpB;IACA,IAAIN,IAAI,GAAGnF,mBAAmB,CAACsB,UAAU,CAAC,GAAGoE,IAAI,CAACC,GAAG,CAACF,YAAY,EAAEnE,UAAU,CAAC,GAAGmE,YAAY;IAC9Fd,MAAM,GAAGH,QAAQ,CAACzB,MAAM,CAAC,CAACgC,GAAG,EAAEC,KAAK,EAAEzG,CAAC,KAAK,CAAC,GAAGwG,GAAG,EAAE;MACnDlC,OAAO,EAAEmC,KAAK,CAACnC,OAAO;MACtBU,QAAQ,EAAEyB,KAAK,CAACzB,QAAQ;MACxB+B,QAAQ,EAAE;QACRL,MAAM,EAAEO,OAAO,GAAG,CAACC,YAAY,GAAGf,UAAU,IAAInG,CAAC,GAAG,CAACkH,YAAY,GAAGN,IAAI,IAAI,CAAC;QAC7EA;MACF;IACF,CAAC,CAAC,EAAE1C,YAAY,CAAC;EACnB;EACA,OAAOkC,MAAM;AACf;AACA,OAAO,IAAIiB,sBAAsB,GAAGA,CAACpB,QAAQ,EAAET,gBAAgB,EAAEM,MAAM,EAAEC,cAAc,EAAEuB,WAAW,EAAEtB,QAAQ,EAAEP,eAAe,KAAK;EAClI,IAAI1C,UAAU,GAAGhC,SAAS,CAAC0E,eAAe,CAAC,GAAGD,gBAAgB,GAAGC,eAAe;EAChF,IAAI8B,eAAe,GAAG1B,eAAe,CAACC,MAAM,EAAEC,cAAc,EAAEuB,WAAW,KAAKtB,QAAQ,GAAGsB,WAAW,GAAGtB,QAAQ,EAAEC,QAAQ,EAAElD,UAAU,CAAC;EACtI,IAAIuE,WAAW,KAAKtB,QAAQ,IAAIuB,eAAe,IAAI,IAAI,EAAE;IACvDA,eAAe,GAAGA,eAAe,CAAC1C,GAAG,CAAC2C,GAAG,IAAIpI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;MACrFT,QAAQ,EAAE3H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoI,GAAG,CAACT,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DL,MAAM,EAAEc,GAAG,CAACT,QAAQ,CAACL,MAAM,GAAGY,WAAW,GAAG;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC;EACL;EACA,OAAOC,eAAe;AACxB,CAAC;AACD,OAAO,IAAIE,qBAAqB,GAAGjH,cAAc,CAAC,CAAC4E,iBAAiB,EAAE5D,oBAAoB,EAAEF,YAAY,EAAED,oBAAoB,EAAEgE,iBAAiB,EAAEO,kBAAkB,EAAE/C,gBAAgB,CAAC,EAAEwE,sBAAsB,CAAC;AACjN,IAAIK,oBAAoB,GAAGA,CAAC5D,KAAK,EAAEhC,OAAO,EAAEK,QAAQ,EAAEC,UAAU,KAAK3B,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;AAC7H,IAAIuF,oBAAoB,GAAGA,CAAC7D,KAAK,EAAE9B,QAAQ,EAAEC,OAAO,EAAEG,UAAU,KAAK3B,mBAAmB,CAACqD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;AAC7H,IAAIwF,gBAAgB,GAAGA,CAAC9D,KAAK,EAAEhC,OAAO,EAAEK,QAAQ,EAAEC,UAAU,KAAKxB,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAEhC,OAAO,EAAEM,UAAU,CAAC;AAChI,IAAIyF,gBAAgB,GAAGA,CAAC/D,KAAK,EAAE9B,QAAQ,EAAEC,OAAO,EAAEG,UAAU,KAAKxB,0BAA0B,CAACkD,KAAK,EAAE,OAAO,EAAE7B,OAAO,EAAEG,UAAU,CAAC;AAChI,OAAO,IAAI0F,iBAAiB,GAAGtH,cAAc,CAAC,CAACiH,qBAAqB,EAAEjF,6BAA6B,CAAC,EAAE,CAAC+E,eAAe,EAAEzE,WAAW,KAAK;EACtI,IAAIyE,eAAe,IAAI,IAAI,IAAIzE,WAAW,IAAI,IAAI,EAAE;IAClD,OAAOU,SAAS;EAClB;EACA,IAAIuD,QAAQ,GAAGQ,eAAe,CAAC3E,IAAI,CAACmF,CAAC,IAAIA,CAAC,CAACzD,OAAO,KAAKxB,WAAW,CAACwB,OAAO,IAAIxB,WAAW,CAACmC,OAAO,IAAI,IAAI,IAAI8C,CAAC,CAAC/C,QAAQ,CAACgD,QAAQ,CAAClF,WAAW,CAACmC,OAAO,CAAC,CAAC;EACtJ,IAAI8B,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAOvD,SAAS;EAClB;EACA,OAAOuD,QAAQ,CAACA,QAAQ;AAC1B,CAAC,CAAC;AACF,OAAO,IAAIkB,kBAAkB,GAAGA,CAACC,WAAW,EAAEpF,WAAW,KAAK;EAC5D,IAAIqF,qBAAqB,GAAGzG,wBAAwB,CAACoB,WAAW,CAAC;EACjE,IAAI,CAACoF,WAAW,IAAIC,qBAAqB,IAAI,IAAI,IAAIrF,WAAW,IAAI,IAAI,EAAE;IACxE,OAAOU,SAAS;EAClB;EACA,IAAI;IACFc;EACF,CAAC,GAAGxB,WAAW;EACf,IAAIwB,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOd,SAAS;EAClB;EACA,IAAI4E,UAAU,GAAGF,WAAW,CAAC5D,OAAO,CAAC;EACrC,IAAI,CAAC8D,UAAU,EAAE;IACf,OAAO5E,SAAS;EAClB;EACA,IAAI;IACF6E;EACF,CAAC,GAAGD,UAAU;EACd,IAAI,CAACC,WAAW,EAAE;IAChB,OAAO7E,SAAS;EAClB;EACA,OAAO6E,WAAW,CAACzF,IAAI,CAAC0F,EAAE,IAAIA,EAAE,CAACC,GAAG,KAAKJ,qBAAqB,CAAC;AACjE,CAAC;AACD,IAAIK,uBAAuB,GAAGhI,cAAc,CAAC,CAACqD,oBAAoB,EAAErB,6BAA6B,CAAC,EAAEyF,kBAAkB,CAAC;AACvH,OAAO,IAAIQ,mBAAmB,GAAGjI,cAAc,CAAC,CAACY,yBAAyB,EAAEsG,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE5G,iBAAiB,EAAEC,yCAAyC,EAAEyE,kBAAkB,EAAE4C,uBAAuB,EAAEhG,6BAA6B,EAAEQ,SAAS,CAAC,EAAE,CAAC0D,MAAM,EAAEgC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAErB,GAAG,EAAE9D,MAAM,EAAEoF,KAAK,EAAE9C,QAAQ,EAAEqC,WAAW,EAAEvF,WAAW,EAAEI,KAAK,KAAK;EACpb,IAAI;IACF6F,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGH,KAAK;EACT,IAAIhG,WAAW,IAAI,IAAI,IAAI0E,GAAG,IAAI,IAAI,IAAI9D,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIgF,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAI7C,QAAQ,IAAI,IAAI,EAAE;IAC5L,OAAOxC,SAAS;EAClB;EACA,IAAI;IACF0F;EACF,CAAC,GAAGpG,WAAW;EACf,IAAIqG,aAAa;EACjB,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC5J,MAAM,GAAG,CAAC,EAAE;IACnC6J,aAAa,GAAGD,IAAI;EACtB,CAAC,MAAM;IACLC,aAAa,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,KAAK,CAACJ,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIE,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO3F,SAAS;EAClB;EACA,OAAOvC,oBAAoB,CAAC;IAC1ByC,MAAM;IACNZ,WAAW;IACX0E,GAAG;IACHxB,QAAQ;IACR0C,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVR,WAAW;IACXc,aAAa;IACbzC,MAAM;IACNxD;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
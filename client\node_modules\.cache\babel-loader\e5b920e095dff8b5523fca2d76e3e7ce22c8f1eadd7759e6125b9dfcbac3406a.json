{"ast": null, "code": "import { useAppDispatch } from '../state/hooks';\nimport { mouseLeaveItem, setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nexport var useMouseEnterItemDispatch = (onMouseEnterFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseEnterFromProps === null || onMouseEnterFromProps === void 0 || onMouseEnterFromProps(data, index, event);\n    dispatch(setActiveMouseOverItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};\nexport var useMouseLeaveItemDispatch = onMouseLeaveFromProps => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseLeaveFromProps === null || onMouseLeaveFromProps === void 0 || onMouseLeaveFromProps(data, index, event);\n    dispatch(mouseLeaveItem());\n  };\n};\nexport var useMouseClickItemDispatch = (onMouseClickFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseClickFromProps === null || onMouseClickFromProps === void 0 || onMouseClickFromProps(data, index, event);\n    dispatch(setActiveClickItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};", "map": {"version": 3, "names": ["useAppDispatch", "mouseLeaveItem", "setActiveClickItemIndex", "setActiveMouseOverItemIndex", "useMouseEnterItemDispatch", "onMouseEnterFromProps", "dataKey", "dispatch", "data", "index", "event", "activeIndex", "String", "activeDataKey", "activeCoordinate", "tooltipPosition", "useMouseLeaveItemDispatch", "onMouseLeaveFromProps", "useMouseClickItemDispatch", "onMouseClickFromProps"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/context/tooltipContext.js"], "sourcesContent": ["import { useAppDispatch } from '../state/hooks';\nimport { mouseLeaveItem, setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nexport var useMouseEnterItemDispatch = (onMouseEnterFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseEnterFromProps === null || onMouseEnterFromProps === void 0 || onMouseEnterFromProps(data, index, event);\n    dispatch(setActiveMouseOverItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};\nexport var useMouseLeaveItemDispatch = onMouseLeaveFromProps => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseLeaveFromProps === null || onMouseLeaveFromProps === void 0 || onMouseLeaveFromProps(data, index, event);\n    dispatch(mouseLeaveItem());\n  };\n};\nexport var useMouseClickItemDispatch = (onMouseClickFromProps, dataKey) => {\n  var dispatch = useAppDispatch();\n  return (data, index) => event => {\n    onMouseClickFromProps === null || onMouseClickFromProps === void 0 || onMouseClickFromProps(data, index, event);\n    dispatch(setActiveClickItemIndex({\n      activeIndex: String(index),\n      activeDataKey: dataKey,\n      activeCoordinate: data.tooltipPosition\n    }));\n  };\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,2BAA2B,QAAQ,uBAAuB;AAC5G,OAAO,IAAIC,yBAAyB,GAAGA,CAACC,qBAAqB,EAAEC,OAAO,KAAK;EACzE,IAAIC,QAAQ,GAAGP,cAAc,CAAC,CAAC;EAC/B,OAAO,CAACQ,IAAI,EAAEC,KAAK,KAAKC,KAAK,IAAI;IAC/BL,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACG,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC/GH,QAAQ,CAACJ,2BAA2B,CAAC;MACnCQ,WAAW,EAAEC,MAAM,CAACH,KAAK,CAAC;MAC1BI,aAAa,EAAEP,OAAO;MACtBQ,gBAAgB,EAAEN,IAAI,CAACO;IACzB,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,yBAAyB,GAAGC,qBAAqB,IAAI;EAC9D,IAAIV,QAAQ,GAAGP,cAAc,CAAC,CAAC;EAC/B,OAAO,CAACQ,IAAI,EAAEC,KAAK,KAAKC,KAAK,IAAI;IAC/BO,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACT,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC/GH,QAAQ,CAACN,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC;AACH,CAAC;AACD,OAAO,IAAIiB,yBAAyB,GAAGA,CAACC,qBAAqB,EAAEb,OAAO,KAAK;EACzE,IAAIC,QAAQ,GAAGP,cAAc,CAAC,CAAC;EAC/B,OAAO,CAACQ,IAAI,EAAEC,KAAK,KAAKC,KAAK,IAAI;IAC/BS,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACX,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC/GH,QAAQ,CAACL,uBAAuB,CAAC;MAC/BS,WAAW,EAAEC,MAAM,CAACH,KAAK,CAAC;MAC1BI,aAAa,EAAEP,OAAO;MACtBQ,gBAAgB,EAAEN,IAAI,CAACO;IACzB,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport { computePieSectors } from '../../polar/Pie';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getTooltipNameProp, getValueByDataKey } from '../../util/ChartUtils';\nimport { selectUnfilteredPolarItems } from './polarSelectors';\nvar pickId = (_state, id) => id;\nvar selectSynchronisedPieSettings = createSelector([selectUnfilteredPolarItems, pickId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'pie').find(item => item.id === id));\n\n// Keep stable reference to an empty array to prevent re-renders\nvar emptyArray = [];\nvar pickCells = (_state, _id, cells) => {\n  if ((cells === null || cells === void 0 ? void 0 : cells.length) === 0) {\n    return emptyArray;\n  }\n  return cells;\n};\nexport var selectDisplayedData = createSelector([selectChartDataAndAlwaysIgnoreIndexes, selectSynchronisedPieSettings, pickCells], (_ref, pieSettings, cells) => {\n  var {\n    chartData\n  } = _ref;\n  if (pieSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((pieSettings === null || pieSettings === void 0 ? void 0 : pieSettings.data) != null && pieSettings.data.length > 0) {\n    displayedData = pieSettings.data;\n  } else {\n    displayedData = chartData;\n  }\n  if ((!displayedData || !displayedData.length) && cells != null) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, pieSettings.presentationProps), cell.props));\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData;\n});\nexport var selectPieLegend = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells], (displayedData, pieSettings, cells) => {\n  if (displayedData == null || pieSettings == null) {\n    return undefined;\n  }\n  return displayedData.map((entry, i) => {\n    var _cells$i;\n    var name = getValueByDataKey(entry, pieSettings.nameKey, pieSettings.name);\n    var color;\n    if (cells !== null && cells !== void 0 && (_cells$i = cells[i]) !== null && _cells$i !== void 0 && (_cells$i = _cells$i.props) !== null && _cells$i !== void 0 && _cells$i.fill) {\n      color = cells[i].props.fill;\n    } else if (typeof entry === 'object' && entry != null && 'fill' in entry) {\n      color = entry.fill;\n    } else {\n      color = pieSettings.fill;\n    }\n    return {\n      value: getTooltipNameProp(name, pieSettings.dataKey),\n      color,\n      payload: entry,\n      type: pieSettings.legendType\n    };\n  });\n});\nexport var selectPieSectors = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells, selectChartOffsetInternal], (displayedData, pieSettings, cells, offset) => {\n  if (pieSettings == null || displayedData == null) {\n    return undefined;\n  }\n  return computePieSectors({\n    offset,\n    pieSettings,\n    displayedData,\n    cells\n  });\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "computePieSectors", "selectChartDataAndAlwaysIgnoreIndexes", "selectChartOffsetInternal", "getTooltipNameProp", "getValueByDataKey", "selectUnfilteredPolarItems", "pickId", "_state", "id", "selectSynchronisedPieSettings", "graphicalItems", "item", "type", "find", "emptyArray", "pick<PERSON>ells", "_id", "cells", "selectDisplayedData", "_ref", "pieSettings", "chartData", "undefined", "displayedData", "data", "map", "cell", "presentationProps", "props", "selectPieLegend", "entry", "_cells$i", "name", "<PERSON><PERSON><PERSON>", "color", "fill", "dataKey", "payload", "legendType", "selectPieSectors", "offset"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/pieSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { computePieSectors } from '../../polar/Pie';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getTooltipNameProp, getValueByDataKey } from '../../util/ChartUtils';\nimport { selectUnfilteredPolarItems } from './polarSelectors';\nvar pickId = (_state, id) => id;\nvar selectSynchronisedPieSettings = createSelector([selectUnfilteredPolarItems, pickId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'pie').find(item => item.id === id));\n\n// Keep stable reference to an empty array to prevent re-renders\nvar emptyArray = [];\nvar pickCells = (_state, _id, cells) => {\n  if ((cells === null || cells === void 0 ? void 0 : cells.length) === 0) {\n    return emptyArray;\n  }\n  return cells;\n};\nexport var selectDisplayedData = createSelector([selectChartDataAndAlwaysIgnoreIndexes, selectSynchronisedPieSettings, pickCells], (_ref, pieSettings, cells) => {\n  var {\n    chartData\n  } = _ref;\n  if (pieSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((pieSettings === null || pieSettings === void 0 ? void 0 : pieSettings.data) != null && pieSettings.data.length > 0) {\n    displayedData = pieSettings.data;\n  } else {\n    displayedData = chartData;\n  }\n  if ((!displayedData || !displayedData.length) && cells != null) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, pieSettings.presentationProps), cell.props));\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData;\n});\nexport var selectPieLegend = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells], (displayedData, pieSettings, cells) => {\n  if (displayedData == null || pieSettings == null) {\n    return undefined;\n  }\n  return displayedData.map((entry, i) => {\n    var _cells$i;\n    var name = getValueByDataKey(entry, pieSettings.nameKey, pieSettings.name);\n    var color;\n    if (cells !== null && cells !== void 0 && (_cells$i = cells[i]) !== null && _cells$i !== void 0 && (_cells$i = _cells$i.props) !== null && _cells$i !== void 0 && _cells$i.fill) {\n      color = cells[i].props.fill;\n    } else if (typeof entry === 'object' && entry != null && 'fill' in entry) {\n      color = entry.fill;\n    } else {\n      color = pieSettings.fill;\n    }\n    return {\n      value: getTooltipNameProp(name, pieSettings.dataKey),\n      color,\n      payload: entry,\n      type: pieSettings.legendType\n    };\n  });\n});\nexport var selectPieSectors = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells, selectChartOffsetInternal], (displayedData, pieSettings, cells, offset) => {\n  if (pieSettings == null || displayedData == null) {\n    return undefined;\n  }\n  return computePieSectors({\n    offset,\n    pieSettings,\n    displayedData,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,qCAAqC,QAAQ,iBAAiB;AACvE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC7E,SAASC,0BAA0B,QAAQ,kBAAkB;AAC7D,IAAIC,MAAM,GAAGA,CAACC,MAAM,EAAEC,EAAE,KAAKA,EAAE;AAC/B,IAAIC,6BAA6B,GAAGV,cAAc,CAAC,CAACM,0BAA0B,EAAEC,MAAM,CAAC,EAAE,CAACI,cAAc,EAAEF,EAAE,KAAKE,cAAc,CAACpC,MAAM,CAACqC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,KAAK,CAAC,CAACC,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACH,EAAE,KAAKA,EAAE,CAAC,CAAC;;AAEjM;AACA,IAAIM,UAAU,GAAG,EAAE;AACnB,IAAIC,SAAS,GAAGA,CAACR,MAAM,EAAES,GAAG,EAAEC,KAAK,KAAK;EACtC,IAAI,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACpC,MAAM,MAAM,CAAC,EAAE;IACtE,OAAOiC,UAAU;EACnB;EACA,OAAOG,KAAK;AACd,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGnB,cAAc,CAAC,CAACE,qCAAqC,EAAEQ,6BAA6B,EAAEM,SAAS,CAAC,EAAE,CAACI,IAAI,EAAEC,WAAW,EAAEH,KAAK,KAAK;EAC/J,IAAI;IACFI;EACF,CAAC,GAAGF,IAAI;EACR,IAAIC,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOE,SAAS;EAClB;EACA,IAAIC,aAAa;EACjB,IAAI,CAACH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,IAAI,KAAK,IAAI,IAAIJ,WAAW,CAACI,IAAI,CAAC3C,MAAM,GAAG,CAAC,EAAE;IACvH0C,aAAa,GAAGH,WAAW,CAACI,IAAI;EAClC,CAAC,MAAM;IACLD,aAAa,GAAGF,SAAS;EAC3B;EACA,IAAI,CAAC,CAACE,aAAa,IAAI,CAACA,aAAa,CAAC1C,MAAM,KAAKoC,KAAK,IAAI,IAAI,EAAE;IAC9DM,aAAa,GAAGN,KAAK,CAACQ,GAAG,CAACC,IAAI,IAAI/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,WAAW,CAACO,iBAAiB,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,CAAC;EAChH;EACA,IAAIL,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOD,SAAS;EAClB;EACA,OAAOC,aAAa;AACtB,CAAC,CAAC;AACF,OAAO,IAAIM,eAAe,GAAG9B,cAAc,CAAC,CAACmB,mBAAmB,EAAET,6BAA6B,EAAEM,SAAS,CAAC,EAAE,CAACQ,aAAa,EAAEH,WAAW,EAAEH,KAAK,KAAK;EAClJ,IAAIM,aAAa,IAAI,IAAI,IAAIH,WAAW,IAAI,IAAI,EAAE;IAChD,OAAOE,SAAS;EAClB;EACA,OAAOC,aAAa,CAACE,GAAG,CAAC,CAACK,KAAK,EAAEvC,CAAC,KAAK;IACrC,IAAIwC,QAAQ;IACZ,IAAIC,IAAI,GAAG5B,iBAAiB,CAAC0B,KAAK,EAAEV,WAAW,CAACa,OAAO,EAAEb,WAAW,CAACY,IAAI,CAAC;IAC1E,IAAIE,KAAK;IACT,IAAIjB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAI,CAACc,QAAQ,GAAGd,KAAK,CAAC1B,CAAC,CAAC,MAAM,IAAI,IAAIwC,QAAQ,KAAK,KAAK,CAAC,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACH,KAAK,MAAM,IAAI,IAAIG,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACI,IAAI,EAAE;MAC/KD,KAAK,GAAGjB,KAAK,CAAC1B,CAAC,CAAC,CAACqC,KAAK,CAACO,IAAI;IAC7B,CAAC,MAAM,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,IAAI,MAAM,IAAIA,KAAK,EAAE;MACxEI,KAAK,GAAGJ,KAAK,CAACK,IAAI;IACpB,CAAC,MAAM;MACLD,KAAK,GAAGd,WAAW,CAACe,IAAI;IAC1B;IACA,OAAO;MACL/C,KAAK,EAAEe,kBAAkB,CAAC6B,IAAI,EAAEZ,WAAW,CAACgB,OAAO,CAAC;MACpDF,KAAK;MACLG,OAAO,EAAEP,KAAK;MACdlB,IAAI,EAAEQ,WAAW,CAACkB;IACpB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,OAAO,IAAIC,gBAAgB,GAAGxC,cAAc,CAAC,CAACmB,mBAAmB,EAAET,6BAA6B,EAAEM,SAAS,EAAEb,yBAAyB,CAAC,EAAE,CAACqB,aAAa,EAAEH,WAAW,EAAEH,KAAK,EAAEuB,MAAM,KAAK;EACtL,IAAIpB,WAAW,IAAI,IAAI,IAAIG,aAAa,IAAI,IAAI,EAAE;IAChD,OAAOD,SAAS;EAClB;EACA,OAAOtB,iBAAiB,CAAC;IACvBwC,MAAM;IACNpB,WAAW;IACXG,aAAa;IACbN;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst iteratee = require('../util/iteratee.js');\nfunction sumBy(array, iteratee$1) {\n  if (!array || !array.length) {\n    return 0;\n  }\n  if (iteratee$1 != null) {\n    iteratee$1 = iteratee.iteratee(iteratee$1);\n  }\n  let result = undefined;\n  for (let i = 0; i < array.length; i++) {\n    const current = iteratee$1 ? iteratee$1(array[i]) : array[i];\n    if (current !== undefined) {\n      if (result === undefined) {\n        result = current;\n      } else {\n        result += current;\n      }\n    }\n  }\n  return result;\n}\nexports.sumBy = sumBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "iteratee", "require", "sumBy", "array", "iteratee$1", "length", "result", "undefined", "i", "current"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/math/sumBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst iteratee = require('../util/iteratee.js');\n\nfunction sumBy(array, iteratee$1) {\n    if (!array || !array.length) {\n        return 0;\n    }\n    if (iteratee$1 != null) {\n        iteratee$1 = iteratee.iteratee(iteratee$1);\n    }\n    let result = undefined;\n    for (let i = 0; i < array.length; i++) {\n        const current = iteratee$1 ? iteratee$1(array[i]) : array[i];\n        if (current !== undefined) {\n            if (result === undefined) {\n                result = current;\n            }\n            else {\n                result += current;\n            }\n        }\n    }\n    return result;\n}\n\nexports.sumBy = sumBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASC,KAAKA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC9B,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,EAAE;IACzB,OAAO,CAAC;EACZ;EACA,IAAID,UAAU,IAAI,IAAI,EAAE;IACpBA,UAAU,GAAGJ,QAAQ,CAACA,QAAQ,CAACI,UAAU,CAAC;EAC9C;EACA,IAAIE,MAAM,GAAGC,SAAS;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACE,MAAM,EAAEG,CAAC,EAAE,EAAE;IACnC,MAAMC,OAAO,GAAGL,UAAU,GAAGA,UAAU,CAACD,KAAK,CAACK,CAAC,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,CAAC;IAC5D,IAAIC,OAAO,KAAKF,SAAS,EAAE;MACvB,IAAID,MAAM,KAAKC,SAAS,EAAE;QACtBD,MAAM,GAAGG,OAAO;MACpB,CAAC,MACI;QACDH,MAAM,IAAIG,OAAO;MACrB;IACJ;EACJ;EACA,OAAOH,MAAM;AACjB;AAEAV,OAAO,CAACM,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
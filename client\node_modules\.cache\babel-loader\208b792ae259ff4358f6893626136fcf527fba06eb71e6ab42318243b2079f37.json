{"ast": null, "code": "function upperFirst(str) {\n  return str.substring(0, 1).toUpperCase() + str.substring(1);\n}\nexport { upperFirst };", "map": {"version": 3, "names": ["upperFirst", "str", "substring", "toUpperCase"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/upperFirst.mjs"], "sourcesContent": ["function upperFirst(str) {\n    return str.substring(0, 1).toUpperCase() + str.substring(1);\n}\n\nexport { upperFirst };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC;AAC/D;AAEA,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * Simple LRU (Least Recently Used) cache implementation\n */\nexport class LRUCache {\n  constructor(maxSize) {\n    _defineProperty(this, \"cache\", new Map());\n    this.maxSize = maxSize;\n  }\n  get(key) {\n    var value = this.cache.get(key);\n    if (value !== undefined) {\n      this.cache.delete(key);\n      this.cache.set(key, value);\n    }\n    return value;\n  }\n  set(key, value) {\n    if (this.cache.has(key)) {\n      this.cache.delete(key);\n    } else if (this.cache.size >= this.maxSize) {\n      var firstKey = this.cache.keys().next().value;\n      this.cache.delete(firstKey);\n    }\n    this.cache.set(key, value);\n  }\n  clear() {\n    this.cache.clear();\n  }\n  size() {\n    return this.cache.size;\n  }\n}", "map": {"version": 3, "names": ["_defineProperty", "e", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "L<PERSON><PERSON><PERSON>", "constructor", "maxSize", "Map", "get", "key", "cache", "undefined", "delete", "set", "has", "size", "firstKey", "keys", "next", "clear"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/LRUCache.js"], "sourcesContent": ["function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * Simple LRU (Least Recently Used) cache implementation\n */\nexport class LRUCache {\n  constructor(maxSize) {\n    _defineProperty(this, \"cache\", new Map());\n    this.maxSize = maxSize;\n  }\n  get(key) {\n    var value = this.cache.get(key);\n    if (value !== undefined) {\n      this.cache.delete(key);\n      this.cache.set(key, value);\n    }\n    return value;\n  }\n  set(key, value) {\n    if (this.cache.has(key)) {\n      this.cache.delete(key);\n    } else if (this.cache.size >= this.maxSize) {\n      var firstKey = this.cache.keys().next().value;\n      this.cache.delete(firstKey);\n    }\n    this.cache.set(key, value);\n  }\n  clear() {\n    this.cache.clear();\n  }\n  size() {\n    return this.cache.size;\n  }\n}"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGE,cAAc,CAACF,CAAC,CAAC,KAAKD,CAAC,GAAGI,MAAM,CAACC,cAAc,CAACL,CAAC,EAAEC,CAAC,EAAE;IAAEK,KAAK,EAAEJ,CAAC;IAAEK,UAAU,EAAE,CAAC,CAAC;IAAEC,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGT,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASG,cAAcA,CAACD,CAAC,EAAE;EAAE,IAAIQ,CAAC,GAAGC,YAAY,CAACT,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOQ,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACT,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACU,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKb,CAAC,EAAE;IAAE,IAAIU,CAAC,GAAGV,CAAC,CAACc,IAAI,CAACZ,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOS,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKd,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AACvT;AACA;AACA;AACA,OAAO,MAAMgB,QAAQ,CAAC;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACnBrB,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAIsB,GAAG,CAAC,CAAC,CAAC;IACzC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACxB;EACAE,GAAGA,CAACC,GAAG,EAAE;IACP,IAAIjB,KAAK,GAAG,IAAI,CAACkB,KAAK,CAACF,GAAG,CAACC,GAAG,CAAC;IAC/B,IAAIjB,KAAK,KAAKmB,SAAS,EAAE;MACvB,IAAI,CAACD,KAAK,CAACE,MAAM,CAACH,GAAG,CAAC;MACtB,IAAI,CAACC,KAAK,CAACG,GAAG,CAACJ,GAAG,EAAEjB,KAAK,CAAC;IAC5B;IACA,OAAOA,KAAK;EACd;EACAqB,GAAGA,CAACJ,GAAG,EAAEjB,KAAK,EAAE;IACd,IAAI,IAAI,CAACkB,KAAK,CAACI,GAAG,CAACL,GAAG,CAAC,EAAE;MACvB,IAAI,CAACC,KAAK,CAACE,MAAM,CAACH,GAAG,CAAC;IACxB,CAAC,MAAM,IAAI,IAAI,CAACC,KAAK,CAACK,IAAI,IAAI,IAAI,CAACT,OAAO,EAAE;MAC1C,IAAIU,QAAQ,GAAG,IAAI,CAACN,KAAK,CAACO,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC1B,KAAK;MAC7C,IAAI,CAACkB,KAAK,CAACE,MAAM,CAACI,QAAQ,CAAC;IAC7B;IACA,IAAI,CAACN,KAAK,CAACG,GAAG,CAACJ,GAAG,EAAEjB,KAAK,CAAC;EAC5B;EACA2B,KAAKA,CAAA,EAAG;IACN,IAAI,CAACT,KAAK,CAACS,KAAK,CAAC,CAAC;EACpB;EACAJ,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACL,KAAK,CAACK,IAAI;EACxB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
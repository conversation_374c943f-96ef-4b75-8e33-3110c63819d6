{"ast": null, "code": "function isWeakSet(value) {\n  return value instanceof WeakSet;\n}\nexport { isWeakSet };", "map": {"version": 3, "names": ["isWeakSet", "value", "WeakSet"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/predicate/isWeakSet.mjs"], "sourcesContent": ["function isWeakSet(value) {\n    return value instanceof WeakSet;\n}\n\nexport { isWeakSet };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,KAAK,EAAE;EACtB,OAAOA,KAAK,YAAYC,OAAO;AACnC;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
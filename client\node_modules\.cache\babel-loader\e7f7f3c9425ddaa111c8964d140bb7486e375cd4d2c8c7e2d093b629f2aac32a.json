{"ast": null, "code": "function partial(func, ...partialArgs) {\n  return partialImpl(func, placeholderSymbol, ...partialArgs);\n}\nfunction partialImpl(func, placeholder, ...partialArgs) {\n  const partialed = function (...providedArgs) {\n    let providedArgsIndex = 0;\n    const substitutedArgs = partialArgs.slice().map(arg => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);\n    const remainingArgs = providedArgs.slice(providedArgsIndex);\n    return func.apply(this, substitutedArgs.concat(remainingArgs));\n  };\n  if (func.prototype) {\n    partialed.prototype = Object.create(func.prototype);\n  }\n  return partialed;\n}\nconst placeholderSymbol = Symbol('partial.placeholder');\npartial.placeholder = placeholderSymbol;\nexport { partial, partialImpl };", "map": {"version": 3, "names": ["partial", "func", "partialArgs", "partialImpl", "placeholderSymbol", "placeholder", "partialed", "provided<PERSON><PERSON><PERSON>", "providedArgsIndex", "<PERSON><PERSON><PERSON><PERSON>", "slice", "map", "arg", "remainingArgs", "apply", "concat", "prototype", "Object", "create", "Symbol"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/partial.mjs"], "sourcesContent": ["function partial(func, ...partialArgs) {\n    return partialImpl(func, placeholderSymbol, ...partialArgs);\n}\nfunction partialImpl(func, placeholder, ...partialArgs) {\n    const partialed = function (...providedArgs) {\n        let providedArgsIndex = 0;\n        const substitutedArgs = partialArgs\n            .slice()\n            .map(arg => (arg === placeholder ? providedArgs[providedArgsIndex++] : arg));\n        const remainingArgs = providedArgs.slice(providedArgsIndex);\n        return func.apply(this, substitutedArgs.concat(remainingArgs));\n    };\n    if (func.prototype) {\n        partialed.prototype = Object.create(func.prototype);\n    }\n    return partialed;\n}\nconst placeholderSymbol = Symbol('partial.placeholder');\npartial.placeholder = placeholderSymbol;\n\nexport { partial, partialImpl };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,IAAI,EAAE,GAAGC,WAAW,EAAE;EACnC,OAAOC,WAAW,CAACF,IAAI,EAAEG,iBAAiB,EAAE,GAAGF,WAAW,CAAC;AAC/D;AACA,SAASC,WAAWA,CAACF,IAAI,EAAEI,WAAW,EAAE,GAAGH,WAAW,EAAE;EACpD,MAAMI,SAAS,GAAG,SAAAA,CAAU,GAAGC,YAAY,EAAE;IACzC,IAAIC,iBAAiB,GAAG,CAAC;IACzB,MAAMC,eAAe,GAAGP,WAAW,CAC9BQ,KAAK,CAAC,CAAC,CACPC,GAAG,CAACC,GAAG,IAAKA,GAAG,KAAKP,WAAW,GAAGE,YAAY,CAACC,iBAAiB,EAAE,CAAC,GAAGI,GAAI,CAAC;IAChF,MAAMC,aAAa,GAAGN,YAAY,CAACG,KAAK,CAACF,iBAAiB,CAAC;IAC3D,OAAOP,IAAI,CAACa,KAAK,CAAC,IAAI,EAAEL,eAAe,CAACM,MAAM,CAACF,aAAa,CAAC,CAAC;EAClE,CAAC;EACD,IAAIZ,IAAI,CAACe,SAAS,EAAE;IAChBV,SAAS,CAACU,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACjB,IAAI,CAACe,SAAS,CAAC;EACvD;EACA,OAAOV,SAAS;AACpB;AACA,MAAMF,iBAAiB,GAAGe,MAAM,CAAC,qBAAqB,CAAC;AACvDnB,OAAO,CAACK,WAAW,GAAGD,iBAAiB;AAEvC,SAASJ,OAAO,EAAEG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
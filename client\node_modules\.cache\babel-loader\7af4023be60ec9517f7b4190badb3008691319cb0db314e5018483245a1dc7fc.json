{"ast": null, "code": "import { clsx } from 'clsx';\nimport { isNumber } from '../DataUtils';\nvar CSS_CLASS_PREFIX = 'recharts-tooltip-wrapper';\nvar TOOLTIP_HIDDEN = {\n  visibility: 'hidden'\n};\nexport function getTooltipCSSClassName(_ref) {\n  var {\n    coordinate,\n    translateX,\n    translateY\n  } = _ref;\n  return clsx(CSS_CLASS_PREFIX, {\n    [\"\".concat(CSS_CLASS_PREFIX, \"-right\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX >= coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-left\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX < coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-bottom\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY >= coordinate.y,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-top\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY < coordinate.y\n  });\n}\nexport function getTooltipTranslateXY(_ref2) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    key,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipDimension,\n    viewBox,\n    viewBoxDimension\n  } = _ref2;\n  if (position && isNumber(position[key])) {\n    return position[key];\n  }\n  var negative = coordinate[key] - tooltipDimension - (offsetTopLeft > 0 ? offsetTopLeft : 0);\n  var positive = coordinate[key] + offsetTopLeft;\n  if (allowEscapeViewBox[key]) {\n    return reverseDirection[key] ? negative : positive;\n  }\n  var viewBoxKey = viewBox[key];\n  if (viewBoxKey == null) {\n    return 0;\n  }\n  if (reverseDirection[key]) {\n    var _tooltipBoundary = negative;\n    var _viewBoxBoundary = viewBoxKey;\n    if (_tooltipBoundary < _viewBoxBoundary) {\n      return Math.max(positive, viewBoxKey);\n    }\n    return Math.max(negative, viewBoxKey);\n  }\n  if (viewBoxDimension == null) {\n    return 0;\n  }\n  var tooltipBoundary = positive + tooltipDimension;\n  var viewBoxBoundary = viewBoxKey + viewBoxDimension;\n  if (tooltipBoundary > viewBoxBoundary) {\n    return Math.max(negative, viewBoxKey);\n  }\n  return Math.max(positive, viewBoxKey);\n}\nexport function getTransformStyle(_ref3) {\n  var {\n    translateX,\n    translateY,\n    useTranslate3d\n  } = _ref3;\n  return {\n    transform: useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n  };\n}\nexport function getTooltipTranslate(_ref4) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipBox,\n    useTranslate3d,\n    viewBox\n  } = _ref4;\n  var cssProperties, translateX, translateY;\n  if (tooltipBox.height > 0 && tooltipBox.width > 0 && coordinate) {\n    translateX = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'x',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.width,\n      viewBox,\n      viewBoxDimension: viewBox.width\n    });\n    translateY = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'y',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.height,\n      viewBox,\n      viewBoxDimension: viewBox.height\n    });\n    cssProperties = getTransformStyle({\n      translateX,\n      translateY,\n      useTranslate3d\n    });\n  } else {\n    cssProperties = TOOLTIP_HIDDEN;\n  }\n  return {\n    cssProperties,\n    cssClasses: getTooltipCSSClassName({\n      translateX,\n      translateY,\n      coordinate\n    })\n  };\n}", "map": {"version": 3, "names": ["clsx", "isNumber", "CSS_CLASS_PREFIX", "TOOLTIP_HIDDEN", "visibility", "getTooltipCSSClassName", "_ref", "coordinate", "translateX", "translateY", "concat", "x", "y", "getTooltipTranslateXY", "_ref2", "allowEscapeViewBox", "key", "offsetTopLeft", "position", "reverseDirection", "tooltipDimension", "viewBox", "viewBoxDimension", "negative", "positive", "viewBoxKey", "_tooltipBoundary", "_viewBoxBoundary", "Math", "max", "tooltipBoundary", "viewBoxBoundary", "getTransformStyle", "_ref3", "useTranslate3d", "transform", "getTooltipTranslate", "_ref4", "tooltipBox", "cssProperties", "height", "width", "cssClasses"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/tooltip/translate.js"], "sourcesContent": ["import { clsx } from 'clsx';\nimport { isNumber } from '../DataUtils';\nvar CSS_CLASS_PREFIX = 'recharts-tooltip-wrapper';\nvar TOOLTIP_HIDDEN = {\n  visibility: 'hidden'\n};\nexport function getTooltipCSSClassName(_ref) {\n  var {\n    coordinate,\n    translateX,\n    translateY\n  } = _ref;\n  return clsx(CSS_CLASS_PREFIX, {\n    [\"\".concat(CSS_CLASS_PREFIX, \"-right\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX >= coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-left\")]: isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX < coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-bottom\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY >= coordinate.y,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-top\")]: isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY < coordinate.y\n  });\n}\nexport function getTooltipTranslateXY(_ref2) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    key,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipDimension,\n    viewBox,\n    viewBoxDimension\n  } = _ref2;\n  if (position && isNumber(position[key])) {\n    return position[key];\n  }\n  var negative = coordinate[key] - tooltipDimension - (offsetTopLeft > 0 ? offsetTopLeft : 0);\n  var positive = coordinate[key] + offsetTopLeft;\n  if (allowEscapeViewBox[key]) {\n    return reverseDirection[key] ? negative : positive;\n  }\n  var viewBoxKey = viewBox[key];\n  if (viewBoxKey == null) {\n    return 0;\n  }\n  if (reverseDirection[key]) {\n    var _tooltipBoundary = negative;\n    var _viewBoxBoundary = viewBoxKey;\n    if (_tooltipBoundary < _viewBoxBoundary) {\n      return Math.max(positive, viewBoxKey);\n    }\n    return Math.max(negative, viewBoxKey);\n  }\n  if (viewBoxDimension == null) {\n    return 0;\n  }\n  var tooltipBoundary = positive + tooltipDimension;\n  var viewBoxBoundary = viewBoxKey + viewBoxDimension;\n  if (tooltipBoundary > viewBoxBoundary) {\n    return Math.max(negative, viewBoxKey);\n  }\n  return Math.max(positive, viewBoxKey);\n}\nexport function getTransformStyle(_ref3) {\n  var {\n    translateX,\n    translateY,\n    useTranslate3d\n  } = _ref3;\n  return {\n    transform: useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n  };\n}\nexport function getTooltipTranslate(_ref4) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipBox,\n    useTranslate3d,\n    viewBox\n  } = _ref4;\n  var cssProperties, translateX, translateY;\n  if (tooltipBox.height > 0 && tooltipBox.width > 0 && coordinate) {\n    translateX = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'x',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.width,\n      viewBox,\n      viewBoxDimension: viewBox.width\n    });\n    translateY = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'y',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.height,\n      viewBox,\n      viewBoxDimension: viewBox.height\n    });\n    cssProperties = getTransformStyle({\n      translateX,\n      translateY,\n      useTranslate3d\n    });\n  } else {\n    cssProperties = TOOLTIP_HIDDEN;\n  }\n  return {\n    cssProperties,\n    cssClasses: getTooltipCSSClassName({\n      translateX,\n      translateY,\n      coordinate\n    })\n  };\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,MAAM;AAC3B,SAASC,QAAQ,QAAQ,cAAc;AACvC,IAAIC,gBAAgB,GAAG,0BAA0B;AACjD,IAAIC,cAAc,GAAG;EACnBC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,IAAI;IACFC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGH,IAAI;EACR,OAAON,IAAI,CAACE,gBAAgB,EAAE;IAC5B,CAAC,EAAE,CAACQ,MAAM,CAACR,gBAAgB,EAAE,QAAQ,CAAC,GAAGD,QAAQ,CAACO,UAAU,CAAC,IAAID,UAAU,IAAIN,QAAQ,CAACM,UAAU,CAACI,CAAC,CAAC,IAAIH,UAAU,IAAID,UAAU,CAACI,CAAC;IACnI,CAAC,EAAE,CAACD,MAAM,CAACR,gBAAgB,EAAE,OAAO,CAAC,GAAGD,QAAQ,CAACO,UAAU,CAAC,IAAID,UAAU,IAAIN,QAAQ,CAACM,UAAU,CAACI,CAAC,CAAC,IAAIH,UAAU,GAAGD,UAAU,CAACI,CAAC;IACjI,CAAC,EAAE,CAACD,MAAM,CAACR,gBAAgB,EAAE,SAAS,CAAC,GAAGD,QAAQ,CAACQ,UAAU,CAAC,IAAIF,UAAU,IAAIN,QAAQ,CAACM,UAAU,CAACK,CAAC,CAAC,IAAIH,UAAU,IAAIF,UAAU,CAACK,CAAC;IACpI,CAAC,EAAE,CAACF,MAAM,CAACR,gBAAgB,EAAE,MAAM,CAAC,GAAGD,QAAQ,CAACQ,UAAU,CAAC,IAAIF,UAAU,IAAIN,QAAQ,CAACM,UAAU,CAACK,CAAC,CAAC,IAAIH,UAAU,GAAGF,UAAU,CAACK;EACjI,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,IAAI;IACFC,kBAAkB;IAClBR,UAAU;IACVS,GAAG;IACHC,aAAa;IACbC,QAAQ;IACRC,gBAAgB;IAChBC,gBAAgB;IAChBC,OAAO;IACPC;EACF,CAAC,GAAGR,KAAK;EACT,IAAII,QAAQ,IAAIjB,QAAQ,CAACiB,QAAQ,CAACF,GAAG,CAAC,CAAC,EAAE;IACvC,OAAOE,QAAQ,CAACF,GAAG,CAAC;EACtB;EACA,IAAIO,QAAQ,GAAGhB,UAAU,CAACS,GAAG,CAAC,GAAGI,gBAAgB,IAAIH,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;EAC3F,IAAIO,QAAQ,GAAGjB,UAAU,CAACS,GAAG,CAAC,GAAGC,aAAa;EAC9C,IAAIF,kBAAkB,CAACC,GAAG,CAAC,EAAE;IAC3B,OAAOG,gBAAgB,CAACH,GAAG,CAAC,GAAGO,QAAQ,GAAGC,QAAQ;EACpD;EACA,IAAIC,UAAU,GAAGJ,OAAO,CAACL,GAAG,CAAC;EAC7B,IAAIS,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO,CAAC;EACV;EACA,IAAIN,gBAAgB,CAACH,GAAG,CAAC,EAAE;IACzB,IAAIU,gBAAgB,GAAGH,QAAQ;IAC/B,IAAII,gBAAgB,GAAGF,UAAU;IACjC,IAAIC,gBAAgB,GAAGC,gBAAgB,EAAE;MACvC,OAAOC,IAAI,CAACC,GAAG,CAACL,QAAQ,EAAEC,UAAU,CAAC;IACvC;IACA,OAAOG,IAAI,CAACC,GAAG,CAACN,QAAQ,EAAEE,UAAU,CAAC;EACvC;EACA,IAAIH,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAO,CAAC;EACV;EACA,IAAIQ,eAAe,GAAGN,QAAQ,GAAGJ,gBAAgB;EACjD,IAAIW,eAAe,GAAGN,UAAU,GAAGH,gBAAgB;EACnD,IAAIQ,eAAe,GAAGC,eAAe,EAAE;IACrC,OAAOH,IAAI,CAACC,GAAG,CAACN,QAAQ,EAAEE,UAAU,CAAC;EACvC;EACA,OAAOG,IAAI,CAACC,GAAG,CAACL,QAAQ,EAAEC,UAAU,CAAC;AACvC;AACA,OAAO,SAASO,iBAAiBA,CAACC,KAAK,EAAE;EACvC,IAAI;IACFzB,UAAU;IACVC,UAAU;IACVyB;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACLE,SAAS,EAAED,cAAc,GAAG,cAAc,CAACxB,MAAM,CAACF,UAAU,EAAE,MAAM,CAAC,CAACE,MAAM,CAACD,UAAU,EAAE,QAAQ,CAAC,GAAG,YAAY,CAACC,MAAM,CAACF,UAAU,EAAE,MAAM,CAAC,CAACE,MAAM,CAACD,UAAU,EAAE,KAAK;EACvK,CAAC;AACH;AACA,OAAO,SAAS2B,mBAAmBA,CAACC,KAAK,EAAE;EACzC,IAAI;IACFtB,kBAAkB;IAClBR,UAAU;IACVU,aAAa;IACbC,QAAQ;IACRC,gBAAgB;IAChBmB,UAAU;IACVJ,cAAc;IACdb;EACF,CAAC,GAAGgB,KAAK;EACT,IAAIE,aAAa,EAAE/B,UAAU,EAAEC,UAAU;EACzC,IAAI6B,UAAU,CAACE,MAAM,GAAG,CAAC,IAAIF,UAAU,CAACG,KAAK,GAAG,CAAC,IAAIlC,UAAU,EAAE;IAC/DC,UAAU,GAAGK,qBAAqB,CAAC;MACjCE,kBAAkB;MAClBR,UAAU;MACVS,GAAG,EAAE,GAAG;MACRC,aAAa;MACbC,QAAQ;MACRC,gBAAgB;MAChBC,gBAAgB,EAAEkB,UAAU,CAACG,KAAK;MAClCpB,OAAO;MACPC,gBAAgB,EAAED,OAAO,CAACoB;IAC5B,CAAC,CAAC;IACFhC,UAAU,GAAGI,qBAAqB,CAAC;MACjCE,kBAAkB;MAClBR,UAAU;MACVS,GAAG,EAAE,GAAG;MACRC,aAAa;MACbC,QAAQ;MACRC,gBAAgB;MAChBC,gBAAgB,EAAEkB,UAAU,CAACE,MAAM;MACnCnB,OAAO;MACPC,gBAAgB,EAAED,OAAO,CAACmB;IAC5B,CAAC,CAAC;IACFD,aAAa,GAAGP,iBAAiB,CAAC;MAChCxB,UAAU;MACVC,UAAU;MACVyB;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACLK,aAAa,GAAGpC,cAAc;EAChC;EACA,OAAO;IACLoC,aAAa;IACbG,UAAU,EAAErC,sBAAsB,CAAC;MACjCG,UAAU;MACVC,UAAU;MACVF;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
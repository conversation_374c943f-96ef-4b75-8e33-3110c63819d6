{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction maxBy(items, getValue) {\n  if (items.length === 0) {\n    return undefined;\n  }\n  let maxElement = items[0];\n  let max = getValue(maxElement);\n  for (let i = 1; i < items.length; i++) {\n    const element = items[i];\n    const value = getValue(element);\n    if (value > max) {\n      max = value;\n      maxElement = element;\n    }\n  }\n  return maxElement;\n}\nexports.maxBy = maxBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "maxBy", "items", "getValue", "length", "undefined", "maxElement", "max", "i", "element"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/maxBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction maxBy(items, getValue) {\n    if (items.length === 0) {\n        return undefined;\n    }\n    let maxElement = items[0];\n    let max = getValue(maxElement);\n    for (let i = 1; i < items.length; i++) {\n        const element = items[i];\n        const value = getValue(element);\n        if (value > max) {\n            max = value;\n            maxElement = element;\n        }\n    }\n    return maxElement;\n}\n\nexports.maxBy = maxBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC5B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOC,SAAS;EACpB;EACA,IAAIC,UAAU,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACzB,IAAIK,GAAG,GAAGJ,QAAQ,CAACG,UAAU,CAAC;EAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACE,MAAM,EAAEI,CAAC,EAAE,EAAE;IACnC,MAAMC,OAAO,GAAGP,KAAK,CAACM,CAAC,CAAC;IACxB,MAAMR,KAAK,GAAGG,QAAQ,CAACM,OAAO,CAAC;IAC/B,IAAIT,KAAK,GAAGO,GAAG,EAAE;MACbA,GAAG,GAAGP,KAAK;MACXM,UAAU,GAAGG,OAAO;IACxB;EACJ;EACA,OAAOH,UAAU;AACrB;AAEAT,OAAO,CAACI,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
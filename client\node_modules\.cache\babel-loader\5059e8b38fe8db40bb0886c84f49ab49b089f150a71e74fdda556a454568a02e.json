{"ast": null, "code": "/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nexport function isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}", "map": {"version": 3, "names": ["isStacked", "graphicalItem", "stackId", "dataKey"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/types/StackedGraphicalItem.js"], "sourcesContent": ["/**\n * Some graphical items allow data stacking. The stacks are optional,\n * so all props here are optional too.\n */\n\n/**\n * Some graphical items allow data stacking.\n * This interface is used to represent the items that are stacked\n * because the user has provided the stackId and dataKey properties.\n */\n\nexport function isStacked(graphicalItem) {\n  return graphicalItem.stackId != null && graphicalItem.dataKey != null;\n}"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASA,SAASA,CAACC,aAAa,EAAE;EACvC,OAAOA,aAAa,CAACC,OAAO,IAAI,IAAI,IAAID,aAAa,CAACE,OAAO,IAAI,IAAI;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
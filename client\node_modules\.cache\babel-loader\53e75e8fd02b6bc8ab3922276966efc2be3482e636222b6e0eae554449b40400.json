{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport { computeFunnelTrapezoids } from '../../cartesian/Funnel';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nvar pickFunnelSettings = (_state, funnelSettings) => funnelSettings;\nexport var selectFunnelTrapezoids = createSelector([selectChartOffsetInternal, pickFunnelSettings, selectChartDataAndAlwaysIgnoreIndexes], (offset, _ref, _ref2) => {\n  var {\n    data,\n    dataKey,\n    nameKey,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    customWidth,\n    cells,\n    presentationProps\n  } = _ref;\n  var {\n    chartData\n  } = _ref2;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else if (chartData != null && chartData.length > 0) {\n    displayedData = chartData;\n  }\n  if (displayedData && displayedData.length) {\n    displayedData = displayedData.map((entry, index) => _objectSpread(_objectSpread(_objectSpread({\n      payload: entry\n    }, presentationProps), entry), cells && cells[index] && cells[index].props));\n  } else if (cells && cells.length) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, presentationProps), cell.props));\n  } else {\n    return {\n      trapezoids: [],\n      data: displayedData\n    };\n  }\n  return computeFunnelTrapezoids({\n    dataKey,\n    nameKey,\n    displayedData,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    offset,\n    customWidth\n  });\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "computeFunnelTrapezoids", "selectChartOffsetInternal", "selectChartDataAndAlwaysIgnoreIndexes", "pickFunnelSettings", "_state", "funnelSettings", "selectFunnelTrapezoids", "offset", "_ref", "_ref2", "data", "dataKey", "<PERSON><PERSON><PERSON>", "tooltipType", "lastShapeType", "reversed", "customWidth", "cells", "presentationProps", "chartData", "displayedData", "map", "entry", "index", "payload", "props", "cell", "trapezoids"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/funnelSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { computeFunnelTrapezoids } from '../../cartesian/Funnel';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nvar pickFunnelSettings = (_state, funnelSettings) => funnelSettings;\nexport var selectFunnelTrapezoids = createSelector([selectChartOffsetInternal, pickFunnelSettings, selectChartDataAndAlwaysIgnoreIndexes], (offset, _ref, _ref2) => {\n  var {\n    data,\n    dataKey,\n    nameKey,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    customWidth,\n    cells,\n    presentationProps\n  } = _ref;\n  var {\n    chartData\n  } = _ref2;\n  var displayedData;\n  if (data != null && data.length > 0) {\n    displayedData = data;\n  } else if (chartData != null && chartData.length > 0) {\n    displayedData = chartData;\n  }\n  if (displayedData && displayedData.length) {\n    displayedData = displayedData.map((entry, index) => _objectSpread(_objectSpread(_objectSpread({\n      payload: entry\n    }, presentationProps), entry), cells && cells[index] && cells[index].props));\n  } else if (cells && cells.length) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, presentationProps), cell.props));\n  } else {\n    return {\n      trapezoids: [],\n      data: displayedData\n    };\n  }\n  return computeFunnelTrapezoids({\n    dataKey,\n    nameKey,\n    displayedData,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    offset,\n    customWidth\n  });\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,qCAAqC,QAAQ,iBAAiB;AACvE,IAAIC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,cAAc,KAAKA,cAAc;AACnE,OAAO,IAAIC,sBAAsB,GAAGP,cAAc,CAAC,CAACE,yBAAyB,EAAEE,kBAAkB,EAAED,qCAAqC,CAAC,EAAE,CAACK,MAAM,EAAEC,IAAI,EAAEC,KAAK,KAAK;EAClK,IAAI;IACFC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC;EACF,CAAC,GAAGV,IAAI;EACR,IAAI;IACFW;EACF,CAAC,GAAGV,KAAK;EACT,IAAIW,aAAa;EACjB,IAAIV,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7B,MAAM,GAAG,CAAC,EAAE;IACnCuC,aAAa,GAAGV,IAAI;EACtB,CAAC,MAAM,IAAIS,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACtC,MAAM,GAAG,CAAC,EAAE;IACpDuC,aAAa,GAAGD,SAAS;EAC3B;EACA,IAAIC,aAAa,IAAIA,aAAa,CAACvC,MAAM,EAAE;IACzCuC,aAAa,GAAGA,aAAa,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK5C,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC5F6C,OAAO,EAAEF;IACX,CAAC,EAAEJ,iBAAiB,CAAC,EAAEI,KAAK,CAAC,EAAEL,KAAK,IAAIA,KAAK,CAACM,KAAK,CAAC,IAAIN,KAAK,CAACM,KAAK,CAAC,CAACE,KAAK,CAAC,CAAC;EAC9E,CAAC,MAAM,IAAIR,KAAK,IAAIA,KAAK,CAACpC,MAAM,EAAE;IAChCuC,aAAa,GAAGH,KAAK,CAACI,GAAG,CAACK,IAAI,IAAI/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuC,iBAAiB,CAAC,EAAEQ,IAAI,CAACD,KAAK,CAAC,CAAC;EACpG,CAAC,MAAM;IACL,OAAO;MACLE,UAAU,EAAE,EAAE;MACdjB,IAAI,EAAEU;IACR,CAAC;EACH;EACA,OAAOpB,uBAAuB,CAAC;IAC7BW,OAAO;IACPC,OAAO;IACPQ,aAAa;IACbP,WAAW;IACXC,aAAa;IACbC,QAAQ;IACRR,MAAM;IACNS;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
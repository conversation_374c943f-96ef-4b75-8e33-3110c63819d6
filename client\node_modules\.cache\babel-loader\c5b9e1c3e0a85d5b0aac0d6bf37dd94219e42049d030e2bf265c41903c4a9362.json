{"ast": null, "code": "function zip(...arrs) {\n  let rowCount = 0;\n  for (let i = 0; i < arrs.length; i++) {\n    if (arrs[i].length > rowCount) {\n      rowCount = arrs[i].length;\n    }\n  }\n  const columnCount = arrs.length;\n  const result = Array(rowCount);\n  for (let i = 0; i < rowCount; ++i) {\n    const row = Array(columnCount);\n    for (let j = 0; j < columnCount; ++j) {\n      row[j] = arrs[j][i];\n    }\n    result[i] = row;\n  }\n  return result;\n}\nexport { zip };", "map": {"version": 3, "names": ["zip", "arrs", "rowCount", "i", "length", "columnCount", "result", "Array", "row", "j"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/zip.mjs"], "sourcesContent": ["function zip(...arrs) {\n    let rowCount = 0;\n    for (let i = 0; i < arrs.length; i++) {\n        if (arrs[i].length > rowCount) {\n            rowCount = arrs[i].length;\n        }\n    }\n    const columnCount = arrs.length;\n    const result = Array(rowCount);\n    for (let i = 0; i < rowCount; ++i) {\n        const row = Array(columnCount);\n        for (let j = 0; j < columnCount; ++j) {\n            row[j] = arrs[j][i];\n        }\n        result[i] = row;\n    }\n    return result;\n}\n\nexport { zip };\n"], "mappings": "AAAA,SAASA,GAAGA,CAAC,GAAGC,IAAI,EAAE;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAACC,MAAM,GAAGF,QAAQ,EAAE;MAC3BA,QAAQ,GAAGD,IAAI,CAACE,CAAC,CAAC,CAACC,MAAM;IAC7B;EACJ;EACA,MAAMC,WAAW,GAAGJ,IAAI,CAACG,MAAM;EAC/B,MAAME,MAAM,GAAGC,KAAK,CAACL,QAAQ,CAAC;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAE,EAAEC,CAAC,EAAE;IAC/B,MAAMK,GAAG,GAAGD,KAAK,CAACF,WAAW,CAAC;IAC9B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAE,EAAEI,CAAC,EAAE;MAClCD,GAAG,CAACC,CAAC,CAAC,GAAGR,IAAI,CAACQ,CAAC,CAAC,CAACN,CAAC,CAAC;IACvB;IACAG,MAAM,CAACH,CAAC,CAAC,GAAGK,GAAG;EACnB;EACA,OAAOF,MAAM;AACjB;AAEA,SAASN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeScatterPoints } from '../../cartesian/Scatter';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems, selectZAxisWithScale } from './axisSelectors';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, _zAxisId, _id, _cells, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, _zAxisId, _id, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectZAxis = (state, _xAxisId, _yAxisId, zAxisId) => selectZAxisWithScale(state, 'zAxis', zAxisId, false);\nvar pickScatterId = (_state, _xAxisId, _yAxisId, _zAxisId, id) => id;\nvar pickCells = (_state, _xAxisId, _yAxisId, _zAxisId, _id, cells) => cells;\nvar scatterChartDataSelector = (state, xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectChartDataWithIndexesIfNotInPanorama(state, xAxisId, yAxisId, isPanorama);\nvar selectSynchronisedScatterSettings = createSelector([selectUnfilteredCartesianItems, pickScatterId], (graphicalItems, id) => {\n  return graphicalItems.filter(item => item.type === 'scatter').find(item => item.id === id);\n});\nexport var selectScatterPoints = createSelector([scatterChartDataSelector, selectXAxisWithScale, selectXAxisTicks, selectYAxisWithScale, selectYAxisTicks, selectZAxis, selectSynchronisedScatterSettings, pickCells], (_ref, xAxis, xAxisTicks, yAxis, yAxisTicks, zAxis, scatterSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (scatterSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((scatterSettings === null || scatterSettings === void 0 ? void 0 : scatterSettings.data) != null && scatterSettings.data.length > 0) {\n    displayedData = scatterSettings.data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || (xAxisTicks === null || xAxisTicks === void 0 ? void 0 : xAxisTicks.length) === 0 || (yAxisTicks === null || yAxisTicks === void 0 ? void 0 : yAxisTicks.length) === 0) {\n    return undefined;\n  }\n  return computeScatterPoints({\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeScatterPoints", "selectChartDataWithIndexesIfNotInPanorama", "selectAxisWithScale", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "selectZAxisWithScale", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "_zAxisId", "_id", "_cells", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectZAxis", "zAxisId", "pickScatterId", "_state", "id", "pick<PERSON>ells", "cells", "scatterChartDataSelector", "selectSynchronisedScatterSettings", "graphicalItems", "filter", "item", "type", "find", "selectScatterPoints", "_ref", "xAxis", "xAxisTicks", "yAxis", "yAxisTicks", "zAxis", "scatterSettings", "chartData", "dataStartIndex", "dataEndIndex", "undefined", "displayedData", "data", "length", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/scatterSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeScatterPoints } from '../../cartesian/Scatter';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { selectAxisWithScale, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems, selectZAxisWithScale } from './axisSelectors';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, _zAxisId, _id, _cells, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, _zAxisId, _id, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectZAxis = (state, _xAxisId, _yAxisId, zAxisId) => selectZAxisWithScale(state, 'zAxis', zAxisId, false);\nvar pickScatterId = (_state, _xAxisId, _yAxisId, _zAxisId, id) => id;\nvar pickCells = (_state, _xAxisId, _yAxisId, _zAxisId, _id, cells) => cells;\nvar scatterChartDataSelector = (state, xAxisId, yAxisId, _zAxisId, _id, _cells, isPanorama) => selectChartDataWithIndexesIfNotInPanorama(state, xAxisId, yAxisId, isPanorama);\nvar selectSynchronisedScatterSettings = createSelector([selectUnfilteredCartesianItems, pickScatterId], (graphicalItems, id) => {\n  return graphicalItems.filter(item => item.type === 'scatter').find(item => item.id === id);\n});\nexport var selectScatterPoints = createSelector([scatterChartDataSelector, selectXAxisWithScale, selectXAxisTicks, selectYAxisWithScale, selectYAxisTicks, selectZAxis, selectSynchronisedScatterSettings, pickCells], (_ref, xAxis, xAxisTicks, yAxis, yAxisTicks, zAxis, scatterSettings, cells) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (scatterSettings == null) {\n    return undefined;\n  }\n  var displayedData;\n  if ((scatterSettings === null || scatterSettings === void 0 ? void 0 : scatterSettings.data) != null && scatterSettings.data.length > 0) {\n    displayedData = scatterSettings.data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || (xAxisTicks === null || xAxisTicks === void 0 ? void 0 : xAxisTicks.length) === 0 || (yAxisTicks === null || yAxisTicks === void 0 ? void 0 : yAxisTicks.length) === 0) {\n    return undefined;\n  }\n  return computeScatterPoints({\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,mBAAmB,EAAEC,0BAA0B,EAAEC,8BAA8B,EAAEC,oBAAoB,QAAQ,iBAAiB;AACvI,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,KAAKX,mBAAmB,CAACK,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEK,UAAU,CAAC;AACpJ,IAAIC,gBAAgB,GAAGA,CAACP,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,KAAKV,0BAA0B,CAACI,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEK,UAAU,CAAC;AACvJ,IAAIE,oBAAoB,GAAGA,CAACR,KAAK,EAAES,QAAQ,EAAEC,OAAO,EAAEP,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,KAAKX,mBAAmB,CAACK,KAAK,EAAE,OAAO,EAAEU,OAAO,EAAEJ,UAAU,CAAC;AACpJ,IAAIK,gBAAgB,GAAGA,CAACX,KAAK,EAAES,QAAQ,EAAEC,OAAO,EAAEP,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,KAAKV,0BAA0B,CAACI,KAAK,EAAE,OAAO,EAAEU,OAAO,EAAEJ,UAAU,CAAC;AACvJ,IAAIM,WAAW,GAAGA,CAACZ,KAAK,EAAES,QAAQ,EAAEP,QAAQ,EAAEW,OAAO,KAAKf,oBAAoB,CAACE,KAAK,EAAE,OAAO,EAAEa,OAAO,EAAE,KAAK,CAAC;AAC9G,IAAIC,aAAa,GAAGA,CAACC,MAAM,EAAEN,QAAQ,EAAEP,QAAQ,EAAEC,QAAQ,EAAEa,EAAE,KAAKA,EAAE;AACpE,IAAIC,SAAS,GAAGA,CAACF,MAAM,EAAEN,QAAQ,EAAEP,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEc,KAAK,KAAKA,KAAK;AAC3E,IAAIC,wBAAwB,GAAGA,CAACnB,KAAK,EAAEC,OAAO,EAAES,OAAO,EAAEP,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,KAAKZ,yCAAyC,CAACM,KAAK,EAAEC,OAAO,EAAES,OAAO,EAAEJ,UAAU,CAAC;AAC7K,IAAIc,iCAAiC,GAAG5B,cAAc,CAAC,CAACK,8BAA8B,EAAEiB,aAAa,CAAC,EAAE,CAACO,cAAc,EAAEL,EAAE,KAAK;EAC9H,OAAOK,cAAc,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,CAAC,CAACC,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACP,EAAE,KAAKA,EAAE,CAAC;AAC5F,CAAC,CAAC;AACF,OAAO,IAAIU,mBAAmB,GAAGlC,cAAc,CAAC,CAAC2B,wBAAwB,EAAEpB,oBAAoB,EAAEQ,gBAAgB,EAAEC,oBAAoB,EAAEG,gBAAgB,EAAEC,WAAW,EAAEQ,iCAAiC,EAAEH,SAAS,CAAC,EAAE,CAACU,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,eAAe,EAAEf,KAAK,KAAK;EACpS,IAAI;IACFgB,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGT,IAAI;EACR,IAAIM,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAOI,SAAS;EAClB;EACA,IAAIC,aAAa;EACjB,IAAI,CAACL,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACM,IAAI,KAAK,IAAI,IAAIN,eAAe,CAACM,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;IACvIF,aAAa,GAAGL,eAAe,CAACM,IAAI;EACtC,CAAC,MAAM;IACLD,aAAa,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACO,KAAK,CAACN,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIE,aAAa,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAID,UAAU,IAAI,IAAI,IAAIE,UAAU,IAAI,IAAI,IAAI,CAACF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,MAAM,MAAM,CAAC,IAAI,CAACT,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,MAAM,MAAM,CAAC,EAAE;IACjR,OAAOH,SAAS;EAClB;EACA,OAAO5C,oBAAoB,CAAC;IAC1B6C,aAAa;IACbV,KAAK;IACLE,KAAK;IACLE,KAAK;IACLC,eAAe;IACfJ,UAAU;IACVE,UAAU;IACVb;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
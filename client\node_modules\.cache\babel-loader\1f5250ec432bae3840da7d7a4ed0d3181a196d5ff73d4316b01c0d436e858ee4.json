{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * ErrorBars have lot more settings but all the others are scoped to the component itself.\n * Only some of them required to be reported to the global store because <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> need to know\n * if the error bar is contributing to extending the axis domain.\n */\n\nvar initialState = {};\nvar errorBarSlice = createSlice({\n  name: 'errorBars',\n  initialState,\n  reducers: {\n    addErrorBar: (state, action) => {\n      var {\n        itemId,\n        errorBar\n      } = action.payload;\n      if (!state[itemId]) {\n        state[itemId] = [];\n      }\n      state[itemId].push(errorBar);\n    },\n    removeErrorBar: (state, action) => {\n      var {\n        itemId,\n        errorBar\n      } = action.payload;\n      if (state[itemId]) {\n        state[itemId] = state[itemId].filter(e => e.dataKey !== errorBar.dataKey || e.direction !== errorBar.direction);\n      }\n    }\n  }\n});\nexport var {\n  addErrorBar,\n  removeErrorBar\n} = errorBarSlice.actions;\nexport var errorBarReducer = errorBarSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "errorBarSlice", "name", "reducers", "addErrorBar", "state", "action", "itemId", "errorBar", "payload", "push", "removeErrorBar", "filter", "e", "dataKey", "direction", "actions", "errorBarReducer", "reducer"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/errorBarSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * ErrorBars have lot more settings but all the others are scoped to the component itself.\n * Only some of them required to be reported to the global store because <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> need to know\n * if the error bar is contributing to extending the axis domain.\n */\n\nvar initialState = {};\nvar errorBarSlice = createSlice({\n  name: 'errorBars',\n  initialState,\n  reducers: {\n    addErrorBar: (state, action) => {\n      var {\n        itemId,\n        errorBar\n      } = action.payload;\n      if (!state[itemId]) {\n        state[itemId] = [];\n      }\n      state[itemId].push(errorBar);\n    },\n    removeErrorBar: (state, action) => {\n      var {\n        itemId,\n        errorBar\n      } = action.payload;\n      if (state[itemId]) {\n        state[itemId] = state[itemId].filter(e => e.dataKey !== errorBar.dataKey || e.direction !== errorBar.direction);\n      }\n    }\n  }\n});\nexport var {\n  addErrorBar,\n  removeErrorBar\n} = errorBarSlice.actions;\nexport var errorBarReducer = errorBarSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG,CAAC,CAAC;AACrB,IAAIC,aAAa,GAAGF,WAAW,CAAC;EAC9BG,IAAI,EAAE,WAAW;EACjBF,YAAY;EACZG,QAAQ,EAAE;IACRC,WAAW,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC9B,IAAI;QACFC,MAAM;QACNC;MACF,CAAC,GAAGF,MAAM,CAACG,OAAO;MAClB,IAAI,CAACJ,KAAK,CAACE,MAAM,CAAC,EAAE;QAClBF,KAAK,CAACE,MAAM,CAAC,GAAG,EAAE;MACpB;MACAF,KAAK,CAACE,MAAM,CAAC,CAACG,IAAI,CAACF,QAAQ,CAAC;IAC9B,CAAC;IACDG,cAAc,EAAEA,CAACN,KAAK,EAAEC,MAAM,KAAK;MACjC,IAAI;QACFC,MAAM;QACNC;MACF,CAAC,GAAGF,MAAM,CAACG,OAAO;MAClB,IAAIJ,KAAK,CAACE,MAAM,CAAC,EAAE;QACjBF,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKN,QAAQ,CAACM,OAAO,IAAID,CAAC,CAACE,SAAS,KAAKP,QAAQ,CAACO,SAAS,CAAC;MACjH;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTX,WAAW;EACXO;AACF,CAAC,GAAGV,aAAa,CAACe,OAAO;AACzB,OAAO,IAAIC,eAAe,GAAGhB,aAAa,CAACiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { computeArea } from '../../cartesian/Area';\nimport { selectAxisWithScale, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickAreaId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedAreaSettings = createSelector([selectUnfilteredCartesianItems, pickAreaId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'area').find(item => item.id === id));\nexport var selectGraphicalItemStackedData = (state, xAxisId, yAxisId, isPanorama, id) => {\n  var _stackGroups$stackId;\n  var areaSettings = selectSynchronisedAreaSettings(state, xAxisId, yAxisId, isPanorama, id);\n  if (areaSettings == null) {\n    return undefined;\n  }\n  var layout = selectChartLayout(state);\n  var isXAxisCategorical = isCategoricalAxis(layout, 'xAxis');\n  var stackGroups;\n  if (isXAxisCategorical) {\n    stackGroups = selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  } else {\n    stackGroups = selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n  }\n  if (stackGroups == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = areaSettings;\n  var stackSeriesIdentifier = getStackSeriesIdentifier(areaSettings);\n  if (stackId == null || stackSeriesIdentifier == null) {\n    return undefined;\n  }\n  var groups = (_stackGroups$stackId = stackGroups[stackId]) === null || _stackGroups$stackId === void 0 ? void 0 : _stackGroups$stackId.stackedData;\n  return groups === null || groups === void 0 ? void 0 : groups.find(v => v.key === stackSeriesIdentifier);\n};\nexport var selectArea = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectGraphicalItemStackedData, selectChartDataWithIndexesIfNotInPanorama, selectBandSize, selectSynchronisedAreaSettings], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, stackedData, _ref, bandSize, areaSettings) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (areaSettings == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = areaSettings;\n  var displayedData;\n  if (data && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n\n  // Where is this supposed to come from? No charts have that as a prop.\n  var chartBaseValue = undefined;\n  return computeArea({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataStartIndex,\n    areaSettings,\n    stackedData,\n    displayedData,\n    chartBaseValue,\n    bandSize\n  });\n});", "map": {"version": 3, "names": ["createSelector", "computeArea", "selectAxisWithScale", "selectStackGroups", "selectTicksOfGraphicalItem", "selectUnfilteredCartesianItems", "selectChartLayout", "selectChartDataWithIndexesIfNotInPanorama", "getBandSizeOfAxis", "isCategoricalAxis", "getStackSeriesIdentifier", "selectXAxisWithScale", "state", "xAxisId", "_yAxisId", "isPanorama", "selectXAxisTicks", "selectYAxisWithScale", "_xAxisId", "yAxisId", "selectYAxisTicks", "selectBandSize", "layout", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "pickAreaId", "_state", "_isPanorama", "id", "selectSynchronisedAreaSettings", "graphicalItems", "filter", "item", "type", "find", "selectGraphicalItemStackedData", "_stackGroups$stackId", "areaSettings", "undefined", "isXAxisCategorical", "stackGroups", "stackId", "stackSeriesIdentifier", "groups", "stackedData", "v", "key", "selectArea", "_ref", "bandSize", "chartData", "dataStartIndex", "dataEndIndex", "length", "data", "displayedData", "slice", "chartBaseValue"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/state/selectors/areaSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { computeArea } from '../../cartesian/Area';\nimport { selectAxisWithScale, selectStackGroups, selectTicksOfGraphicalItem, selectUnfilteredCartesianItems } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { getBandSizeOfAxis, isCategoricalAxis } from '../../util/ChartUtils';\nimport { getStackSeriesIdentifier } from '../../util/stacks/getStackSeriesIdentifier';\nvar selectXAxisWithScale = (state, xAxisId, _yAxisId, isPanorama) => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama);\nvar selectXAxisTicks = (state, xAxisId, _yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'xAxis', xAxisId, isPanorama);\nvar selectYAxisWithScale = (state, _xAxisId, yAxisId, isPanorama) => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama);\nvar selectYAxisTicks = (state, _xAxisId, yAxisId, isPanorama) => selectTicksOfGraphicalItem(state, 'yAxis', yAxisId, isPanorama);\nvar selectBandSize = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks) => {\n  if (isCategoricalAxis(layout, 'xAxis')) {\n    return getBandSizeOfAxis(xAxis, xAxisTicks, false);\n  }\n  return getBandSizeOfAxis(yAxis, yAxisTicks, false);\n});\nvar pickAreaId = (_state, _xAxisId, _yAxisId, _isPanorama, id) => id;\n\n/*\n * There is a race condition problem because we read some data from props and some from the state.\n * The state is updated through a dispatch and is one render behind,\n * and so we have this weird one tick render where the displayedData in one selector have the old dataKey\n * but the new dataKey in another selector.\n *\n * A proper fix is to either move everything into the state, or read the dataKey always from props\n * - but this is a smaller change.\n */\nvar selectSynchronisedAreaSettings = createSelector([selectUnfilteredCartesianItems, pickAreaId], (graphicalItems, id) => graphicalItems.filter(item => item.type === 'area').find(item => item.id === id));\nexport var selectGraphicalItemStackedData = (state, xAxisId, yAxisId, isPanorama, id) => {\n  var _stackGroups$stackId;\n  var areaSettings = selectSynchronisedAreaSettings(state, xAxisId, yAxisId, isPanorama, id);\n  if (areaSettings == null) {\n    return undefined;\n  }\n  var layout = selectChartLayout(state);\n  var isXAxisCategorical = isCategoricalAxis(layout, 'xAxis');\n  var stackGroups;\n  if (isXAxisCategorical) {\n    stackGroups = selectStackGroups(state, 'yAxis', yAxisId, isPanorama);\n  } else {\n    stackGroups = selectStackGroups(state, 'xAxis', xAxisId, isPanorama);\n  }\n  if (stackGroups == null) {\n    return undefined;\n  }\n  var {\n    stackId\n  } = areaSettings;\n  var stackSeriesIdentifier = getStackSeriesIdentifier(areaSettings);\n  if (stackId == null || stackSeriesIdentifier == null) {\n    return undefined;\n  }\n  var groups = (_stackGroups$stackId = stackGroups[stackId]) === null || _stackGroups$stackId === void 0 ? void 0 : _stackGroups$stackId.stackedData;\n  return groups === null || groups === void 0 ? void 0 : groups.find(v => v.key === stackSeriesIdentifier);\n};\nexport var selectArea = createSelector([selectChartLayout, selectXAxisWithScale, selectYAxisWithScale, selectXAxisTicks, selectYAxisTicks, selectGraphicalItemStackedData, selectChartDataWithIndexesIfNotInPanorama, selectBandSize, selectSynchronisedAreaSettings], (layout, xAxis, yAxis, xAxisTicks, yAxisTicks, stackedData, _ref, bandSize, areaSettings) => {\n  var {\n    chartData,\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (areaSettings == null || layout !== 'horizontal' && layout !== 'vertical' || xAxis == null || yAxis == null || xAxisTicks == null || yAxisTicks == null || xAxisTicks.length === 0 || yAxisTicks.length === 0 || bandSize == null) {\n    return undefined;\n  }\n  var {\n    data\n  } = areaSettings;\n  var displayedData;\n  if (data && data.length > 0) {\n    displayedData = data;\n  } else {\n    displayedData = chartData === null || chartData === void 0 ? void 0 : chartData.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n\n  // Where is this supposed to come from? No charts have that as a prop.\n  var chartBaseValue = undefined;\n  return computeArea({\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataStartIndex,\n    areaSettings,\n    stackedData,\n    displayedData,\n    chartBaseValue,\n    bandSize\n  });\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,mBAAmB,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,8BAA8B,QAAQ,iBAAiB;AACpI,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,yCAAyC,QAAQ,iBAAiB;AAC3E,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC5E,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,IAAIC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKb,mBAAmB,CAACU,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAC7H,IAAIC,gBAAgB,GAAGA,CAACJ,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,KAAKX,0BAA0B,CAACQ,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;AAChI,IAAIE,oBAAoB,GAAGA,CAACL,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKb,mBAAmB,CAACU,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAC7H,IAAIK,gBAAgB,GAAGA,CAACR,KAAK,EAAEM,QAAQ,EAAEC,OAAO,EAAEJ,UAAU,KAAKX,0BAA0B,CAACQ,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;AAChI,IAAIM,cAAc,GAAGrB,cAAc,CAAC,CAACM,iBAAiB,EAAEK,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,CAAC,EAAE,CAACE,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,KAAK;EACzL,IAAIjB,iBAAiB,CAACa,MAAM,EAAE,OAAO,CAAC,EAAE;IACtC,OAAOd,iBAAiB,CAACe,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;EACpD;EACA,OAAOjB,iBAAiB,CAACgB,KAAK,EAAEE,UAAU,EAAE,KAAK,CAAC;AACpD,CAAC,CAAC;AACF,IAAIC,UAAU,GAAGA,CAACC,MAAM,EAAEV,QAAQ,EAAEJ,QAAQ,EAAEe,WAAW,EAAEC,EAAE,KAAKA,EAAE;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,8BAA8B,GAAG/B,cAAc,CAAC,CAACK,8BAA8B,EAAEsB,UAAU,CAAC,EAAE,CAACK,cAAc,EAAEF,EAAE,KAAKE,cAAc,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM,CAAC,CAACC,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC,CAAC;AAC3M,OAAO,IAAIO,8BAA8B,GAAGA,CAACzB,KAAK,EAAEC,OAAO,EAAEM,OAAO,EAAEJ,UAAU,EAAEe,EAAE,KAAK;EACvF,IAAIQ,oBAAoB;EACxB,IAAIC,YAAY,GAAGR,8BAA8B,CAACnB,KAAK,EAAEC,OAAO,EAAEM,OAAO,EAAEJ,UAAU,EAAEe,EAAE,CAAC;EAC1F,IAAIS,YAAY,IAAI,IAAI,EAAE;IACxB,OAAOC,SAAS;EAClB;EACA,IAAIlB,MAAM,GAAGhB,iBAAiB,CAACM,KAAK,CAAC;EACrC,IAAI6B,kBAAkB,GAAGhC,iBAAiB,CAACa,MAAM,EAAE,OAAO,CAAC;EAC3D,IAAIoB,WAAW;EACf,IAAID,kBAAkB,EAAE;IACtBC,WAAW,GAAGvC,iBAAiB,CAACS,KAAK,EAAE,OAAO,EAAEO,OAAO,EAAEJ,UAAU,CAAC;EACtE,CAAC,MAAM;IACL2B,WAAW,GAAGvC,iBAAiB,CAACS,KAAK,EAAE,OAAO,EAAEC,OAAO,EAAEE,UAAU,CAAC;EACtE;EACA,IAAI2B,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOF,SAAS;EAClB;EACA,IAAI;IACFG;EACF,CAAC,GAAGJ,YAAY;EAChB,IAAIK,qBAAqB,GAAGlC,wBAAwB,CAAC6B,YAAY,CAAC;EAClE,IAAII,OAAO,IAAI,IAAI,IAAIC,qBAAqB,IAAI,IAAI,EAAE;IACpD,OAAOJ,SAAS;EAClB;EACA,IAAIK,MAAM,GAAG,CAACP,oBAAoB,GAAGI,WAAW,CAACC,OAAO,CAAC,MAAM,IAAI,IAAIL,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACQ,WAAW;EAClJ,OAAOD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACT,IAAI,CAACW,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,qBAAqB,CAAC;AAC1G,CAAC;AACD,OAAO,IAAIK,UAAU,GAAGjD,cAAc,CAAC,CAACM,iBAAiB,EAAEK,oBAAoB,EAAEM,oBAAoB,EAAED,gBAAgB,EAAEI,gBAAgB,EAAEiB,8BAA8B,EAAE9B,yCAAyC,EAAEc,cAAc,EAAEU,8BAA8B,CAAC,EAAE,CAACT,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEoB,WAAW,EAAEI,IAAI,EAAEC,QAAQ,EAAEZ,YAAY,KAAK;EAClW,IAAI;IACFa,SAAS;IACTC,cAAc;IACdC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAIX,YAAY,IAAI,IAAI,IAAIjB,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIC,KAAK,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAID,UAAU,CAAC8B,MAAM,KAAK,CAAC,IAAI7B,UAAU,CAAC6B,MAAM,KAAK,CAAC,IAAIJ,QAAQ,IAAI,IAAI,EAAE;IACpO,OAAOX,SAAS;EAClB;EACA,IAAI;IACFgB;EACF,CAAC,GAAGjB,YAAY;EAChB,IAAIkB,aAAa;EACjB,IAAID,IAAI,IAAIA,IAAI,CAACD,MAAM,GAAG,CAAC,EAAE;IAC3BE,aAAa,GAAGD,IAAI;EACtB,CAAC,MAAM;IACLC,aAAa,GAAGL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,KAAK,CAACL,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACzH;EACA,IAAIG,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOjB,SAAS;EAClB;;EAEA;EACA,IAAImB,cAAc,GAAGnB,SAAS;EAC9B,OAAOvC,WAAW,CAAC;IACjBqB,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACV2B,cAAc;IACdd,YAAY;IACZO,WAAW;IACXW,aAAa;IACbE,cAAc;IACdR;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function ary(func, n) {\n  return function (...args) {\n    return func.apply(this, args.slice(0, n));\n  };\n}\nexport { ary };", "map": {"version": 3, "names": ["ary", "func", "n", "args", "apply", "slice"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/function/ary.mjs"], "sourcesContent": ["function ary(func, n) {\n    return function (...args) {\n        return func.apply(this, args.slice(0, n));\n    };\n}\n\nexport { ary };\n"], "mappings": "AAAA,SAASA,GAAGA,CAACC,IAAI,EAAEC,CAAC,EAAE;EAClB,OAAO,UAAU,GAAGC,IAAI,EAAE;IACtB,OAAOF,IAAI,CAACG,KAAK,CAAC,IAAI,EAAED,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC,CAAC;EAC7C,CAAC;AACL;AAEA,SAASF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
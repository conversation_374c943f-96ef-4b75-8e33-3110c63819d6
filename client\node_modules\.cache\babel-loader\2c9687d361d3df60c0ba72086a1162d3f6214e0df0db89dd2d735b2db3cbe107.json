{"ast": null, "code": "export var COLOR_PANEL = ['#1890FF', '#66B5FF', '#41D9C7', '#2FC25B', '#6EDB8F', '#9AE65C', '#FACC14', '#E6965C', '#57AD71', '#223273', '#738AE6', '#7564CC', '#8543E0', '#A877ED', '#5C8EE6', '#13C2C2', '#70E0E0', '#5CA3E6', '#3436C7', '#8082FF', '#DD81E6', '#F04864', '#FA7D92', '#D598D9'];\n\n/**\n * We use this attribute to identify which element is the one that the user is touching.\n * The index is the position of the element in the data array.\n * This can be either a number (for array-based charts) or a string (for the charts that have a matrix-shaped data).\n */\nexport var DATA_ITEM_INDEX_ATTRIBUTE_NAME = 'data-recharts-item-index';\n/**\n * We use this attribute to identify which element is the one that the user is touching.\n * <PERSON><PERSON><PERSON> works here as a kind of identifier for the element. It's not a perfect identifier for ~two~ three reasons:\n *\n * 1. There can be two different elements with the same dataKey; we won't know which is it\n * 2. DataKey can be a function, and that serialized will be a `[Function: anonymous]` string\n * which means we will be able to identify that it was a function but can't tell which one.\n * This will lead to some weird bugs. A proper fix would be to either:\n * a) use a unique identifier for each element (passed from props, or generated)\n * b) figure out how to compare the dataKey or graphical item by object reference\n *\n * a) is a fuss because we don't have the unique identifier in props,\n * and b) is possible most of the time except for touchMove events which work differently from mouseEnter/mouseLeave:\n * - while mouseEnter is fired for the element that the mouse is over,\n * touchMove is fired for the element where user has started touching. As the finger moves,\n * we can identify the element that the user is touching by using the elementFromPoint method,\n * but it keeps calling the handler on the element where touchStart was fired.\n *\n * Okay and now I discovered a third reason: the dataKey can be undefined and that's still fine\n * because if dataKey is undefined then graphical elements assume the dataKey of the axes.\n * Which makes it a convenient way of using recharts to render a chart but horrible identifier.\n */\nexport var DATA_ITEM_DATAKEY_ATTRIBUTE_NAME = 'data-recharts-item-data-key';\nexport var DEFAULT_Y_AXIS_WIDTH = 60;", "map": {"version": 3, "names": ["COLOR_PANEL", "DATA_ITEM_INDEX_ATTRIBUTE_NAME", "DATA_ITEM_DATAKEY_ATTRIBUTE_NAME", "DEFAULT_Y_AXIS_WIDTH"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/Constants.js"], "sourcesContent": ["export var COLOR_PANEL = ['#1890FF', '#66B5FF', '#41D9C7', '#2FC25B', '#6EDB8F', '#9AE65C', '#FACC14', '#E6965C', '#57AD71', '#223273', '#738AE6', '#7564CC', '#8543E0', '#A877ED', '#5C8EE6', '#13C2C2', '#70E0E0', '#5CA3E6', '#3436C7', '#8082FF', '#DD81E6', '#F04864', '#FA7D92', '#D598D9'];\n\n/**\n * We use this attribute to identify which element is the one that the user is touching.\n * The index is the position of the element in the data array.\n * This can be either a number (for array-based charts) or a string (for the charts that have a matrix-shaped data).\n */\nexport var DATA_ITEM_INDEX_ATTRIBUTE_NAME = 'data-recharts-item-index';\n/**\n * We use this attribute to identify which element is the one that the user is touching.\n * <PERSON><PERSON><PERSON> works here as a kind of identifier for the element. It's not a perfect identifier for ~two~ three reasons:\n *\n * 1. There can be two different elements with the same dataKey; we won't know which is it\n * 2. DataKey can be a function, and that serialized will be a `[Function: anonymous]` string\n * which means we will be able to identify that it was a function but can't tell which one.\n * This will lead to some weird bugs. A proper fix would be to either:\n * a) use a unique identifier for each element (passed from props, or generated)\n * b) figure out how to compare the dataKey or graphical item by object reference\n *\n * a) is a fuss because we don't have the unique identifier in props,\n * and b) is possible most of the time except for touchMove events which work differently from mouseEnter/mouseLeave:\n * - while mouseEnter is fired for the element that the mouse is over,\n * touchMove is fired for the element where user has started touching. As the finger moves,\n * we can identify the element that the user is touching by using the elementFromPoint method,\n * but it keeps calling the handler on the element where touchStart was fired.\n *\n * Okay and now I discovered a third reason: the dataKey can be undefined and that's still fine\n * because if dataKey is undefined then graphical elements assume the dataKey of the axes.\n * Which makes it a convenient way of using recharts to render a chart but horrible identifier.\n */\nexport var DATA_ITEM_DATAKEY_ATTRIBUTE_NAME = 'data-recharts-item-data-key';\nexport var DEFAULT_Y_AXIS_WIDTH = 60;"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;AAEjS;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,8BAA8B,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gCAAgC,GAAG,6BAA6B;AAC3E,OAAO,IAAIC,oBAAoB,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
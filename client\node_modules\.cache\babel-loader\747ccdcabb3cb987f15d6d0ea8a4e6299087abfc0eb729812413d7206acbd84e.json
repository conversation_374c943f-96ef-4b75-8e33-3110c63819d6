{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport get from 'es-toolkit/compat/get';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport { findEntryInArray, isNan, isNullish, isNumber, isNumOrStr, mathSign } from './DataUtils';\nimport { inRangeOfSector, polarToCartesian } from './PolarUtils';\nimport { getSliced } from './getSliced';\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (isNullish(obj) || isNullish(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return get(obj, dataKey, defaultValue);\n  }\n  if (typeof dataKey === 'function') {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\nexport var calculateActiveTickIndex = (coordinate, ticks, unsortedTicks, axisType, range) => {\n  var _ticks$length;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or fewer ticks or if there is no coordinate then the active tick is at index 0\n  if (len <= 1 || coordinate == null) {\n    return 0;\n  }\n  if (axisType === 'angleAxis' && range != null && Math.abs(Math.abs(range[1] - range[0]) - 360) <= 1e-6) {\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          ({\n            index\n          } = unsortedTicks[i]);\n          break;\n        }\n      } else {\n        var minValue = Math.min(before, after);\n        var maxValue = Math.max(before, after);\n        if (coordinate > (minValue + cur) / 2 && coordinate <= (maxValue + cur) / 2) {\n          ({\n            index\n          } = unsortedTicks[i]);\n          break;\n        }\n      }\n    }\n  } else if (ticks) {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        ({\n          index\n        } = ticks[_i]);\n        break;\n      }\n    }\n  }\n  return index;\n};\nexport var appendOffsetOfLegend = (offset, legendSettings, legendSize) => {\n  if (legendSettings && legendSize) {\n    var {\n      width: boxWidth,\n      height: boxHeight\n    } = legendSize;\n    var {\n      align,\n      verticalAlign,\n      layout\n    } = legendSettings;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && align !== 'center' && isNumber(offset[align])) {\n      return _objectSpread(_objectSpread({}, offset), {}, {\n        [align]: offset[align] + (boxWidth || 0)\n      });\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && verticalAlign !== 'middle' && isNumber(offset[verticalAlign])) {\n      return _objectSpread(_objectSpread({}, offset), {}, {\n        [verticalAlign]: offset[verticalAlign] + (boxHeight || 0)\n      });\n    }\n  }\n  return offset;\n};\nexport var isCategoricalAxis = (layout, axisType) => layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks           The ticks in axis\n * @param {Number} minValue        The minimum value of axis\n * @param {Number} maxValue        The maximum value of axis\n * @param {boolean} syncWithTicks  Synchronize grid lines with ticks or not\n * @return {Array}                 Coordinates\n */\nexport var getCoordinatesOfGrid = (ticks, minValue, maxValue, syncWithTicks) => {\n  if (syncWithTicks) {\n    return ticks.map(entry => entry.coordinate);\n  }\n  var hasMin, hasMax;\n  var values = ticks.map(entry => {\n    if (entry.coordinate === minValue) {\n      hasMin = true;\n    }\n    if (entry.coordinate === maxValue) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(minValue);\n  }\n  if (!hasMax) {\n    values.push(maxValue);\n  }\n  return values;\n};\n\n/**\n * A subset of d3-scale that Recharts is using\n */\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = (axis, isGrid, isAll) => {\n  if (!axis) {\n    return null;\n  }\n  var {\n    duplicateDomain,\n    type,\n    range,\n    scale,\n    realScaleType,\n    isCategorical,\n    categoricalDomain,\n    tickCount,\n    ticks,\n    niceTicks,\n    axisType\n  } = axis;\n  if (!scale) {\n    return null;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range && range.length >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (ticks || niceTicks)) {\n    var result = (ticks || niceTicks || []).map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset,\n        index\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks && !isAll && tickCount != null) {\n    return scale.ticks(tickCount).map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset,\n      index\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = scale => {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var minValue = Math.min(range[0], range[1]) - EPS;\n  var maxValue = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < minValue || first > maxValue || last < minValue || last > maxValue) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\n\n/**\n * Both value and domain are tuples of two numbers\n * - but the type stays as array of numbers until we have better support in rest of the app\n * @param value input that will be truncated\n * @param domain boundaries\n * @returns tuple of two numbers\n */\nexport var truncateByDomain = (value, domain) => {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var minValue = Math.min(domain[0], domain[1]);\n  var maxValue = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < minValue) {\n    result[0] = minValue;\n  }\n  if (!isNumber(value[1]) || value[1] > maxValue) {\n    result[1] = maxValue;\n  }\n  if (result[0] > maxValue) {\n    result[0] = maxValue;\n  }\n  if (result[1] < minValue) {\n    result[1] = minValue;\n  }\n  return result;\n};\n\n/**\n * Stacks all positive numbers above zero and all negative numbers below zero.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetSign = series => {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Replaces all negative values with zero when stacking data.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetPositive = series => {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Function type to compute offset for stacked data.\n *\n * d3-shape has something fishy going on with its types.\n * In @definitelytyped/d3-shape, this function (the offset accessor) is typed as Series<> => void.\n * However! When I actually open the storybook I can see that the offset accessor actually receives Array<Series<>>.\n * The same I can see in the source code itself:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n * That one unfortunately has no types but we can tell it passes three-dimensional array.\n *\n * Which leads me to believe that definitelytyped is wrong on this one.\n * There's open discussion on this topic without much attention:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n */\n\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  // @ts-expect-error definitelytyped types are incorrect\n  expand: stackOffsetExpand,\n  // @ts-expect-error definitelytyped types are incorrect\n  none: stackOffsetNone,\n  // @ts-expect-error definitelytyped types are incorrect\n  silhouette: stackOffsetSilhouette,\n  // @ts-expect-error definitelytyped types are incorrect\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = (data, dataKeys, offsetType) => {\n  var offsetAccessor = STACK_OFFSET_MAP[offsetType];\n  var stack = shapeStack().keys(dataKeys).value((d, key) => +getValueByDataKey(d, key, 0)).order(stackOrderNone)\n  // @ts-expect-error definitelytyped types are incorrect\n  .offset(offsetAccessor);\n  return stack(data);\n};\n\n/**\n * Stack IDs in the external props allow numbers; but internally we use it as an object key\n * and object keys are always strings. Also, it would be kinda confusing if stackId=8 and stackId='8' were different stacks\n * so let's just force a string.\n */\n\nexport function getNormalizedStackId(publicStackId) {\n  return publicStackId == null ? undefined : String(publicStackId);\n}\nexport function getCateCoordinateOfLine(_ref) {\n  var {\n    axis,\n    ticks,\n    bandSize,\n    entry,\n    index,\n    dataKey\n  } = _ref;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !isNullish(entry[axis.dataKey])) {\n      // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !isNullish(dataKey) ? dataKey : axis.dataKey);\n\n  // @ts-expect-error getValueByDataKey does not validate the output type\n  return !isNullish(value) ? axis.scale(value) : null;\n}\nexport var getCateCoordinateOfBar = _ref2 => {\n  var {\n    axis,\n    ticks,\n    offset,\n    bandSize,\n    entry,\n    index\n  } = _ref2;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.scale.domain()[index]);\n  return !isNullish(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = _ref3 => {\n  var {\n    numericAxis\n  } = _ref3;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    // @ts-expect-error type number means the domain has numbers in it but this relationship is not known to typescript\n    var minValue = Math.min(domain[0], domain[1]);\n    // @ts-expect-error type number means the domain has numbers in it but this relationship is not known to typescript\n    var maxValue = Math.max(domain[0], domain[1]);\n    if (minValue <= 0 && maxValue >= 0) {\n      return 0;\n    }\n    if (maxValue < 0) {\n      return maxValue;\n    }\n    return minValue;\n  }\n  return domain[0];\n};\nvar getDomainOfSingle = data => {\n  var flat = data.flat(2).filter(isNumber);\n  return [Math.min(...flat), Math.max(...flat)];\n};\nvar makeDomainFinite = domain => {\n  return [domain[0] === Infinity ? 0 : domain[0], domain[1] === -Infinity ? 0 : domain[1]];\n};\nexport var getDomainOfStackGroups = (stackGroups, startIndex, endIndex) => {\n  if (stackGroups == null) {\n    return undefined;\n  }\n  return makeDomainFinite(Object.keys(stackGroups).reduce((result, stackId) => {\n    var group = stackGroups[stackId];\n    var {\n      stackedData\n    } = group;\n    var domain = stackedData.reduce((res, entry) => {\n      var sliced = getSliced(entry, startIndex, endIndex);\n      var s = getDomainOfSingle(sliced);\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]));\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = (axis, ticks, isBar) => {\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = sortBy(ticks, o => o.coordinate);\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\nexport function getTooltipEntry(_ref4) {\n  var {\n    tooltipEntrySettings,\n    dataKey,\n    payload,\n    value,\n    name\n  } = _ref4;\n  return _objectSpread(_objectSpread({}, tooltipEntrySettings), {}, {\n    dataKey,\n    payload,\n    value,\n    name\n  });\n}\nexport function getTooltipNameProp(nameFromItem, dataKey) {\n  if (nameFromItem) {\n    return String(nameFromItem);\n  }\n  if (typeof dataKey === 'string') {\n    return dataKey;\n  }\n  return undefined;\n}\nexport function inRange(x, y, layout, polarViewBox, offset) {\n  if (layout === 'horizontal' || layout === 'vertical') {\n    var isInRange = x >= offset.left && x <= offset.left + offset.width && y >= offset.top && y <= offset.top + offset.height;\n    return isInRange ? {\n      x,\n      y\n    } : null;\n  }\n  if (polarViewBox) {\n    return inRangeOfSector({\n      x,\n      y\n    }, polarViewBox);\n  }\n  return null;\n}\nexport var getActiveCoordinate = (layout, tooltipTicks, activeIndex, rangeObj) => {\n  var entry = tooltipTicks.find(tick => tick && tick.index === activeIndex);\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var {\n        radius: _radius\n      } = rangeObj;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var {\n      angle\n    } = rangeObj;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle,\n      radius\n    });\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n};\nexport var calculateTooltipPos = (rangeObj, layout) => {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "sortBy", "get", "stack", "shapeStack", "stackOffsetExpand", "stackOffsetNone", "stackOffsetSilhouette", "stackOffsetWiggle", "stackOrderNone", "findEntryInArray", "isNan", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isNumOrStr", "mathSign", "inRangeOfSector", "polarToCartesian", "getSliced", "getValueByDataKey", "obj", "dataKey", "defaultValue", "calculateActiveTickIndex", "coordinate", "ticks", "unsortedTicks", "axisType", "range", "_ticks$length", "index", "len", "Math", "abs", "before", "cur", "after", "sameDirectionCoord", "diffInterval", "curInRange", "min", "max", "afterInRange", "sameInterval", "minValue", "maxValue", "_i", "appendOffsetOfLegend", "offset", "legendSettings", "legendSize", "width", "boxWidth", "height", "boxHeight", "align", "verticalAlign", "layout", "isCategoricalAxis", "getCoordinatesOfGrid", "syncWithTicks", "map", "entry", "has<PERSON>in", "hasMax", "values", "getTicksOfAxis", "axis", "isGrid", "isAll", "duplicateDomain", "type", "scale", "realScaleType", "isCategorical", "categoricalDomain", "tickCount", "niceTicks", "offsetForBand", "bandwidth", "result", "scaleContent", "indexOf", "row", "domain", "EPS", "checkDomainOfScale", "first", "last", "truncateByDomain", "offsetSign", "series", "n", "j", "m", "positive", "negative", "offsetPositive", "STACK_OFFSET_MAP", "sign", "expand", "none", "silhouette", "wiggle", "getStackedData", "data", "dataKeys", "offsetType", "offsetAccessor", "d", "key", "order", "getNormalizedStackId", "publicStackId", "undefined", "getCateCoordinateOfLine", "_ref", "bandSize", "allowDuplicatedCategory", "matchedTick", "getCateCoordinateOfBar", "_ref2", "getBaseValueOfBar", "_ref3", "numericAxis", "getDomainOfSingle", "flat", "makeDomainFinite", "Infinity", "getDomainOfStackGroups", "stackGroups", "startIndex", "endIndex", "reduce", "stackId", "group", "stackedData", "res", "sliced", "s", "MIN_VALUE_REG", "MAX_VALUE_REG", "getBandSizeOfAxis", "isBar", "bandWidth", "orderedTicks", "prev", "getTooltipEntry", "_ref4", "tooltipEntrySettings", "payload", "name", "getTooltipNameProp", "nameFromItem", "inRange", "x", "y", "polarViewBox", "isInRange", "left", "top", "getActiveCoordinate", "tooltipTicks", "activeIndex", "rangeObj", "find", "tick", "_angle", "radius", "_radius", "cx", "cy", "angle", "calculateTooltipPos"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/recharts/es6/util/ChartUtils.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport get from 'es-toolkit/compat/get';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport { findEntryInArray, isNan, isNullish, isNumber, isNumOrStr, mathSign } from './DataUtils';\nimport { inRangeOfSector, polarToCartesian } from './PolarUtils';\nimport { getSliced } from './getSliced';\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (isNullish(obj) || isNullish(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return get(obj, dataKey, defaultValue);\n  }\n  if (typeof dataKey === 'function') {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\nexport var calculateActiveTickIndex = (coordinate, ticks, unsortedTicks, axisType, range) => {\n  var _ticks$length;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or fewer ticks or if there is no coordinate then the active tick is at index 0\n  if (len <= 1 || coordinate == null) {\n    return 0;\n  }\n  if (axisType === 'angleAxis' && range != null && Math.abs(Math.abs(range[1] - range[0]) - 360) <= 1e-6) {\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          ({\n            index\n          } = unsortedTicks[i]);\n          break;\n        }\n      } else {\n        var minValue = Math.min(before, after);\n        var maxValue = Math.max(before, after);\n        if (coordinate > (minValue + cur) / 2 && coordinate <= (maxValue + cur) / 2) {\n          ({\n            index\n          } = unsortedTicks[i]);\n          break;\n        }\n      }\n    }\n  } else if (ticks) {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        ({\n          index\n        } = ticks[_i]);\n        break;\n      }\n    }\n  }\n  return index;\n};\nexport var appendOffsetOfLegend = (offset, legendSettings, legendSize) => {\n  if (legendSettings && legendSize) {\n    var {\n      width: boxWidth,\n      height: boxHeight\n    } = legendSize;\n    var {\n      align,\n      verticalAlign,\n      layout\n    } = legendSettings;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && align !== 'center' && isNumber(offset[align])) {\n      return _objectSpread(_objectSpread({}, offset), {}, {\n        [align]: offset[align] + (boxWidth || 0)\n      });\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && verticalAlign !== 'middle' && isNumber(offset[verticalAlign])) {\n      return _objectSpread(_objectSpread({}, offset), {}, {\n        [verticalAlign]: offset[verticalAlign] + (boxHeight || 0)\n      });\n    }\n  }\n  return offset;\n};\nexport var isCategoricalAxis = (layout, axisType) => layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks           The ticks in axis\n * @param {Number} minValue        The minimum value of axis\n * @param {Number} maxValue        The maximum value of axis\n * @param {boolean} syncWithTicks  Synchronize grid lines with ticks or not\n * @return {Array}                 Coordinates\n */\nexport var getCoordinatesOfGrid = (ticks, minValue, maxValue, syncWithTicks) => {\n  if (syncWithTicks) {\n    return ticks.map(entry => entry.coordinate);\n  }\n  var hasMin, hasMax;\n  var values = ticks.map(entry => {\n    if (entry.coordinate === minValue) {\n      hasMin = true;\n    }\n    if (entry.coordinate === maxValue) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(minValue);\n  }\n  if (!hasMax) {\n    values.push(maxValue);\n  }\n  return values;\n};\n\n/**\n * A subset of d3-scale that Recharts is using\n */\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = (axis, isGrid, isAll) => {\n  if (!axis) {\n    return null;\n  }\n  var {\n    duplicateDomain,\n    type,\n    range,\n    scale,\n    realScaleType,\n    isCategorical,\n    categoricalDomain,\n    tickCount,\n    ticks,\n    niceTicks,\n    axisType\n  } = axis;\n  if (!scale) {\n    return null;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range && range.length >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (ticks || niceTicks)) {\n    var result = (ticks || niceTicks || []).map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset,\n        index\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks && !isAll && tickCount != null) {\n    return scale.ticks(tickCount).map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset,\n      index\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = scale => {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var minValue = Math.min(range[0], range[1]) - EPS;\n  var maxValue = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < minValue || first > maxValue || last < minValue || last > maxValue) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\n\n/**\n * Both value and domain are tuples of two numbers\n * - but the type stays as array of numbers until we have better support in rest of the app\n * @param value input that will be truncated\n * @param domain boundaries\n * @returns tuple of two numbers\n */\nexport var truncateByDomain = (value, domain) => {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var minValue = Math.min(domain[0], domain[1]);\n  var maxValue = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < minValue) {\n    result[0] = minValue;\n  }\n  if (!isNumber(value[1]) || value[1] > maxValue) {\n    result[1] = maxValue;\n  }\n  if (result[0] > maxValue) {\n    result[0] = maxValue;\n  }\n  if (result[1] < minValue) {\n    result[1] = minValue;\n  }\n  return result;\n};\n\n/**\n * Stacks all positive numbers above zero and all negative numbers below zero.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetSign = series => {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Replaces all negative values with zero when stacking data.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetPositive = series => {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Function type to compute offset for stacked data.\n *\n * d3-shape has something fishy going on with its types.\n * In @definitelytyped/d3-shape, this function (the offset accessor) is typed as Series<> => void.\n * However! When I actually open the storybook I can see that the offset accessor actually receives Array<Series<>>.\n * The same I can see in the source code itself:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n * That one unfortunately has no types but we can tell it passes three-dimensional array.\n *\n * Which leads me to believe that definitelytyped is wrong on this one.\n * There's open discussion on this topic without much attention:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n */\n\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  // @ts-expect-error definitelytyped types are incorrect\n  expand: stackOffsetExpand,\n  // @ts-expect-error definitelytyped types are incorrect\n  none: stackOffsetNone,\n  // @ts-expect-error definitelytyped types are incorrect\n  silhouette: stackOffsetSilhouette,\n  // @ts-expect-error definitelytyped types are incorrect\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = (data, dataKeys, offsetType) => {\n  var offsetAccessor = STACK_OFFSET_MAP[offsetType];\n  var stack = shapeStack().keys(dataKeys).value((d, key) => +getValueByDataKey(d, key, 0)).order(stackOrderNone)\n  // @ts-expect-error definitelytyped types are incorrect\n  .offset(offsetAccessor);\n  return stack(data);\n};\n\n/**\n * Stack IDs in the external props allow numbers; but internally we use it as an object key\n * and object keys are always strings. Also, it would be kinda confusing if stackId=8 and stackId='8' were different stacks\n * so let's just force a string.\n */\n\nexport function getNormalizedStackId(publicStackId) {\n  return publicStackId == null ? undefined : String(publicStackId);\n}\nexport function getCateCoordinateOfLine(_ref) {\n  var {\n    axis,\n    ticks,\n    bandSize,\n    entry,\n    index,\n    dataKey\n  } = _ref;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !isNullish(entry[axis.dataKey])) {\n      // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !isNullish(dataKey) ? dataKey : axis.dataKey);\n\n  // @ts-expect-error getValueByDataKey does not validate the output type\n  return !isNullish(value) ? axis.scale(value) : null;\n}\nexport var getCateCoordinateOfBar = _ref2 => {\n  var {\n    axis,\n    ticks,\n    offset,\n    bandSize,\n    entry,\n    index\n  } = _ref2;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.scale.domain()[index]);\n  return !isNullish(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = _ref3 => {\n  var {\n    numericAxis\n  } = _ref3;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    // @ts-expect-error type number means the domain has numbers in it but this relationship is not known to typescript\n    var minValue = Math.min(domain[0], domain[1]);\n    // @ts-expect-error type number means the domain has numbers in it but this relationship is not known to typescript\n    var maxValue = Math.max(domain[0], domain[1]);\n    if (minValue <= 0 && maxValue >= 0) {\n      return 0;\n    }\n    if (maxValue < 0) {\n      return maxValue;\n    }\n    return minValue;\n  }\n  return domain[0];\n};\nvar getDomainOfSingle = data => {\n  var flat = data.flat(2).filter(isNumber);\n  return [Math.min(...flat), Math.max(...flat)];\n};\nvar makeDomainFinite = domain => {\n  return [domain[0] === Infinity ? 0 : domain[0], domain[1] === -Infinity ? 0 : domain[1]];\n};\nexport var getDomainOfStackGroups = (stackGroups, startIndex, endIndex) => {\n  if (stackGroups == null) {\n    return undefined;\n  }\n  return makeDomainFinite(Object.keys(stackGroups).reduce((result, stackId) => {\n    var group = stackGroups[stackId];\n    var {\n      stackedData\n    } = group;\n    var domain = stackedData.reduce((res, entry) => {\n      var sliced = getSliced(entry, startIndex, endIndex);\n      var s = getDomainOfSingle(sliced);\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]));\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = (axis, ticks, isBar) => {\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = sortBy(ticks, o => o.coordinate);\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\nexport function getTooltipEntry(_ref4) {\n  var {\n    tooltipEntrySettings,\n    dataKey,\n    payload,\n    value,\n    name\n  } = _ref4;\n  return _objectSpread(_objectSpread({}, tooltipEntrySettings), {}, {\n    dataKey,\n    payload,\n    value,\n    name\n  });\n}\nexport function getTooltipNameProp(nameFromItem, dataKey) {\n  if (nameFromItem) {\n    return String(nameFromItem);\n  }\n  if (typeof dataKey === 'string') {\n    return dataKey;\n  }\n  return undefined;\n}\nexport function inRange(x, y, layout, polarViewBox, offset) {\n  if (layout === 'horizontal' || layout === 'vertical') {\n    var isInRange = x >= offset.left && x <= offset.left + offset.width && y >= offset.top && y <= offset.top + offset.height;\n    return isInRange ? {\n      x,\n      y\n    } : null;\n  }\n  if (polarViewBox) {\n    return inRangeOfSector({\n      x,\n      y\n    }, polarViewBox);\n  }\n  return null;\n}\nexport var getActiveCoordinate = (layout, tooltipTicks, activeIndex, rangeObj) => {\n  var entry = tooltipTicks.find(tick => tick && tick.index === activeIndex);\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var {\n        radius: _radius\n      } = rangeObj;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var {\n      angle\n    } = rangeObj;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle,\n      radius\n    });\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n};\nexport var calculateTooltipPos = (rangeObj, layout) => {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,OAAO8B,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,IAAIC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,yBAAyB;AAC3J,SAASC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,aAAa;AAChG,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,cAAc;AAChE,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,OAAO,EAAEC,YAAY,EAAE;EAC5D,IAAIV,SAAS,CAACQ,GAAG,CAAC,IAAIR,SAAS,CAACS,OAAO,CAAC,EAAE;IACxC,OAAOC,YAAY;EACrB;EACA,IAAIR,UAAU,CAACO,OAAO,CAAC,EAAE;IACvB,OAAOnB,GAAG,CAACkB,GAAG,EAAEC,OAAO,EAAEC,YAAY,CAAC;EACxC;EACA,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjC,OAAOA,OAAO,CAACD,GAAG,CAAC;EACrB;EACA,OAAOE,YAAY;AACrB;AACA,OAAO,IAAIC,wBAAwB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,KAAK,KAAK;EAC3F,IAAIC,aAAa;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,GAAG,GAAG,CAACF,aAAa,GAAGJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC1C,MAAM,MAAM,IAAI,IAAI8C,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;;EAE/I;EACA,IAAIE,GAAG,IAAI,CAAC,IAAIP,UAAU,IAAI,IAAI,EAAE;IAClC,OAAO,CAAC;EACV;EACA,IAAIG,QAAQ,KAAK,WAAW,IAAIC,KAAK,IAAI,IAAI,IAAII,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE;IACtG;IACA,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;MAC5B,IAAIyC,MAAM,GAAGzC,CAAC,GAAG,CAAC,GAAGiC,aAAa,CAACjC,CAAC,GAAG,CAAC,CAAC,CAAC+B,UAAU,GAAGE,aAAa,CAACK,GAAG,GAAG,CAAC,CAAC,CAACP,UAAU;MACxF,IAAIW,GAAG,GAAGT,aAAa,CAACjC,CAAC,CAAC,CAAC+B,UAAU;MACrC,IAAIY,KAAK,GAAG3C,CAAC,IAAIsC,GAAG,GAAG,CAAC,GAAGL,aAAa,CAAC,CAAC,CAAC,CAACF,UAAU,GAAGE,aAAa,CAACjC,CAAC,GAAG,CAAC,CAAC,CAAC+B,UAAU;MACxF,IAAIa,kBAAkB,GAAG,KAAK,CAAC;MAC/B,IAAItB,QAAQ,CAACoB,GAAG,GAAGD,MAAM,CAAC,KAAKnB,QAAQ,CAACqB,KAAK,GAAGD,GAAG,CAAC,EAAE;QACpD,IAAIG,YAAY,GAAG,EAAE;QACrB,IAAIvB,QAAQ,CAACqB,KAAK,GAAGD,GAAG,CAAC,KAAKpB,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3DS,kBAAkB,GAAGD,KAAK;UAC1B,IAAIG,UAAU,GAAGJ,GAAG,GAAGP,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC1CU,YAAY,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACQ,GAAG,CAACD,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;UACjEI,YAAY,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACS,GAAG,CAACF,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;QACnE,CAAC,MAAM;UACLG,kBAAkB,GAAGH,MAAM;UAC3B,IAAIQ,YAAY,GAAGN,KAAK,GAAGR,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC9CU,YAAY,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACQ,GAAG,CAACL,GAAG,EAAE,CAACO,YAAY,GAAGP,GAAG,IAAI,CAAC,CAAC;UACzDG,YAAY,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACS,GAAG,CAACN,GAAG,EAAE,CAACO,YAAY,GAAGP,GAAG,IAAI,CAAC,CAAC;QAC3D;QACA,IAAIQ,YAAY,GAAG,CAACX,IAAI,CAACQ,GAAG,CAACL,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,EAAEH,IAAI,CAACS,GAAG,CAACN,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,CAAC;QACjH,IAAIX,UAAU,GAAGmB,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,IAAImB,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,IAAIc,YAAY,CAAC,CAAC,CAAC,IAAId,UAAU,IAAIc,YAAY,CAAC,CAAC,CAAC,EAAE;UACnI,CAAC;YACCR;UACF,CAAC,GAAGJ,aAAa,CAACjC,CAAC,CAAC;UACpB;QACF;MACF,CAAC,MAAM;QACL,IAAImD,QAAQ,GAAGZ,IAAI,CAACQ,GAAG,CAACN,MAAM,EAAEE,KAAK,CAAC;QACtC,IAAIS,QAAQ,GAAGb,IAAI,CAACS,GAAG,CAACP,MAAM,EAAEE,KAAK,CAAC;QACtC,IAAIZ,UAAU,GAAG,CAACoB,QAAQ,GAAGT,GAAG,IAAI,CAAC,IAAIX,UAAU,IAAI,CAACqB,QAAQ,GAAGV,GAAG,IAAI,CAAC,EAAE;UAC3E,CAAC;YACCL;UACF,CAAC,GAAGJ,aAAa,CAACjC,CAAC,CAAC;UACpB;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAIgC,KAAK,EAAE;IAChB;IACA,KAAK,IAAIqB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGf,GAAG,EAAEe,EAAE,EAAE,EAAE;MAC/B,IAAIA,EAAE,KAAK,CAAC,IAAItB,UAAU,IAAI,CAACC,KAAK,CAACqB,EAAE,CAAC,CAACtB,UAAU,GAAGC,KAAK,CAACqB,EAAE,GAAG,CAAC,CAAC,CAACtB,UAAU,IAAI,CAAC,IAAIsB,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGf,GAAG,GAAG,CAAC,IAAIP,UAAU,GAAG,CAACC,KAAK,CAACqB,EAAE,CAAC,CAACtB,UAAU,GAAGC,KAAK,CAACqB,EAAE,GAAG,CAAC,CAAC,CAACtB,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,CAACC,KAAK,CAACqB,EAAE,CAAC,CAACtB,UAAU,GAAGC,KAAK,CAACqB,EAAE,GAAG,CAAC,CAAC,CAACtB,UAAU,IAAI,CAAC,IAAIsB,EAAE,KAAKf,GAAG,GAAG,CAAC,IAAIP,UAAU,GAAG,CAACC,KAAK,CAACqB,EAAE,CAAC,CAACtB,UAAU,GAAGC,KAAK,CAACqB,EAAE,GAAG,CAAC,CAAC,CAACtB,UAAU,IAAI,CAAC,EAAE;QAClV,CAAC;UACCM;QACF,CAAC,GAAGL,KAAK,CAACqB,EAAE,CAAC;QACb;MACF;IACF;EACF;EACA,OAAOhB,KAAK;AACd,CAAC;AACD,OAAO,IAAIiB,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,cAAc,EAAEC,UAAU,KAAK;EACxE,IAAID,cAAc,IAAIC,UAAU,EAAE;IAChC,IAAI;MACFC,KAAK,EAAEC,QAAQ;MACfC,MAAM,EAAEC;IACV,CAAC,GAAGJ,UAAU;IACd,IAAI;MACFK,KAAK;MACLC,aAAa;MACbC;IACF,CAAC,GAAGR,cAAc;IAClB,IAAI,CAACQ,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,YAAY,IAAID,aAAa,KAAK,QAAQ,KAAKD,KAAK,KAAK,QAAQ,IAAI1C,QAAQ,CAACmC,MAAM,CAACO,KAAK,CAAC,CAAC,EAAE;MACrI,OAAO1E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClD,CAACO,KAAK,GAAGP,MAAM,CAACO,KAAK,CAAC,IAAIH,QAAQ,IAAI,CAAC;MACzC,CAAC,CAAC;IACJ;IACA,IAAI,CAACK,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIF,KAAK,KAAK,QAAQ,KAAKC,aAAa,KAAK,QAAQ,IAAI3C,QAAQ,CAACmC,MAAM,CAACQ,aAAa,CAAC,CAAC,EAAE;MAC7I,OAAO3E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClD,CAACQ,aAAa,GAAGR,MAAM,CAACQ,aAAa,CAAC,IAAIF,SAAS,IAAI,CAAC;MAC1D,CAAC,CAAC;IACJ;EACF;EACA,OAAON,MAAM;AACf,CAAC;AACD,OAAO,IAAIU,iBAAiB,GAAGA,CAACD,MAAM,EAAE9B,QAAQ,KAAK8B,MAAM,KAAK,YAAY,IAAI9B,QAAQ,KAAK,OAAO,IAAI8B,MAAM,KAAK,UAAU,IAAI9B,QAAQ,KAAK,OAAO,IAAI8B,MAAM,KAAK,SAAS,IAAI9B,QAAQ,KAAK,WAAW,IAAI8B,MAAM,KAAK,QAAQ,IAAI9B,QAAQ,KAAK,YAAY;;AAE7P;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgC,oBAAoB,GAAGA,CAAClC,KAAK,EAAEmB,QAAQ,EAAEC,QAAQ,EAAEe,aAAa,KAAK;EAC9E,IAAIA,aAAa,EAAE;IACjB,OAAOnC,KAAK,CAACoC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACtC,UAAU,CAAC;EAC7C;EACA,IAAIuC,MAAM,EAAEC,MAAM;EAClB,IAAIC,MAAM,GAAGxC,KAAK,CAACoC,GAAG,CAACC,KAAK,IAAI;IAC9B,IAAIA,KAAK,CAACtC,UAAU,KAAKoB,QAAQ,EAAE;MACjCmB,MAAM,GAAG,IAAI;IACf;IACA,IAAID,KAAK,CAACtC,UAAU,KAAKqB,QAAQ,EAAE;MACjCmB,MAAM,GAAG,IAAI;IACf;IACA,OAAOF,KAAK,CAACtC,UAAU;EACzB,CAAC,CAAC;EACF,IAAI,CAACuC,MAAM,EAAE;IACXE,MAAM,CAACtF,IAAI,CAACiE,QAAQ,CAAC;EACvB;EACA,IAAI,CAACoB,MAAM,EAAE;IACXC,MAAM,CAACtF,IAAI,CAACkE,QAAQ,CAAC;EACvB;EACA,OAAOoB,MAAM;AACf,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK;EACnD,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,IAAI;IACFG,eAAe;IACfC,IAAI;IACJ3C,KAAK;IACL4C,KAAK;IACLC,aAAa;IACbC,aAAa;IACbC,iBAAiB;IACjBC,SAAS;IACTnD,KAAK;IACLoD,SAAS;IACTlD;EACF,CAAC,GAAGwC,IAAI;EACR,IAAI,CAACK,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAIM,aAAa,GAAGL,aAAa,KAAK,WAAW,IAAID,KAAK,CAACO,SAAS,GAAGP,KAAK,CAACO,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAChG,IAAI/B,MAAM,GAAG,CAACoB,MAAM,IAAIC,KAAK,KAAKE,IAAI,KAAK,UAAU,IAAIC,KAAK,CAACO,SAAS,GAAGP,KAAK,CAACO,SAAS,CAAC,CAAC,GAAGD,aAAa,GAAG,CAAC;EAChH9B,MAAM,GAAGrB,QAAQ,KAAK,WAAW,IAAIC,KAAK,IAAIA,KAAK,CAAC7C,MAAM,IAAI,CAAC,GAAGgC,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGoB,MAAM,GAAGA,MAAM;;EAErH;EACA,IAAIoB,MAAM,KAAK3C,KAAK,IAAIoD,SAAS,CAAC,EAAE;IAClC,IAAIG,MAAM,GAAG,CAACvD,KAAK,IAAIoD,SAAS,IAAI,EAAE,EAAEhB,GAAG,CAAC,CAACC,KAAK,EAAEhC,KAAK,KAAK;MAC5D,IAAImD,YAAY,GAAGX,eAAe,GAAGA,eAAe,CAACY,OAAO,CAACpB,KAAK,CAAC,GAAGA,KAAK;MAC3E,OAAO;QACL;QACA;QACAtC,UAAU,EAAEgD,KAAK,CAACS,YAAY,CAAC,GAAGjC,MAAM;QACxC1D,KAAK,EAAEwE,KAAK;QACZd,MAAM;QACNlB;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAOkD,MAAM,CAACxG,MAAM,CAAC2G,GAAG,IAAI,CAACxE,KAAK,CAACwE,GAAG,CAAC3D,UAAU,CAAC,CAAC;EACrD;;EAEA;EACA,IAAIkD,aAAa,IAAIC,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAACd,GAAG,CAAC,CAACC,KAAK,EAAEhC,KAAK,MAAM;MAC9CN,UAAU,EAAEgD,KAAK,CAACV,KAAK,CAAC,GAAGd,MAAM;MACjC1D,KAAK,EAAEwE,KAAK;MACZhC,KAAK;MACLkB;IACF,CAAC,CAAC,CAAC;EACL;EACA,IAAIwB,KAAK,CAAC/C,KAAK,IAAI,CAAC4C,KAAK,IAAIO,SAAS,IAAI,IAAI,EAAE;IAC9C,OAAOJ,KAAK,CAAC/C,KAAK,CAACmD,SAAS,CAAC,CAACf,GAAG,CAAC,CAACC,KAAK,EAAEhC,KAAK,MAAM;MACnDN,UAAU,EAAEgD,KAAK,CAACV,KAAK,CAAC,GAAGd,MAAM;MACjC1D,KAAK,EAAEwE,KAAK;MACZd,MAAM;MACNlB;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAO0C,KAAK,CAACY,MAAM,CAAC,CAAC,CAACvB,GAAG,CAAC,CAACC,KAAK,EAAEhC,KAAK,MAAM;IAC3CN,UAAU,EAAEgD,KAAK,CAACV,KAAK,CAAC,GAAGd,MAAM;IACjC1D,KAAK,EAAEgF,eAAe,GAAGA,eAAe,CAACR,KAAK,CAAC,GAAGA,KAAK;IACvDhC,KAAK;IACLkB;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIqC,GAAG,GAAG,IAAI;AACd,OAAO,IAAIC,kBAAkB,GAAGd,KAAK,IAAI;EACvC,IAAIY,MAAM,GAAGZ,KAAK,CAACY,MAAM,CAAC,CAAC;EAC3B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACrG,MAAM,IAAI,CAAC,EAAE;IACjC;EACF;EACA,IAAIgD,GAAG,GAAGqD,MAAM,CAACrG,MAAM;EACvB,IAAI6C,KAAK,GAAG4C,KAAK,CAAC5C,KAAK,CAAC,CAAC;EACzB,IAAIgB,QAAQ,GAAGZ,IAAI,CAACQ,GAAG,CAACZ,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGyD,GAAG;EACjD,IAAIxC,QAAQ,GAAGb,IAAI,CAACS,GAAG,CAACb,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGyD,GAAG;EACjD,IAAIE,KAAK,GAAGf,KAAK,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAII,IAAI,GAAGhB,KAAK,CAACY,MAAM,CAACrD,GAAG,GAAG,CAAC,CAAC,CAAC;EACjC,IAAIwD,KAAK,GAAG3C,QAAQ,IAAI2C,KAAK,GAAG1C,QAAQ,IAAI2C,IAAI,GAAG5C,QAAQ,IAAI4C,IAAI,GAAG3C,QAAQ,EAAE;IAC9E2B,KAAK,CAACY,MAAM,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAACrD,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI0D,gBAAgB,GAAGA,CAACnG,KAAK,EAAE8F,MAAM,KAAK;EAC/C,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACrG,MAAM,KAAK,CAAC,IAAI,CAAC8B,QAAQ,CAACuE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAACvE,QAAQ,CAACuE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAClF,OAAO9F,KAAK;EACd;EACA,IAAIsD,QAAQ,GAAGZ,IAAI,CAACQ,GAAG,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAIvC,QAAQ,GAAGb,IAAI,CAACS,GAAG,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAIJ,MAAM,GAAG,CAAC1F,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,IAAI,CAACuB,QAAQ,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGsD,QAAQ,EAAE;IAC9CoC,MAAM,CAAC,CAAC,CAAC,GAAGpC,QAAQ;EACtB;EACA,IAAI,CAAC/B,QAAQ,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGuD,QAAQ,EAAE;IAC9CmC,MAAM,CAAC,CAAC,CAAC,GAAGnC,QAAQ;EACtB;EACA,IAAImC,MAAM,CAAC,CAAC,CAAC,GAAGnC,QAAQ,EAAE;IACxBmC,MAAM,CAAC,CAAC,CAAC,GAAGnC,QAAQ;EACtB;EACA,IAAImC,MAAM,CAAC,CAAC,CAAC,GAAGpC,QAAQ,EAAE;IACxBoC,MAAM,CAAC,CAAC,CAAC,GAAGpC,QAAQ;EACtB;EACA,OAAOoC,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIU,UAAU,GAAGC,MAAM,IAAI;EAChC,IAAIC,CAAC,GAAGD,MAAM,CAAC5G,MAAM;EACrB,IAAI6G,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAC5G,MAAM,EAAE8G,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAChD,IAAIE,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmG,CAAC,EAAE,EAAEnG,CAAC,EAAE;MAC1B,IAAIH,KAAK,GAAGqB,KAAK,CAACgF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE;MACA,IAAIvG,KAAK,IAAI,CAAC,EAAE;QACdqG,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ;QAC1BJ,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,GAAGzG,KAAK;QAClCyG,QAAQ,GAAGJ,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGG,QAAQ;QAC1BL,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGG,QAAQ,GAAG1G,KAAK;QAClC0G,QAAQ,GAAGL,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA;IACF;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,cAAc,GAAGN,MAAM,IAAI;EACpC,IAAIC,CAAC,GAAGD,MAAM,CAAC5G,MAAM;EACrB,IAAI6G,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAC5G,MAAM,EAAE8G,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAChD,IAAIE,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmG,CAAC,EAAE,EAAEnG,CAAC,EAAE;MAC1B,IAAIH,KAAK,GAAGqB,KAAK,CAACgF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE;MACA,IAAIvG,KAAK,IAAI,CAAC,EAAE;QACdqG,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ;QAC1BJ,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,QAAQ,GAAGzG,KAAK;QAClCyG,QAAQ,GAAGJ,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACnBF,MAAM,CAAClG,CAAC,CAAC,CAACoG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACrB;MACA;IACF;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIK,gBAAgB,GAAG;EACrBC,IAAI,EAAET,UAAU;EAChB;EACAU,MAAM,EAAE/F,iBAAiB;EACzB;EACAgG,IAAI,EAAE/F,eAAe;EACrB;EACAgG,UAAU,EAAE/F,qBAAqB;EACjC;EACAgG,MAAM,EAAE/F,iBAAiB;EACzBuF,QAAQ,EAAEE;AACZ,CAAC;AACD,OAAO,IAAIO,cAAc,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EAC1D,IAAIC,cAAc,GAAGV,gBAAgB,CAACS,UAAU,CAAC;EACjD,IAAIxG,KAAK,GAAGC,UAAU,CAAC,CAAC,CAAC/B,IAAI,CAACqI,QAAQ,CAAC,CAACpH,KAAK,CAAC,CAACuH,CAAC,EAAEC,GAAG,KAAK,CAAC3F,iBAAiB,CAAC0F,CAAC,EAAEC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACC,KAAK,CAACtG,cAAc;EAC7G;EAAA,CACCuC,MAAM,CAAC4D,cAAc,CAAC;EACvB,OAAOzG,KAAK,CAACsG,IAAI,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASO,oBAAoBA,CAACC,aAAa,EAAE;EAClD,OAAOA,aAAa,IAAI,IAAI,GAAGC,SAAS,GAAGnH,MAAM,CAACkH,aAAa,CAAC;AAClE;AACA,OAAO,SAASE,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,IAAI;IACFjD,IAAI;IACJ1C,KAAK;IACL4F,QAAQ;IACRvD,KAAK;IACLhC,KAAK;IACLT;EACF,CAAC,GAAG+F,IAAI;EACR,IAAIjD,IAAI,CAACI,IAAI,KAAK,UAAU,EAAE;IAC5B;IACA;IACA,IAAI,CAACJ,IAAI,CAACmD,uBAAuB,IAAInD,IAAI,CAAC9C,OAAO,IAAI,CAACT,SAAS,CAACkD,KAAK,CAACK,IAAI,CAAC9C,OAAO,CAAC,CAAC,EAAE;MACpF;MACA,IAAIkG,WAAW,GAAG7G,gBAAgB,CAACe,KAAK,EAAE,OAAO,EAAEqC,KAAK,CAACK,IAAI,CAAC9C,OAAO,CAAC,CAAC;MACvE,IAAIkG,WAAW,EAAE;QACf,OAAOA,WAAW,CAAC/F,UAAU,GAAG6F,QAAQ,GAAG,CAAC;MAC9C;IACF;IACA,OAAO5F,KAAK,CAACK,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,CAAC,CAACN,UAAU,GAAG6F,QAAQ,GAAG,CAAC,GAAG,IAAI;EACrE;EACA,IAAI/H,KAAK,GAAG6B,iBAAiB,CAAC2C,KAAK,EAAE,CAAClD,SAAS,CAACS,OAAO,CAAC,GAAGA,OAAO,GAAG8C,IAAI,CAAC9C,OAAO,CAAC;;EAElF;EACA,OAAO,CAACT,SAAS,CAACtB,KAAK,CAAC,GAAG6E,IAAI,CAACK,KAAK,CAAClF,KAAK,CAAC,GAAG,IAAI;AACrD;AACA,OAAO,IAAIkI,sBAAsB,GAAGC,KAAK,IAAI;EAC3C,IAAI;IACFtD,IAAI;IACJ1C,KAAK;IACLuB,MAAM;IACNqE,QAAQ;IACRvD,KAAK;IACLhC;EACF,CAAC,GAAG2F,KAAK;EACT,IAAItD,IAAI,CAACI,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO9C,KAAK,CAACK,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,CAAC,CAACN,UAAU,GAAGwB,MAAM,GAAG,IAAI;EAC/D;EACA,IAAI1D,KAAK,GAAG6B,iBAAiB,CAAC2C,KAAK,EAAEK,IAAI,CAAC9C,OAAO,EAAE8C,IAAI,CAACK,KAAK,CAACY,MAAM,CAAC,CAAC,CAACtD,KAAK,CAAC,CAAC;EAC9E,OAAO,CAAClB,SAAS,CAACtB,KAAK,CAAC,GAAG6E,IAAI,CAACK,KAAK,CAAClF,KAAK,CAAC,GAAG+H,QAAQ,GAAG,CAAC,GAAGrE,MAAM,GAAG,IAAI;AAC7E,CAAC;AACD,OAAO,IAAI0E,iBAAiB,GAAGC,KAAK,IAAI;EACtC,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK;EACT,IAAIvC,MAAM,GAAGwC,WAAW,CAACpD,KAAK,CAACY,MAAM,CAAC,CAAC;EACvC,IAAIwC,WAAW,CAACrD,IAAI,KAAK,QAAQ,EAAE;IACjC;IACA,IAAI3B,QAAQ,GAAGZ,IAAI,CAACQ,GAAG,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,IAAIvC,QAAQ,GAAGb,IAAI,CAACS,GAAG,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAIxC,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,EAAE;MAClC,OAAO,CAAC;IACV;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAOA,QAAQ;IACjB;IACA,OAAOD,QAAQ;EACjB;EACA,OAAOwC,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIyC,iBAAiB,GAAGpB,IAAI,IAAI;EAC9B,IAAIqB,IAAI,GAAGrB,IAAI,CAACqB,IAAI,CAAC,CAAC,CAAC,CAACtJ,MAAM,CAACqC,QAAQ,CAAC;EACxC,OAAO,CAACmB,IAAI,CAACQ,GAAG,CAAC,GAAGsF,IAAI,CAAC,EAAE9F,IAAI,CAACS,GAAG,CAAC,GAAGqF,IAAI,CAAC,CAAC;AAC/C,CAAC;AACD,IAAIC,gBAAgB,GAAG3C,MAAM,IAAI;EAC/B,OAAO,CAACA,MAAM,CAAC,CAAC,CAAC,KAAK4C,QAAQ,GAAG,CAAC,GAAG5C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC4C,QAAQ,GAAG,CAAC,GAAG5C,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC;AACD,OAAO,IAAI6C,sBAAsB,GAAGA,CAACC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,KAAK;EACzE,IAAIF,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOhB,SAAS;EAClB;EACA,OAAOa,gBAAgB,CAAC3J,MAAM,CAACC,IAAI,CAAC6J,WAAW,CAAC,CAACG,MAAM,CAAC,CAACrD,MAAM,EAAEsD,OAAO,KAAK;IAC3E,IAAIC,KAAK,GAAGL,WAAW,CAACI,OAAO,CAAC;IAChC,IAAI;MACFE;IACF,CAAC,GAAGD,KAAK;IACT,IAAInD,MAAM,GAAGoD,WAAW,CAACH,MAAM,CAAC,CAACI,GAAG,EAAE3E,KAAK,KAAK;MAC9C,IAAI4E,MAAM,GAAGxH,SAAS,CAAC4C,KAAK,EAAEqE,UAAU,EAAEC,QAAQ,CAAC;MACnD,IAAIO,CAAC,GAAGd,iBAAiB,CAACa,MAAM,CAAC;MACjC,OAAO,CAAC1G,IAAI,CAACQ,GAAG,CAACiG,GAAG,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE3G,IAAI,CAACS,GAAG,CAACgG,GAAG,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,EAAE,CAACX,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;IACzB,OAAO,CAAChG,IAAI,CAACQ,GAAG,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEhD,IAAI,CAACS,GAAG,CAAC2C,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC,EAAE,CAACgD,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD,OAAO,IAAIY,aAAa,GAAG,iDAAiD;AAC5E,OAAO,IAAIC,aAAa,GAAG,kDAAkD;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB,GAAGA,CAAC3E,IAAI,EAAE1C,KAAK,EAAEsH,KAAK,KAAK;EACrD,IAAI5E,IAAI,IAAIA,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACK,KAAK,CAACO,SAAS,EAAE;IAC9C,IAAIiE,SAAS,GAAG7E,IAAI,CAACK,KAAK,CAACO,SAAS,CAAC,CAAC;IACtC,IAAI,CAACgE,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAE;MAC3B,OAAOA,SAAS;IAClB;EACF;EACA,IAAI7E,IAAI,IAAI1C,KAAK,IAAIA,KAAK,CAAC1C,MAAM,IAAI,CAAC,EAAE;IACtC,IAAIkK,YAAY,GAAGhJ,MAAM,CAACwB,KAAK,EAAElD,CAAC,IAAIA,CAAC,CAACiD,UAAU,CAAC;IACnD,IAAI6F,QAAQ,GAAGW,QAAQ;IACvB,KAAK,IAAIvI,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGkH,YAAY,CAAClK,MAAM,EAAEU,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;MACvD,IAAI0C,GAAG,GAAG8G,YAAY,CAACxJ,CAAC,CAAC;MACzB,IAAIyJ,IAAI,GAAGD,YAAY,CAACxJ,CAAC,GAAG,CAAC,CAAC;MAC9B4H,QAAQ,GAAGrF,IAAI,CAACQ,GAAG,CAAC,CAACL,GAAG,CAACX,UAAU,IAAI,CAAC,KAAK0H,IAAI,CAAC1H,UAAU,IAAI,CAAC,CAAC,EAAE6F,QAAQ,CAAC;IAC/E;IACA,OAAOA,QAAQ,KAAKW,QAAQ,GAAG,CAAC,GAAGX,QAAQ;EAC7C;EACA,OAAO0B,KAAK,GAAG7B,SAAS,GAAG,CAAC;AAC9B,CAAC;AACD,OAAO,SAASiC,eAAeA,CAACC,KAAK,EAAE;EACrC,IAAI;IACFC,oBAAoB;IACpBhI,OAAO;IACPiI,OAAO;IACPhK,KAAK;IACLiK;EACF,CAAC,GAAGH,KAAK;EACT,OAAOvK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwK,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;IAChEhI,OAAO;IACPiI,OAAO;IACPhK,KAAK;IACLiK;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,kBAAkBA,CAACC,YAAY,EAAEpI,OAAO,EAAE;EACxD,IAAIoI,YAAY,EAAE;IAChB,OAAO1J,MAAM,CAAC0J,YAAY,CAAC;EAC7B;EACA,IAAI,OAAOpI,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EACA,OAAO6F,SAAS;AAClB;AACA,OAAO,SAASwC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEnG,MAAM,EAAEoG,YAAY,EAAE7G,MAAM,EAAE;EAC1D,IAAIS,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;IACpD,IAAIqG,SAAS,GAAGH,CAAC,IAAI3G,MAAM,CAAC+G,IAAI,IAAIJ,CAAC,IAAI3G,MAAM,CAAC+G,IAAI,GAAG/G,MAAM,CAACG,KAAK,IAAIyG,CAAC,IAAI5G,MAAM,CAACgH,GAAG,IAAIJ,CAAC,IAAI5G,MAAM,CAACgH,GAAG,GAAGhH,MAAM,CAACK,MAAM;IACzH,OAAOyG,SAAS,GAAG;MACjBH,CAAC;MACDC;IACF,CAAC,GAAG,IAAI;EACV;EACA,IAAIC,YAAY,EAAE;IAChB,OAAO7I,eAAe,CAAC;MACrB2I,CAAC;MACDC;IACF,CAAC,EAAEC,YAAY,CAAC;EAClB;EACA,OAAO,IAAI;AACb;AACA,OAAO,IAAII,mBAAmB,GAAGA,CAACxG,MAAM,EAAEyG,YAAY,EAAEC,WAAW,EAAEC,QAAQ,KAAK;EAChF,IAAItG,KAAK,GAAGoG,YAAY,CAACG,IAAI,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACxI,KAAK,KAAKqI,WAAW,CAAC;EACzE,IAAIrG,KAAK,EAAE;IACT,IAAIL,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLkG,CAAC,EAAE7F,KAAK,CAACtC,UAAU;QACnBoI,CAAC,EAAEQ,QAAQ,CAACR;MACd,CAAC;IACH;IACA,IAAInG,MAAM,KAAK,UAAU,EAAE;MACzB,OAAO;QACLkG,CAAC,EAAES,QAAQ,CAACT,CAAC;QACbC,CAAC,EAAE9F,KAAK,CAACtC;MACX,CAAC;IACH;IACA,IAAIiC,MAAM,KAAK,SAAS,EAAE;MACxB,IAAI8G,MAAM,GAAGzG,KAAK,CAACtC,UAAU;MAC7B,IAAI;QACFgJ,MAAM,EAAEC;MACV,CAAC,GAAGL,QAAQ;MACZ,OAAOvL,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuL,QAAQ,CAAC,EAAEnJ,gBAAgB,CAACmJ,QAAQ,CAACM,EAAE,EAAEN,QAAQ,CAACO,EAAE,EAAEF,OAAO,EAAEF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChIK,KAAK,EAAEL,MAAM;QACbC,MAAM,EAAEC;MACV,CAAC,CAAC;IACJ;IACA,IAAID,MAAM,GAAG1G,KAAK,CAACtC,UAAU;IAC7B,IAAI;MACFoJ;IACF,CAAC,GAAGR,QAAQ;IACZ,OAAOvL,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuL,QAAQ,CAAC,EAAEnJ,gBAAgB,CAACmJ,QAAQ,CAACM,EAAE,EAAEN,QAAQ,CAACO,EAAE,EAAEH,MAAM,EAAEI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9HA,KAAK;MACLJ;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACLb,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;AACH,CAAC;AACD,OAAO,IAAIiB,mBAAmB,GAAGA,CAACT,QAAQ,EAAE3G,MAAM,KAAK;EACrD,IAAIA,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO2G,QAAQ,CAACT,CAAC;EACnB;EACA,IAAIlG,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO2G,QAAQ,CAACR,CAAC;EACnB;EACA,IAAInG,MAAM,KAAK,SAAS,EAAE;IACxB,OAAO2G,QAAQ,CAACQ,KAAK;EACvB;EACA,OAAOR,QAAQ,CAACI,MAAM;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
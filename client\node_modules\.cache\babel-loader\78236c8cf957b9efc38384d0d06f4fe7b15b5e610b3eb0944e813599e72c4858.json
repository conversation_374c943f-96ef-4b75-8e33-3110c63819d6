{"ast": null, "code": "import { differenceBy } from './differenceBy.mjs';\nimport { intersectionBy } from './intersectionBy.mjs';\nimport { unionBy } from './unionBy.mjs';\nfunction xorBy(arr1, arr2, mapper) {\n  const union = unionBy(arr1, arr2, mapper);\n  const intersection = intersectionBy(arr1, arr2, mapper);\n  return differenceBy(union, intersection, mapper);\n}\nexport { xorBy };", "map": {"version": 3, "names": ["differenceBy", "intersectionBy", "unionBy", "xorBy", "arr1", "arr2", "mapper", "union", "intersection"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/xorBy.mjs"], "sourcesContent": ["import { differenceBy } from './differenceBy.mjs';\nimport { intersectionBy } from './intersectionBy.mjs';\nimport { unionBy } from './unionBy.mjs';\n\nfunction xorBy(arr1, arr2, mapper) {\n    const union = unionBy(arr1, arr2, mapper);\n    const intersection = intersectionBy(arr1, arr2, mapper);\n    return differenceBy(union, intersection, mapper);\n}\n\nexport { xorBy };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,eAAe;AAEvC,SAASC,KAAKA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC/B,MAAMC,KAAK,GAAGL,OAAO,CAACE,IAAI,EAAEC,IAAI,EAAEC,MAAM,CAAC;EACzC,MAAME,YAAY,GAAGP,cAAc,CAACG,IAAI,EAAEC,IAAI,EAAEC,MAAM,CAAC;EACvD,OAAON,YAAY,CAACO,KAAK,EAAEC,YAAY,EAAEF,MAAM,CAAC;AACpD;AAEA,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
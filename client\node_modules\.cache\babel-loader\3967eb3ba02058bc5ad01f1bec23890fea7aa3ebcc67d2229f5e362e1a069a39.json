{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isDeepKey(key) {\n  switch (typeof key) {\n    case 'number':\n    case 'symbol':\n      {\n        return false;\n      }\n    case 'string':\n      {\n        return key.includes('.') || key.includes('[') || key.includes(']');\n      }\n  }\n}\nexports.isDeepKey = isDeepKey;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "<PERSON><PERSON><PERSON><PERSON>ey", "key", "includes"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/compat/_internal/isDeepKey.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isDeepKey(key) {\n    switch (typeof key) {\n        case 'number':\n        case 'symbol': {\n            return false;\n        }\n        case 'string': {\n            return key.includes('.') || key.includes('[') || key.includes(']');\n        }\n    }\n}\n\nexports.isDeepKey = isDeepKey;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,QAAQ,OAAOA,GAAG;IACd,KAAK,QAAQ;IACb,KAAK,QAAQ;MAAE;QACX,OAAO,KAAK;MAChB;IACA,KAAK,QAAQ;MAAE;QACX,OAAOA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC;MACtE;EACJ;AACJ;AAEAN,OAAO,CAACI,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
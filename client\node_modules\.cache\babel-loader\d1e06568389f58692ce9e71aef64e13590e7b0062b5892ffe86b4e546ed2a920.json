{"ast": null, "code": "function uniqWith(arr, areItemsEqual) {\n  const result = [];\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const isUniq = result.every(v => !areItemsEqual(v, item));\n    if (isUniq) {\n      result.push(item);\n    }\n  }\n  return result;\n}\nexport { uniqWith };", "map": {"version": 3, "names": ["uniqWith", "arr", "areItemsEqual", "result", "i", "length", "item", "isUniq", "every", "v", "push"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/array/uniqWith.mjs"], "sourcesContent": ["function uniqWith(arr, areItemsEqual) {\n    const result = [];\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const isUniq = result.every(v => !areItemsEqual(v, item));\n        if (isUniq) {\n            result.push(item);\n        }\n    }\n    return result;\n}\n\nexport { uniqWith };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,GAAG,EAAEC,aAAa,EAAE;EAClC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,MAAMG,MAAM,GAAGJ,MAAM,CAACK,KAAK,CAACC,CAAC,IAAI,CAACP,aAAa,CAACO,CAAC,EAAEH,IAAI,CAAC,CAAC;IACzD,IAAIC,MAAM,EAAE;MACRJ,MAAM,CAACO,IAAI,CAACJ,IAAI,CAAC;IACrB;EACJ;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
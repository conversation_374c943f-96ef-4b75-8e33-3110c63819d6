{"ast": null, "code": "function sum(nums) {\n  let result = 0;\n  for (let i = 0; i < nums.length; i++) {\n    result += nums[i];\n  }\n  return result;\n}\nexport { sum };", "map": {"version": 3, "names": ["sum", "nums", "result", "i", "length"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/math/sum.mjs"], "sourcesContent": ["function sum(nums) {\n    let result = 0;\n    for (let i = 0; i < nums.length; i++) {\n        result += nums[i];\n    }\n    return result;\n}\n\nexport { sum };\n"], "mappings": "AAAA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACf,IAAIC,MAAM,GAAG,CAAC;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClCD,MAAM,IAAID,IAAI,CAACE,CAAC,CAAC;EACrB;EACA,OAAOD,MAAM;AACjB;AAEA,SAASF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "function trimEnd(str, chars) {\n  if (chars === undefined) {\n    return str.trimEnd();\n  }\n  let endIndex = str.length;\n  switch (typeof chars) {\n    case 'string':\n      {\n        if (chars.length !== 1) {\n          throw new Error(`The 'chars' parameter should be a single character string.`);\n        }\n        while (endIndex > 0 && str[endIndex - 1] === chars) {\n          endIndex--;\n        }\n        break;\n      }\n    case 'object':\n      {\n        while (endIndex > 0 && chars.includes(str[endIndex - 1])) {\n          endIndex--;\n        }\n      }\n  }\n  return str.substring(0, endIndex);\n}\nexport { trimEnd };", "map": {"version": 3, "names": ["trimEnd", "str", "chars", "undefined", "endIndex", "length", "Error", "includes", "substring"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/string/trimEnd.mjs"], "sourcesContent": ["function trimEnd(str, chars) {\n    if (chars === undefined) {\n        return str.trimEnd();\n    }\n    let endIndex = str.length;\n    switch (typeof chars) {\n        case 'string': {\n            if (chars.length !== 1) {\n                throw new Error(`The 'chars' parameter should be a single character string.`);\n            }\n            while (endIndex > 0 && str[endIndex - 1] === chars) {\n                endIndex--;\n            }\n            break;\n        }\n        case 'object': {\n            while (endIndex > 0 && chars.includes(str[endIndex - 1])) {\n                endIndex--;\n            }\n        }\n    }\n    return str.substring(0, endIndex);\n}\n\nexport { trimEnd };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACzB,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACrB,OAAOF,GAAG,CAACD,OAAO,CAAC,CAAC;EACxB;EACA,IAAII,QAAQ,GAAGH,GAAG,CAACI,MAAM;EACzB,QAAQ,OAAOH,KAAK;IAChB,KAAK,QAAQ;MAAE;QACX,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;UACpB,MAAM,IAAIC,KAAK,CAAC,4DAA4D,CAAC;QACjF;QACA,OAAOF,QAAQ,GAAG,CAAC,IAAIH,GAAG,CAACG,QAAQ,GAAG,CAAC,CAAC,KAAKF,KAAK,EAAE;UAChDE,QAAQ,EAAE;QACd;QACA;MACJ;IACA,KAAK,QAAQ;MAAE;QACX,OAAOA,QAAQ,GAAG,CAAC,IAAIF,KAAK,CAACK,QAAQ,CAACN,GAAG,CAACG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE;UACtDA,QAAQ,EAAE;QACd;MACJ;EACJ;EACA,OAAOH,GAAG,CAACO,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC;AACrC;AAEA,SAASJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
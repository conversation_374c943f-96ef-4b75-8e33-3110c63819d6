{"ast": null, "code": "import { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\nfunction clone(obj) {\n  if (isPrimitive(obj)) {\n    return obj;\n  }\n  if (Array.isArray(obj) || isTypedArray(obj) || obj instanceof ArrayBuffer || typeof SharedArrayBuffer !== 'undefined' && obj instanceof SharedArrayBuffer) {\n    return obj.slice(0);\n  }\n  const prototype = Object.getPrototypeOf(obj);\n  const Constructor = prototype.constructor;\n  if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {\n    return new Constructor(obj);\n  }\n  if (obj instanceof RegExp) {\n    const newRegExp = new Constructor(obj);\n    newRegExp.lastIndex = obj.lastIndex;\n    return newRegExp;\n  }\n  if (obj instanceof DataView) {\n    return new Constructor(obj.buffer.slice(0));\n  }\n  if (obj instanceof Error) {\n    const newError = new Constructor(obj.message);\n    newError.stack = obj.stack;\n    newError.name = obj.name;\n    newError.cause = obj.cause;\n    return newError;\n  }\n  if (typeof File !== 'undefined' && obj instanceof File) {\n    const newFile = new Constructor([obj], obj.name, {\n      type: obj.type,\n      lastModified: obj.lastModified\n    });\n    return newFile;\n  }\n  if (typeof obj === 'object') {\n    const newObject = Object.create(prototype);\n    return Object.assign(newObject, obj);\n  }\n  return obj;\n}\nexport { clone };", "map": {"version": 3, "names": ["isPrimitive", "isTypedArray", "clone", "obj", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedArrayBuffer", "slice", "prototype", "Object", "getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "Date", "Map", "Set", "RegExp", "newRegExp", "lastIndex", "DataView", "buffer", "Error", "newError", "message", "stack", "name", "cause", "File", "newFile", "type", "lastModified", "newObject", "create", "assign"], "sources": ["C:/vibe coding/screentime_management_app/client/node_modules/es-toolkit/dist/object/clone.mjs"], "sourcesContent": ["import { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction clone(obj) {\n    if (isPrimitive(obj)) {\n        return obj;\n    }\n    if (Array.isArray(obj) ||\n        isTypedArray(obj) ||\n        obj instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && obj instanceof SharedArrayBuffer)) {\n        return obj.slice(0);\n    }\n    const prototype = Object.getPrototypeOf(obj);\n    const Constructor = prototype.constructor;\n    if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {\n        return new Constructor(obj);\n    }\n    if (obj instanceof RegExp) {\n        const newRegExp = new Constructor(obj);\n        newRegExp.lastIndex = obj.lastIndex;\n        return newRegExp;\n    }\n    if (obj instanceof DataView) {\n        return new Constructor(obj.buffer.slice(0));\n    }\n    if (obj instanceof Error) {\n        const newError = new Constructor(obj.message);\n        newError.stack = obj.stack;\n        newError.name = obj.name;\n        newError.cause = obj.cause;\n        return newError;\n    }\n    if (typeof File !== 'undefined' && obj instanceof File) {\n        const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });\n        return newFile;\n    }\n    if (typeof obj === 'object') {\n        const newObject = Object.create(prototype);\n        return Object.assign(newObject, obj);\n    }\n    return obj;\n}\n\nexport { clone };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,+BAA+B;AAE5D,SAASC,KAAKA,CAACC,GAAG,EAAE;EAChB,IAAIH,WAAW,CAACG,GAAG,CAAC,EAAE;IAClB,OAAOA,GAAG;EACd;EACA,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAClBF,YAAY,CAACE,GAAG,CAAC,IACjBA,GAAG,YAAYG,WAAW,IACzB,OAAOC,iBAAiB,KAAK,WAAW,IAAIJ,GAAG,YAAYI,iBAAkB,EAAE;IAChF,OAAOJ,GAAG,CAACK,KAAK,CAAC,CAAC,CAAC;EACvB;EACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACR,GAAG,CAAC;EAC5C,MAAMS,WAAW,GAAGH,SAAS,CAACI,WAAW;EACzC,IAAIV,GAAG,YAAYW,IAAI,IAAIX,GAAG,YAAYY,GAAG,IAAIZ,GAAG,YAAYa,GAAG,EAAE;IACjE,OAAO,IAAIJ,WAAW,CAACT,GAAG,CAAC;EAC/B;EACA,IAAIA,GAAG,YAAYc,MAAM,EAAE;IACvB,MAAMC,SAAS,GAAG,IAAIN,WAAW,CAACT,GAAG,CAAC;IACtCe,SAAS,CAACC,SAAS,GAAGhB,GAAG,CAACgB,SAAS;IACnC,OAAOD,SAAS;EACpB;EACA,IAAIf,GAAG,YAAYiB,QAAQ,EAAE;IACzB,OAAO,IAAIR,WAAW,CAACT,GAAG,CAACkB,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA,IAAIL,GAAG,YAAYmB,KAAK,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAIX,WAAW,CAACT,GAAG,CAACqB,OAAO,CAAC;IAC7CD,QAAQ,CAACE,KAAK,GAAGtB,GAAG,CAACsB,KAAK;IAC1BF,QAAQ,CAACG,IAAI,GAAGvB,GAAG,CAACuB,IAAI;IACxBH,QAAQ,CAACI,KAAK,GAAGxB,GAAG,CAACwB,KAAK;IAC1B,OAAOJ,QAAQ;EACnB;EACA,IAAI,OAAOK,IAAI,KAAK,WAAW,IAAIzB,GAAG,YAAYyB,IAAI,EAAE;IACpD,MAAMC,OAAO,GAAG,IAAIjB,WAAW,CAAC,CAACT,GAAG,CAAC,EAAEA,GAAG,CAACuB,IAAI,EAAE;MAAEI,IAAI,EAAE3B,GAAG,CAAC2B,IAAI;MAAEC,YAAY,EAAE5B,GAAG,CAAC4B;IAAa,CAAC,CAAC;IACpG,OAAOF,OAAO;EAClB;EACA,IAAI,OAAO1B,GAAG,KAAK,QAAQ,EAAE;IACzB,MAAM6B,SAAS,GAAGtB,MAAM,CAACuB,MAAM,CAACxB,SAAS,CAAC;IAC1C,OAAOC,MAAM,CAACwB,MAAM,CAACF,SAAS,EAAE7B,GAAG,CAAC;EACxC;EACA,OAAOA,GAAG;AACd;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}